{"ast": null, "code": "import React from'react';import'./App.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsxs(\"header\",{className:\"App-header\",children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Clipsy Windows\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Professional clipboard management and sync application\"})]})});}export default App;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "App", "className", "children"], "sources": ["D:/new git/Clipsy-Windows/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  return (\r\n    <div className=\"App\">\r\n      <header className=\"App-header\">\r\n        <h1>Clipsy Windows</h1>\r\n        <p>Professional clipboard management and sync application</p>\r\n      </header>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,QAAKI,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBH,KAAA,WAAQE,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC5BL,IAAA,OAAAK,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBL,IAAA,MAAAK,QAAA,CAAG,wDAAsD,CAAG,CAAC,EACvD,CAAC,CACN,CAAC,CAEV,CAEA,cAAe,CAAAF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}