{"name": "@electron/packager", "version": "18.3.6", "description": "Customize and package your Electron app with OS-specific bundles (.app, .exe, etc.) via JS or CLI", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"electron-packager": "bin/electron-packager.js"}, "repository": {"type": "git", "url": "git+https://github.com/electron/packager.git"}, "author": "<PERSON>", "license": "BSD-2-<PERSON><PERSON>", "bugs": {"url": "https://github.com/electron/packager/issues"}, "homepage": "https://github.com/electron/packager", "files": ["collaborators.md", "bin", "docs/*.md", "dist", "src/targets.js", "usage.txt"], "dependencies": {"@electron/asar": "^3.2.13", "@electron/get": "^3.0.0", "@electron/notarize": "^2.1.0", "@electron/osx-sign": "^1.0.5", "@electron/universal": "^2.0.1", "@electron/windows-sign": "^1.0.0", "debug": "^4.0.1", "extract-zip": "^2.0.0", "filenamify": "^4.1.0", "fs-extra": "^11.1.0", "galactus": "^1.0.0", "get-package-info": "^1.0.0", "junk": "^3.1.0", "parse-author": "^2.0.0", "plist": "^3.0.0", "resedit": "^2.0.0", "resolve": "^1.1.6", "semver": "^7.1.3", "yargs-parser": "^21.1.1"}, "devDependencies": {"@types/debug": "^4.1.10", "@types/fs-extra": "^11.0.3", "@types/parse-author": "^2.0.2", "@types/plist": "^3.0.4", "@types/resolve": "^1.20.4", "@types/semver": "^7.5.8", "@types/yargs-parser": "^21.0.2", "@typescript-eslint/eslint-plugin": "7.2.0", "@typescript-eslint/parser": "^6.9.1", "ava": "^3.10.1", "buffer-equal": "^1.0.0", "codecov": "^3.0.0", "copyfiles": "^2.4.1", "eslint": "^8.52.0", "eslint-plugin-ava": "^14.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^6.2.0", "lodash": "^4.17.21", "nyc": "^15.0.0", "pkg-up": "^4.0.0", "sinon": "^18.0.0", "typedoc": "^0.25.4", "typescript": "^5.2.2"}, "engines": {"node": ">= 16.13.0"}, "scripts": {"ava": "ava test/index.js", "build": "tsc", "postbuild": "copyfiles -f ./temp/targets.js ./src", "codecov": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "coverage": "nyc ava test/index.js", "docs:build": "node test/ci/build_docs.js", "lint": "eslint .", "prepublish": "npm run build", "pretest": "npm run build", "test": "npm run lint && npm run coverage"}, "directories": {"test": "test"}, "keywords": ["electron", "packager", "bundler", "app"], "ava": {"timeout": "120s"}, "funding": {"url": "https://github.com/electron/packager?sponsor=1"}, "resolutions": {"got": "11.8.5"}}