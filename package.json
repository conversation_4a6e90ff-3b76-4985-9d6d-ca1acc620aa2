{"name": "clipsy-windows", "version": "1.0.0", "description": "Clipsy Windows Application", "main": "electron.js", "homepage": "./", "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-qr-code": "^2.0.16"}, "devDependencies": {"@electron/packager": "^18.3.6", "concurrently": "^9.2.0", "electron": "^37.1.0", "electron-builder": "^26.0.12", "png-to-ico": "^2.1.8", "react-scripts": "^5.0.1", "wait-on": "^8.0.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "build-win": "npm run build && electron-builder --win", "build-win-portable": "npm run build && electron-builder --win portable", "build-installer": "npm run build && electron-builder --win nsis", "pack": "electron-builder --dir", "package-win": "npm run build && npx @electron/packager . Clipsy --platform=win32 --arch=x64 --out=dist-packager --overwrite --icon=public/clipsy-logo-no-bg.ico", "create-installer": "node build-installer.js"}, "keywords": [], "author": "", "license": "ISC", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.clipsy.windows", "productName": "<PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["build/**/*", "!build/electron.js", "electron.js", "preload.js"], "extraMetadata": {"main": "electron.js"}, "win": {"target": "nsis", "icon": "public/clipsy-logo-no-bg.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "<PERSON><PERSON><PERSON>"}, "portable": {"artifactName": "${productName}-${version}-portable-${arch}.${ext}"}}}