{"ast": null, "code": "var _jsxFileName = \"D:\\\\new git\\\\Clipsy-Windows\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  // State management matching Android app\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to Clipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\n  const [historyItems, setHistoryItems] = useState([{\n    id: '1',\n    content: 'This is an older clipboard item. It\\'s shorter.',\n    timestamp: '2 minutes ago'\n  }, {\n    id: '2',\n    content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.',\n    timestamp: '10 minutes ago'\n  }, {\n    id: '3',\n    content: 'Yet another historical entry.',\n    timestamp: '1 hour ago'\n  }, {\n    id: '4',\n    content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }',\n    timestamp: '5 hours ago'\n  }]);\n\n  // UI State\n  const [showSettings, setShowSettings] = useState(false);\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [connectionStatus, setConnectionStatus] = useState('connected');\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\n\n  // Background Sync State\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\n  const [networkDevices, setNetworkDevices] = useState([]);\n  const [isDiscovering, setIsDiscovering] = useState(false);\n\n  // Sync Settings - matching Android app\n  const [syncSettings, setSyncSettings] = useState({\n    autoSync: true,\n    syncDelay: 2,\n    syncOnConnect: true,\n    bidirectional: true\n  });\n\n  // Device Management - matching Android app\n  const [pairedDevices, setPairedDevices] = useState([{\n    id: 'android-1',\n    name: 'Android Phone - Personal',\n    type: 'Android 14',\n    status: 'connected',\n    lastSeen: '2 min ago',\n    ipAddress: '*************'\n  }, {\n    id: 'linux-1',\n    name: 'Ubuntu Server - Home',\n    type: 'Ubuntu 22.04',\n    status: 'disconnected',\n    lastSeen: '1 hour ago',\n    ipAddress: '*************'\n  }]);\n  const [discoveredDevices] = useState([{\n    id: 'johns-laptop',\n    name: 'John\\'s Laptop',\n    type: 'Windows 10',\n    status: 'discovering',\n    lastSeen: 'Available for pairing',\n    ipAddress: '*************'\n  }, {\n    id: 'sarahs-desktop',\n    name: 'Sarah\\'s Desktop',\n    type: 'Ubuntu 22.04',\n    status: 'discovering',\n    lastSeen: 'Available for pairing',\n    ipAddress: '*************'\n  }]);\n\n  // Functions\n  const showMessage = text => {\n    setSuccessMessage(text);\n    setTimeout(() => setSuccessMessage(''), 3000);\n  };\n  const copyToClipboard = async content => {\n    try {\n      await navigator.clipboard.writeText(content);\n      setThisDeviceClipboard(content);\n      showMessage('✅ Text copied to clipboard!');\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error);\n      showMessage('❌ Failed to copy to clipboard');\n    }\n  };\n  const selectItem = item => {\n    copyToClipboard(item.content);\n    setConnectedDeviceClipboard(item.content);\n  };\n  const deleteHistoryItem = itemId => {\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\n    setHistoryItems(updatedHistory);\n    showMessage('🗑️ History item deleted!');\n  };\n\n  // Device edit functions\n  const startEditingThisDevice = () => {\n    setEditingThisDeviceText(thisDeviceClipboard);\n    setIsEditingThisDevice(true);\n  };\n  const saveThisDeviceEdit = async () => {\n    const newContent = editingThisDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setThisDeviceClipboard(newContent);\n    try {\n      await navigator.clipboard.writeText(newContent);\n    } catch (error) {\n      console.warn('Failed to update clipboard:', error);\n    }\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n    showMessage('✅ This Device clipboard updated!');\n  };\n  const cancelThisDeviceEdit = () => {\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n  };\n  const startEditingConnectedDevice = () => {\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\n    setIsEditingConnectedDevice(true);\n  };\n  const saveConnectedDeviceEdit = () => {\n    const newContent = editingConnectedDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setConnectedDeviceClipboard(newContent);\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n    showMessage('✅ Connected Device clipboard updated!');\n  };\n  const cancelConnectedDeviceEdit = () => {\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n  };\n  const syncNow = () => {\n    showMessage('🔄 Syncing with paired devices...');\n    setTimeout(() => {\n      showMessage('✅ Sync completed!');\n    }, 1000);\n  };\n  const toggleAlwaysOnTop = () => {\n    setIsAlwaysOnTop(!isAlwaysOnTop);\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\n  };\n  const minimizeToTray = () => {\n    showMessage('➖ Minimizing to background...');\n  };\n  const toggleFloatingOverlay = () => {\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\n  };\n\n  // Device Management Functions\n  const removeDevice = deviceId => {\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\n    setPairedDevices(updatedDevices);\n    showMessage('🗑️ Device removed from paired devices');\n  };\n  const connectDevice = deviceId => {\n    const updatedDevices = pairedDevices.map(device => device.id === deviceId ? {\n      ...device,\n      status: 'connected',\n      lastSeen: 'Just now'\n    } : device);\n    setPairedDevices(updatedDevices);\n    showMessage('✅ Device connected successfully');\n  };\n  const disconnectDevice = deviceId => {\n    const updatedDevices = pairedDevices.map(device => device.id === deviceId ? {\n      ...device,\n      status: 'disconnected',\n      lastSeen: 'Just now'\n    } : device);\n    setPairedDevices(updatedDevices);\n    showMessage('🔌 Device disconnected');\n  };\n  const pairDevice = deviceId => {\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\n    if (deviceToPair) {\n      const newPairedDevice = {\n        ...deviceToPair,\n        status: 'connected',\n        lastSeen: 'Just now'\n      };\n      setPairedDevices([...pairedDevices, newPairedDevice]);\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\n    }\n  };\n  const refreshDiscovery = () => {\n    if (isDiscovering) return;\n    setIsDiscovering(true);\n    showMessage('🔍 Scanning for devices...');\n\n    // Simulate discovery process\n    setTimeout(() => {\n      setNetworkDevices(discoveredDevices);\n      setIsDiscovering(false);\n      showMessage(`📱 Found ${discoveredDevices.length} available devices`);\n    }, 2000);\n  };\n  const scanQRCode = () => {\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\n  };\n  const generateQRCode = () => {\n    showMessage('📋 QR Code generation not available in web version. Use device discovery instead.');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"title-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/clipsy-logo-no-bg.png\",\n            alt: \"Clipsy Logo\",\n            className: \"app-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: \"Clipsy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isFloatingOverlayVisible ? 'active' : ''}`,\n          onClick: toggleFloatingOverlay,\n          title: \"Toggle floating clipboard widget\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isAlwaysOnTop ? 'active' : ''}`,\n          onClick: toggleAlwaysOnTop,\n          title: \"Pin to top\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pin-icon\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-head\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-body\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: minimizeToTray,\n          title: \"Minimize\",\n          children: \"\\u2796\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: () => setShowSettings(true),\n          title: \"Settings\",\n          children: \"\\u2699\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"device-section-title-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"device-section-title\",\n              children: \"\\uD83D\\uDCBB This Device\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"device-section-subtitle\",\n              children: \"Windows PC - Office\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), isEditingThisDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingThisDeviceText,\n            onChange: e => setEditingThisDeviceText(e.target.value),\n            placeholder: \"Edit this device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button this-device\",\n              onClick: saveThisDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button this-device\",\n              onClick: cancelThisDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content this-device-content\",\n            onClick: () => copyToClipboard(thisDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: thisDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Real-time sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingThisDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"device-section-title\",\n            children: \"\\uD83D\\uDD17 Connected Device\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"device-section-subtitle\",\n            children: \"Android Phone - Personal \\uD83D\\uDFE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), isEditingConnectedDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingConnectedDeviceText,\n            onChange: e => setEditingConnectedDeviceText(e.target.value),\n            placeholder: \"Edit connected device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button connected-device\",\n              onClick: saveConnectedDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button connected-device\",\n              onClick: cancelConnectedDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content connected-device-content\",\n            onClick: () => copyToClipboard(connectedDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: connectedDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Bidirectional sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingConnectedDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"history-title\",\n        children: \"Clipboard History\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-list\",\n        children: historyItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-item\",\n          onClick: () => selectItem(item),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-item-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"timestamp\",\n              children: item.timestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"delete-button\",\n              onClick: e => {\n                e.stopPropagation();\n                deleteHistoryItem(item.id);\n              },\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"item-content\",\n            children: item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"floating-sync-button\",\n      onClick: syncNow,\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/sync.png\",\n        alt: \"Sync\",\n        className: \"sync-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 7\n    }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowSettings(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-sidebar\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-title\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: () => setShowSettings(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Device Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"device-info-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-name\",\n                children: \"Windows PC - Office\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail\",\n                children: \"IP: *************\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-status-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `device-status-indicator ${connectionStatus}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `device-status-text ${connectionStatus}`,\n                  children: [\"Status: \", connectionStatus === 'connected' ? 'Connected' : 'Disconnected']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Paired Devices\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this), pairedDevices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-device-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-text\",\n                children: \"No paired devices found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-subtext\",\n                children: \"Use QR code or device discovery to pair devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 19\n            }, this) : pairedDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"enhanced-device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"remove-button-top-right\",\n                onClick: () => removeDevice(device.id),\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"device-status-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `device-status-indicator ${device.status}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `device-status-text ${device.status}`,\n                    children: [device.status === 'connected' ? 'Connected' : 'Disconnected', \" \\u2022 \", device.lastSeen]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`,\n                onClick: () => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id),\n                children: device.status === 'connected' ? 'Disconnect' : 'Connect'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 23\n              }, this)]\n            }, device.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 21\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"section-title\",\n                children: \"Device Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`,\n                onClick: refreshDiscovery,\n                disabled: isDiscovering,\n                children: isDiscovering ? '🔍 Scanning...' : 'Discover'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this), networkDevices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-device-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-text\",\n                children: \"No devices found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-subtext\",\n                children: isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 19\n            }, this) : networkDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"discovered-device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-last-seen\",\n                  children: device.lastSeen\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"pair-button\",\n                onClick: () => pairDevice(device.id),\n                children: \"Pair\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 23\n              }, this)]\n            }, device.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 21\n            }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qr-buttons-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"qr-scan-button\",\n                onClick: scanQRCode,\n                children: \"Scan QR\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"qr-generate-button\",\n                onClick: generateQRCode,\n                children: \"Generate QR\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Additional Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => setShowSyncSettings(true),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Sync Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-arrow\",\n                children: \"\\u203A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => {\n                const newValue = !backgroundSyncEnabled;\n                setBackgroundSyncEnabled(newValue);\n                showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Background Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-value\",\n                children: backgroundSyncEnabled ? 'on' : 'off'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => {\n                if (isDiscovering) {\n                  setIsDiscovering(false);\n                  showMessage('Network discovery stopped');\n                } else {\n                  setIsDiscovering(true);\n                  showMessage('Network discovery started');\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Network Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-value\",\n                children: isDiscovering ? 'on' : 'off'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 241,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"OTlvp1MdeVJzKrqKsWy7LnHSgKM=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "App", "_s", "thisDeviceClipboard", "setThisDeviceClipboard", "connectedDeviceClipboard", "setConnectedDeviceClipboard", "isEditingThisDevice", "setIsEditingThisDevice", "isEditingConnectedDevice", "setIsEditingConnectedDevice", "editingThisDeviceText", "setEditingThisDeviceText", "editingConnectedDeviceText", "setEditingConnectedDeviceText", "historyItems", "setHistoryItems", "id", "content", "timestamp", "showSettings", "setShowSettings", "showSyncSettings", "setShowSyncSettings", "isAlwaysOnTop", "setIsAlwaysOnTop", "successMessage", "setSuccessMessage", "connectionStatus", "setConnectionStatus", "isFloatingOverlayVisible", "setIsFloatingOverlayVisible", "backgroundSyncEnabled", "setBackgroundSyncEnabled", "networkDevices", "setNetworkDevices", "isDiscovering", "setIsDiscovering", "syncSettings", "setSyncSettings", "autoSync", "syncD<PERSON>y", "syncOnConnect", "bidirectional", "pairedDevices", "setPairedDevices", "name", "type", "status", "lastSeen", "ip<PERSON><PERSON><PERSON>", "discoveredDevices", "showMessage", "text", "setTimeout", "copyToClipboard", "navigator", "clipboard", "writeText", "error", "console", "selectItem", "item", "deleteHistoryItem", "itemId", "updatedHistory", "filter", "startEditingThisDevice", "saveThisDeviceEdit", "newContent", "trim", "warn", "cancelThisDeviceEdit", "startEditingConnectedDevice", "saveConnectedDeviceEdit", "cancelConnectedDeviceEdit", "syncNow", "toggleAlwaysOnTop", "minimizeToTray", "toggleFloatingOverlay", "removeDevice", "deviceId", "updatedDevices", "device", "connectDevice", "map", "disconnectDevice", "pairDevice", "deviceToPair", "find", "newPairedDevice", "refreshDiscovery", "length", "scanQRCode", "generateQRCode", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "some", "onClick", "title", "value", "onChange", "e", "target", "placeholder", "autoFocus", "stopPropagation", "disabled", "newValue", "_c", "$RefreshReg$"], "sources": ["D:/new git/Clipsy-Windows/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  // State management matching Android app\r\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to <PERSON>lipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\r\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\r\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\r\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\r\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\r\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\r\n\r\n  const [historyItems, setHistoryItems] = useState([\r\n    { id: '1', content: 'This is an older clipboard item. It\\'s shorter.', timestamp: '2 minutes ago' },\r\n    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },\r\n    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },\r\n    { id: '4', content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }', timestamp: '5 hours ago' }\r\n  ]);\r\n\r\n  // UI State\r\n  const [showSettings, setShowSettings] = useState(false);\r\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\r\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [connectionStatus, setConnectionStatus] = useState('connected');\r\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\r\n\r\n  // Background Sync State\r\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\r\n  const [networkDevices, setNetworkDevices] = useState([]);\r\n  const [isDiscovering, setIsDiscovering] = useState(false);\r\n\r\n  // Sync Settings - matching Android app\r\n  const [syncSettings, setSyncSettings] = useState({\r\n    autoSync: true,\r\n    syncDelay: 2,\r\n    syncOnConnect: true,\r\n    bidirectional: true\r\n  });\r\n\r\n  // Device Management - matching Android app\r\n  const [pairedDevices, setPairedDevices] = useState([\r\n    {\r\n      id: 'android-1',\r\n      name: 'Android Phone - Personal',\r\n      type: 'Android 14',\r\n      status: 'connected',\r\n      lastSeen: '2 min ago',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'linux-1',\r\n      name: 'Ubuntu Server - Home',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'disconnected',\r\n      lastSeen: '1 hour ago',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  const [discoveredDevices] = useState([\r\n    {\r\n      id: 'johns-laptop',\r\n      name: 'John\\'s Laptop',\r\n      type: 'Windows 10',\r\n      status: 'discovering',\r\n      lastSeen: 'Available for pairing',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'sarahs-desktop',\r\n      name: 'Sarah\\'s Desktop',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'discovering',\r\n      lastSeen: 'Available for pairing',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  // Functions\r\n  const showMessage = (text) => {\r\n    setSuccessMessage(text);\r\n    setTimeout(() => setSuccessMessage(''), 3000);\r\n  };\r\n\r\n  const copyToClipboard = async (content) => {\r\n    try {\r\n      await navigator.clipboard.writeText(content);\r\n      setThisDeviceClipboard(content);\r\n      showMessage('✅ Text copied to clipboard!');\r\n    } catch (error) {\r\n      console.error('Failed to copy to clipboard:', error);\r\n      showMessage('❌ Failed to copy to clipboard');\r\n    }\r\n  };\r\n\r\n  const selectItem = (item) => {\r\n    copyToClipboard(item.content);\r\n    setConnectedDeviceClipboard(item.content);\r\n  };\r\n\r\n  const deleteHistoryItem = (itemId) => {\r\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\r\n    setHistoryItems(updatedHistory);\r\n    showMessage('🗑️ History item deleted!');\r\n  };\r\n\r\n  // Device edit functions\r\n  const startEditingThisDevice = () => {\r\n    setEditingThisDeviceText(thisDeviceClipboard);\r\n    setIsEditingThisDevice(true);\r\n  };\r\n\r\n  const saveThisDeviceEdit = async () => {\r\n    const newContent = editingThisDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setThisDeviceClipboard(newContent);\r\n    try {\r\n      await navigator.clipboard.writeText(newContent);\r\n    } catch (error) {\r\n      console.warn('Failed to update clipboard:', error);\r\n    }\r\n\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n    showMessage('✅ This Device clipboard updated!');\r\n  };\r\n\r\n  const cancelThisDeviceEdit = () => {\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n  };\r\n\r\n  const startEditingConnectedDevice = () => {\r\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\r\n    setIsEditingConnectedDevice(true);\r\n  };\r\n\r\n  const saveConnectedDeviceEdit = () => {\r\n    const newContent = editingConnectedDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setConnectedDeviceClipboard(newContent);\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n    showMessage('✅ Connected Device clipboard updated!');\r\n  };\r\n\r\n  const cancelConnectedDeviceEdit = () => {\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n  };\r\n\r\n  const syncNow = () => {\r\n    showMessage('🔄 Syncing with paired devices...');\r\n    setTimeout(() => {\r\n      showMessage('✅ Sync completed!');\r\n    }, 1000);\r\n  };\r\n\r\n  const toggleAlwaysOnTop = () => {\r\n    setIsAlwaysOnTop(!isAlwaysOnTop);\r\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\r\n  };\r\n\r\n  const minimizeToTray = () => {\r\n    showMessage('➖ Minimizing to background...');\r\n  };\r\n\r\n  const toggleFloatingOverlay = () => {\r\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\r\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\r\n  };\r\n\r\n  // Device Management Functions\r\n  const removeDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🗑️ Device removed from paired devices');\r\n  };\r\n\r\n  const connectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'connected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('✅ Device connected successfully');\r\n  };\r\n\r\n  const disconnectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'disconnected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🔌 Device disconnected');\r\n  };\r\n\r\n  const pairDevice = (deviceId) => {\r\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\r\n    if (deviceToPair) {\r\n      const newPairedDevice = {\r\n        ...deviceToPair,\r\n        status: 'connected',\r\n        lastSeen: 'Just now'\r\n      };\r\n      setPairedDevices([...pairedDevices, newPairedDevice]);\r\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\r\n    }\r\n  };\r\n\r\n  const refreshDiscovery = () => {\r\n    if (isDiscovering) return;\r\n\r\n    setIsDiscovering(true);\r\n    showMessage('🔍 Scanning for devices...');\r\n\r\n    // Simulate discovery process\r\n    setTimeout(() => {\r\n      setNetworkDevices(discoveredDevices);\r\n      setIsDiscovering(false);\r\n      showMessage(`📱 Found ${discoveredDevices.length} available devices`);\r\n    }, 2000);\r\n  };\r\n\r\n  const scanQRCode = () => {\r\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\r\n  };\r\n\r\n  const generateQRCode = () => {\r\n    showMessage('📋 QR Code generation not available in web version. Use device discovery instead.');\r\n  };\r\n\r\n  return (\r\n    <div className=\"app-container\">\r\n      {/* Header */}\r\n      <div className=\"header\">\r\n        <div className=\"title-container\">\r\n          <div className=\"logo-container\">\r\n            <img\r\n              src=\"/clipsy-logo-no-bg.png\"\r\n              alt=\"Clipsy Logo\"\r\n              className=\"app-logo\"\r\n            />\r\n            <div className={`connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`}></div>\r\n          </div>\r\n          <h1 className=\"title\">Clipsy</h1>\r\n        </div>\r\n        <div className=\"header-actions\">\r\n          <button\r\n            className={`icon-button ${isFloatingOverlayVisible ? 'active' : ''}`}\r\n            onClick={toggleFloatingOverlay}\r\n            title=\"Toggle floating clipboard widget\"\r\n          >\r\n            📋\r\n          </button>\r\n          <button\r\n            className={`icon-button ${isAlwaysOnTop ? 'active' : ''}`}\r\n            onClick={toggleAlwaysOnTop}\r\n            title=\"Pin to top\"\r\n          >\r\n            <div className=\"pin-icon\">\r\n              <div className=\"pin-head\"></div>\r\n              <div className=\"pin-body\"></div>\r\n            </div>\r\n          </button>\r\n          <button className=\"icon-button\" onClick={minimizeToTray} title=\"Minimize\">\r\n            ➖\r\n          </button>\r\n          <button className=\"icon-button\" onClick={() => setShowSettings(true)} title=\"Settings\">\r\n            ⚙️\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"success-message\">\r\n          <span>{successMessage}</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      <div className=\"main-content\">\r\n        {/* This Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <div className=\"device-section-title-container\">\r\n              <h2 className=\"device-section-title\">💻 This Device</h2>\r\n              <p className=\"device-section-subtitle\">Windows PC - Office</p>\r\n            </div>\r\n          </div>\r\n\r\n          {isEditingThisDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingThisDeviceText}\r\n                onChange={(e) => setEditingThisDeviceText(e.target.value)}\r\n                placeholder=\"Edit this device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button this-device\" onClick={saveThisDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button this-device\" onClick={cancelThisDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content this-device-content\"\r\n                onClick={() => copyToClipboard(thisDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{thisDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Real-time sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingThisDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Connected Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <h2 className=\"device-section-title\">🔗 Connected Device</h2>\r\n            <p className=\"device-section-subtitle\">Android Phone - Personal 🟢</p>\r\n          </div>\r\n\r\n          {isEditingConnectedDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingConnectedDeviceText}\r\n                onChange={(e) => setEditingConnectedDeviceText(e.target.value)}\r\n                placeholder=\"Edit connected device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button connected-device\" onClick={saveConnectedDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button connected-device\" onClick={cancelConnectedDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content connected-device-content\"\r\n                onClick={() => copyToClipboard(connectedDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{connectedDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Bidirectional sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingConnectedDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Clipboard History */}\r\n        <h2 className=\"history-title\">Clipboard History</h2>\r\n        <div className=\"history-list\">\r\n          {historyItems.map((item) => (\r\n            <div\r\n              key={item.id}\r\n              className=\"history-item\"\r\n              onClick={() => selectItem(item)}\r\n            >\r\n              <div className=\"history-item-header\">\r\n                <span className=\"timestamp\">{item.timestamp}</span>\r\n                <button\r\n                  className=\"delete-button\"\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    deleteHistoryItem(item.id);\r\n                  }}\r\n                >\r\n                  ✕\r\n                </button>\r\n              </div>\r\n              <p className=\"item-content\">{item.content}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Floating Sync Button */}\r\n      <button className=\"floating-sync-button\" onClick={syncNow}>\r\n        <img src=\"/sync.png\" alt=\"Sync\" className=\"sync-icon\" />\r\n      </button>\r\n\r\n      {/* Settings Modal */}\r\n      {showSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSettings(false)}>\r\n          <div className=\"settings-sidebar\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              {/* Device Info */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Device Info</h3>\r\n                <div className=\"device-info-card\">\r\n                  <p className=\"device-name\">Windows PC - Office</p>\r\n                  <p className=\"device-detail\">IP: *************</p>\r\n                  <div className=\"device-status-row\">\r\n                    <div className={`device-status-indicator ${connectionStatus}`}></div>\r\n                    <span className={`device-status-text ${connectionStatus}`}>\r\n                      Status: {connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Paired Devices */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Paired Devices</h3>\r\n                {pairedDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No paired devices found</p>\r\n                    <p className=\"empty-device-subtext\">Use QR code or device discovery to pair devices</p>\r\n                  </div>\r\n                ) : (\r\n                  pairedDevices.map((device) => (\r\n                    <div key={device.id} className=\"enhanced-device-card\">\r\n                      {/* Remove Button - White X at top right corner */}\r\n                      <button\r\n                        className=\"remove-button-top-right\"\r\n                        onClick={() => removeDevice(device.id)}\r\n                      >\r\n                        ×\r\n                      </button>\r\n\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <div className=\"device-status-row\">\r\n                          <div className={`device-status-indicator ${device.status}`}></div>\r\n                          <span className={`device-status-text ${device.status}`}>\r\n                            {device.status === 'connected' ? 'Connected' : 'Disconnected'} • {device.lastSeen}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Connect/Disconnect Button - Bottom Right Corner */}\r\n                      <button\r\n                        className={`device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`}\r\n                        onClick={() => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id)}\r\n                      >\r\n                        {device.status === 'connected' ? 'Disconnect' : 'Connect'}\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n              </div>\r\n\r\n              {/* Device Discovery */}\r\n              <div className=\"settings-section\">\r\n                <div className=\"section-header\">\r\n                  <h3 className=\"section-title\">Device Discovery</h3>\r\n                  <button\r\n                    className={`discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`}\r\n                    onClick={refreshDiscovery}\r\n                    disabled={isDiscovering}\r\n                  >\r\n                    {isDiscovering ? '🔍 Scanning...' : 'Discover'}\r\n                  </button>\r\n                </div>\r\n                {networkDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No devices found</p>\r\n                    <p className=\"empty-device-subtext\">\r\n                      {isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'}\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  networkDevices.map((device) => (\r\n                    <div key={device.id} className=\"discovered-device-card\">\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <p className=\"device-card-last-seen\">{device.lastSeen}</p>\r\n                      </div>\r\n                      <button\r\n                        className=\"pair-button\"\r\n                        onClick={() => pairDevice(device.id)}\r\n                      >\r\n                        Pair\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n\r\n                <div className=\"qr-buttons-container\">\r\n                  <button className=\"qr-scan-button\" onClick={scanQRCode}>\r\n                    Scan QR\r\n                  </button>\r\n                  <button className=\"qr-generate-button\" onClick={generateQRCode}>\r\n                    Generate QR\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Additional Settings */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Additional Settings</h3>\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => setShowSyncSettings(true)}\r\n                >\r\n                  <span className=\"setting-item-text\">Sync Settings</span>\r\n                  <span className=\"setting-item-arrow\">›</span>\r\n                </button>\r\n\r\n                {/* Background Sync Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    const newValue = !backgroundSyncEnabled;\r\n                    setBackgroundSyncEnabled(newValue);\r\n                    showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Background Sync</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {backgroundSyncEnabled ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n\r\n                {/* Network Discovery Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    if (isDiscovering) {\r\n                      setIsDiscovering(false);\r\n                      showMessage('Network discovery stopped');\r\n                    } else {\r\n                      setIsDiscovering(true);\r\n                      showMessage('Network discovery started');\r\n                    }\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Network Discovery</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {isDiscovering ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGP,QAAQ,CAAC,sIAAsI,CAAC;EACtM,MAAM,CAACQ,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGT,QAAQ,CAAC,0IAA0I,CAAC;EACpN,MAAM,CAACU,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACY,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACc,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACgB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEhF,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAC/C;IAAEoB,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,iDAAiD;IAAEC,SAAS,EAAE;EAAgB,CAAC,EACnG;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,gLAAgL;IAAEC,SAAS,EAAE;EAAiB,CAAC,EACnO;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,+BAA+B;IAAEC,SAAS,EAAE;EAAa,CAAC,EAC9E;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,sEAAsE;IAAEC,SAAS,EAAE;EAAc,CAAC,CACvH,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,WAAW,CAAC;EACrE,MAAM,CAACiC,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;;EAE/E;EACA,MAAM,CAACmC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC;IAC/C2C,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,CACjD;IACEoB,EAAE,EAAE,WAAW;IACf6B,IAAI,EAAE,0BAA0B;IAChCC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE;EACb,CAAC,EACD;IACEjC,EAAE,EAAE,SAAS;IACb6B,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,cAAc;IACtBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,CACnC;IACEoB,EAAE,EAAE,cAAc;IAClB6B,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,uBAAuB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEjC,EAAE,EAAE,gBAAgB;IACpB6B,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,uBAAuB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;;EAEF;EACA,MAAME,WAAW,GAAIC,IAAI,IAAK;IAC5B1B,iBAAiB,CAAC0B,IAAI,CAAC;IACvBC,UAAU,CAAC,MAAM3B,iBAAiB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;EAED,MAAM4B,eAAe,GAAG,MAAOrC,OAAO,IAAK;IACzC,IAAI;MACF,MAAMsC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACxC,OAAO,CAAC;MAC5Cd,sBAAsB,CAACc,OAAO,CAAC;MAC/BkC,WAAW,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDP,WAAW,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMS,UAAU,GAAIC,IAAI,IAAK;IAC3BP,eAAe,CAACO,IAAI,CAAC5C,OAAO,CAAC;IAC7BZ,2BAA2B,CAACwD,IAAI,CAAC5C,OAAO,CAAC;EAC3C,CAAC;EAED,MAAM6C,iBAAiB,GAAIC,MAAM,IAAK;IACpC,MAAMC,cAAc,GAAGlD,YAAY,CAACmD,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAAC7C,EAAE,KAAK+C,MAAM,CAAC;IACtEhD,eAAe,CAACiD,cAAc,CAAC;IAC/Bb,WAAW,CAAC,2BAA2B,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMe,sBAAsB,GAAGA,CAAA,KAAM;IACnCvD,wBAAwB,CAACT,mBAAmB,CAAC;IAC7CK,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM4D,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,UAAU,GAAG1D,qBAAqB,CAAC2D,IAAI,CAAC,CAAC;IAC/C,IAAI,CAACD,UAAU,EAAE;MACfjB,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEAhD,sBAAsB,CAACiE,UAAU,CAAC;IAClC,IAAI;MACF,MAAMb,SAAS,CAACC,SAAS,CAACC,SAAS,CAACW,UAAU,CAAC;IACjD,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACW,IAAI,CAAC,6BAA6B,EAAEZ,KAAK,CAAC;IACpD;IAEAnD,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;IAC5BwC,WAAW,CAAC,kCAAkC,CAAC;EACjD,CAAC;EAED,MAAMoB,oBAAoB,GAAGA,CAAA,KAAM;IACjChE,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;EAC9B,CAAC;EAED,MAAM6D,2BAA2B,GAAGA,CAAA,KAAM;IACxC3D,6BAA6B,CAACT,wBAAwB,CAAC;IACvDK,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMgE,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAML,UAAU,GAAGxD,0BAA0B,CAACyD,IAAI,CAAC,CAAC;IACpD,IAAI,CAACD,UAAU,EAAE;MACfjB,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEA9C,2BAA2B,CAAC+D,UAAU,CAAC;IACvC3D,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;IACjCsC,WAAW,CAAC,uCAAuC,CAAC;EACtD,CAAC;EAED,MAAMuB,yBAAyB,GAAGA,CAAA,KAAM;IACtCjE,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;EACnC,CAAC;EAED,MAAM8D,OAAO,GAAGA,CAAA,KAAM;IACpBxB,WAAW,CAAC,mCAAmC,CAAC;IAChDE,UAAU,CAAC,MAAM;MACfF,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMyB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpD,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChC4B,WAAW,CAAC5B,aAAa,GAAG,0BAA0B,GAAG,sBAAsB,CAAC;EAClF,CAAC;EAED,MAAMsD,cAAc,GAAGA,CAAA,KAAM;IAC3B1B,WAAW,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAM2B,qBAAqB,GAAGA,CAAA,KAAM;IAClChD,2BAA2B,CAAC,CAACD,wBAAwB,CAAC;IACtDsB,WAAW,CAACtB,wBAAwB,GAAG,2BAA2B,GAAG,0BAA0B,CAAC;EAClG,CAAC;;EAED;EACA,MAAMkD,YAAY,GAAIC,QAAQ,IAAK;IACjC,MAAMC,cAAc,GAAGtC,aAAa,CAACsB,MAAM,CAACiB,MAAM,IAAIA,MAAM,CAAClE,EAAE,KAAKgE,QAAQ,CAAC;IAC7EpC,gBAAgB,CAACqC,cAAc,CAAC;IAChC9B,WAAW,CAAC,wCAAwC,CAAC;EACvD,CAAC;EAED,MAAMgC,aAAa,GAAIH,QAAQ,IAAK;IAClC,MAAMC,cAAc,GAAGtC,aAAa,CAACyC,GAAG,CAACF,MAAM,IAC7CA,MAAM,CAAClE,EAAE,KAAKgE,QAAQ,GAAG;MAAE,GAAGE,MAAM;MAAEnC,MAAM,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAW,CAAC,GAAGkC,MACtF,CAAC;IACDtC,gBAAgB,CAACqC,cAAc,CAAC;IAChC9B,WAAW,CAAC,iCAAiC,CAAC;EAChD,CAAC;EAED,MAAMkC,gBAAgB,GAAIL,QAAQ,IAAK;IACrC,MAAMC,cAAc,GAAGtC,aAAa,CAACyC,GAAG,CAACF,MAAM,IAC7CA,MAAM,CAAClE,EAAE,KAAKgE,QAAQ,GAAG;MAAE,GAAGE,MAAM;MAAEnC,MAAM,EAAE,cAAc;MAAEC,QAAQ,EAAE;IAAW,CAAC,GAAGkC,MACzF,CAAC;IACDtC,gBAAgB,CAACqC,cAAc,CAAC;IAChC9B,WAAW,CAAC,wBAAwB,CAAC;EACvC,CAAC;EAED,MAAMmC,UAAU,GAAIN,QAAQ,IAAK;IAC/B,MAAMO,YAAY,GAAGrC,iBAAiB,CAACsC,IAAI,CAACN,MAAM,IAAIA,MAAM,CAAClE,EAAE,KAAKgE,QAAQ,CAAC;IAC7E,IAAIO,YAAY,EAAE;MAChB,MAAME,eAAe,GAAG;QACtB,GAAGF,YAAY;QACfxC,MAAM,EAAE,WAAW;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDJ,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE8C,eAAe,CAAC,CAAC;MACrDtC,WAAW,CAAC,8BAA8BoC,YAAY,CAAC1C,IAAI,EAAE,CAAC;IAChE;EACF,CAAC;EAED,MAAM6C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIvD,aAAa,EAAE;IAEnBC,gBAAgB,CAAC,IAAI,CAAC;IACtBe,WAAW,CAAC,4BAA4B,CAAC;;IAEzC;IACAE,UAAU,CAAC,MAAM;MACfnB,iBAAiB,CAACgB,iBAAiB,CAAC;MACpCd,gBAAgB,CAAC,KAAK,CAAC;MACvBe,WAAW,CAAC,YAAYD,iBAAiB,CAACyC,MAAM,oBAAoB,CAAC;IACvE,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBzC,WAAW,CAAC,2EAA2E,CAAC;EAC1F,CAAC;EAED,MAAM0C,cAAc,GAAGA,CAAA,KAAM;IAC3B1C,WAAW,CAAC,mFAAmF,CAAC;EAClG,CAAC;EAED,oBACEpD,OAAA;IAAK+F,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BhG,OAAA;MAAK+F,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACrBhG,OAAA;QAAK+F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BhG,OAAA;UAAK+F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhG,OAAA;YACEiG,GAAG,EAAC,wBAAwB;YAC5BC,GAAG,EAAC,aAAa;YACjBH,SAAS,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACFtG,OAAA;YAAK+F,SAAS,EAAE,kBAAkBnD,aAAa,CAAC2D,IAAI,CAACpB,MAAM,IAAIA,MAAM,CAACnC,MAAM,KAAK,WAAW,CAAC,GAAG,WAAW,GAAG,cAAc;UAAG;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnI,CAAC,eACNtG,OAAA;UAAI+F,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACNtG,OAAA;QAAK+F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhG,OAAA;UACE+F,SAAS,EAAE,eAAejE,wBAAwB,GAAG,QAAQ,GAAG,EAAE,EAAG;UACrE0E,OAAO,EAAEzB,qBAAsB;UAC/B0B,KAAK,EAAC,kCAAkC;UAAAT,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtG,OAAA;UACE+F,SAAS,EAAE,eAAevE,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC1DgF,OAAO,EAAE3B,iBAAkB;UAC3B4B,KAAK,EAAC,YAAY;UAAAT,QAAA,eAElBhG,OAAA;YAAK+F,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBhG,OAAA;cAAK+F,SAAS,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChCtG,OAAA;cAAK+F,SAAS,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTtG,OAAA;UAAQ+F,SAAS,EAAC,aAAa;UAACS,OAAO,EAAE1B,cAAe;UAAC2B,KAAK,EAAC,UAAU;UAAAT,QAAA,EAAC;QAE1E;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtG,OAAA;UAAQ+F,SAAS,EAAC,aAAa;UAACS,OAAO,EAAEA,CAAA,KAAMnF,eAAe,CAAC,IAAI,CAAE;UAACoF,KAAK,EAAC,UAAU;UAAAT,QAAA,EAAC;QAEvF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL5E,cAAc,iBACb1B,OAAA;MAAK+F,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BhG,OAAA;QAAAgG,QAAA,EAAOtE;MAAc;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDtG,OAAA;MAAK+F,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3BhG,OAAA;QAAK+F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhG,OAAA;UAAK+F,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpChG,OAAA;YAAK+F,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7ChG,OAAA;cAAI+F,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDtG,OAAA;cAAG+F,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL/F,mBAAmB,gBAClBP,OAAA;UAAK+F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhG,OAAA;YACE+F,SAAS,EAAC,iBAAiB;YAC3BW,KAAK,EAAE/F,qBAAsB;YAC7BgG,QAAQ,EAAGC,CAAC,IAAKhG,wBAAwB,CAACgG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC1DI,WAAW,EAAC,uCAAuC;YACnDC,SAAS;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACFtG,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhG,OAAA;cAAQ+F,SAAS,EAAC,yBAAyB;cAACS,OAAO,EAAEpC,kBAAmB;cAAA4B,QAAA,EAAC;YAEzE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtG,OAAA;cAAQ+F,SAAS,EAAC,2BAA2B;cAACS,OAAO,EAAEhC,oBAAqB;cAAAwB,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENtG,OAAA;UAAK+F,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BhG,OAAA;YACE+F,SAAS,EAAC,uCAAuC;YACjDS,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAACpD,mBAAmB,CAAE;YAAA6F,QAAA,gBAEpDhG,OAAA;cAAG+F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAE7F;YAAmB;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDtG,OAAA;cAAG+F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA8B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChEtG,OAAA;cAAQ+F,SAAS,EAAC,oBAAoB;cAACS,OAAO,EAAErC,sBAAuB;cAAA6B,QAAA,EAAC;YAExE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtG,OAAA;QAAK+F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BhG,OAAA;UAAK+F,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpChG,OAAA;YAAI+F,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DtG,OAAA;YAAG+F,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,EAEL7F,wBAAwB,gBACvBT,OAAA;UAAK+F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BhG,OAAA;YACE+F,SAAS,EAAC,iBAAiB;YAC3BW,KAAK,EAAE7F,0BAA2B;YAClC8F,QAAQ,EAAGC,CAAC,IAAK9F,6BAA6B,CAAC8F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/DI,WAAW,EAAC,4CAA4C;YACxDC,SAAS;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACFtG,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhG,OAAA;cAAQ+F,SAAS,EAAC,8BAA8B;cAACS,OAAO,EAAE9B,uBAAwB;cAAAsB,QAAA,EAAC;YAEnF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtG,OAAA;cAAQ+F,SAAS,EAAC,gCAAgC;cAACS,OAAO,EAAE7B,yBAA0B;cAAAqB,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENtG,OAAA;UAAK+F,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BhG,OAAA;YACE+F,SAAS,EAAC,4CAA4C;YACtDS,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAAClD,wBAAwB,CAAE;YAAA2F,QAAA,gBAEzDhG,OAAA;cAAG+F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAE3F;YAAwB;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DtG,OAAA;cAAG+F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAkC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEtG,OAAA;cAAQ+F,SAAS,EAAC,oBAAoB;cAACS,OAAO,EAAE/B,2BAA4B;cAAAuB,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtG,OAAA;QAAI+F,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDtG,OAAA;QAAK+F,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BjF,YAAY,CAACsE,GAAG,CAAEvB,IAAI,iBACrB9D,OAAA;UAEE+F,SAAS,EAAC,cAAc;UACxBS,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAACC,IAAI,CAAE;UAAAkC,QAAA,gBAEhChG,OAAA;YAAK+F,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClChG,OAAA;cAAM+F,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAElC,IAAI,CAAC3C;YAAS;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDtG,OAAA;cACE+F,SAAS,EAAC,eAAe;cACzBS,OAAO,EAAGI,CAAC,IAAK;gBACdA,CAAC,CAACI,eAAe,CAAC,CAAC;gBACnBjD,iBAAiB,CAACD,IAAI,CAAC7C,EAAE,CAAC;cAC5B,CAAE;cAAA+E,QAAA,EACH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNtG,OAAA;YAAG+F,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAElC,IAAI,CAAC5C;UAAO;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GAhBzCxC,IAAI,CAAC7C,EAAE;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBT,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtG,OAAA;MAAQ+F,SAAS,EAAC,sBAAsB;MAACS,OAAO,EAAE5B,OAAQ;MAAAoB,QAAA,eACxDhG,OAAA;QAAKiG,GAAG,EAAC,WAAW;QAACC,GAAG,EAAC,MAAM;QAACH,SAAS,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,EAGRlF,YAAY,iBACXpB,OAAA;MAAK+F,SAAS,EAAC,eAAe;MAACS,OAAO,EAAEA,CAAA,KAAMnF,eAAe,CAAC,KAAK,CAAE;MAAA2E,QAAA,eACnEhG,OAAA;QAAK+F,SAAS,EAAC,kBAAkB;QAACS,OAAO,EAAGI,CAAC,IAAKA,CAAC,CAACI,eAAe,CAAC,CAAE;QAAAhB,QAAA,gBACpEhG,OAAA;UAAK+F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BhG,OAAA;YAAI+F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CtG,OAAA;YAAQ+F,SAAS,EAAC,cAAc;YAACS,OAAO,EAAEA,CAAA,KAAMnF,eAAe,CAAC,KAAK,CAAE;YAAA2E,QAAA,EAAC;UAExE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtG,OAAA;UAAK+F,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAE/BhG,OAAA;YAAK+F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BhG,OAAA;cAAI+F,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CtG,OAAA;cAAK+F,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BhG,OAAA;gBAAG+F,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClDtG,OAAA;gBAAG+F,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClDtG,OAAA;gBAAK+F,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChChG,OAAA;kBAAK+F,SAAS,EAAE,2BAA2BnE,gBAAgB;gBAAG;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrEtG,OAAA;kBAAM+F,SAAS,EAAE,sBAAsBnE,gBAAgB,EAAG;kBAAAoE,QAAA,GAAC,UACjD,EAACpE,gBAAgB,KAAK,WAAW,GAAG,WAAW,GAAG,cAAc;gBAAA;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtG,OAAA;YAAK+F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BhG,OAAA;cAAI+F,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChD1D,aAAa,CAACgD,MAAM,KAAK,CAAC,gBACzB5F,OAAA;cAAK+F,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChG,OAAA;gBAAG+F,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5DtG,OAAA;gBAAG+F,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAA+C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,GAEN1D,aAAa,CAACyC,GAAG,CAAEF,MAAM,iBACvBnF,OAAA;cAAqB+F,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBAEnDhG,OAAA;gBACE+F,SAAS,EAAC,yBAAyB;gBACnCS,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAACG,MAAM,CAAClE,EAAE,CAAE;gBAAA+E,QAAA,EACxC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETtG,OAAA;gBAAK+F,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhG,OAAA;kBAAG+F,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEb,MAAM,CAACrC;gBAAI;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDtG,OAAA;kBAAG+F,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEb,MAAM,CAACpC;gBAAI;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDtG,OAAA;kBAAK+F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChChG,OAAA;oBAAK+F,SAAS,EAAE,2BAA2BZ,MAAM,CAACnC,MAAM;kBAAG;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClEtG,OAAA;oBAAM+F,SAAS,EAAE,sBAAsBZ,MAAM,CAACnC,MAAM,EAAG;oBAAAgD,QAAA,GACpDb,MAAM,CAACnC,MAAM,KAAK,WAAW,GAAG,WAAW,GAAG,cAAc,EAAC,UAAG,EAACmC,MAAM,CAAClC,QAAQ;kBAAA;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtG,OAAA;gBACE+F,SAAS,EAAE,qCAAqCZ,MAAM,CAACnC,MAAM,KAAK,WAAW,GAAG,mBAAmB,GAAG,gBAAgB,EAAG;gBACzHwD,OAAO,EAAEA,CAAA,KAAMrB,MAAM,CAACnC,MAAM,KAAK,WAAW,GAAGsC,gBAAgB,CAACH,MAAM,CAAClE,EAAE,CAAC,GAAGmE,aAAa,CAACD,MAAM,CAAClE,EAAE,CAAE;gBAAA+E,QAAA,EAErGb,MAAM,CAACnC,MAAM,KAAK,WAAW,GAAG,YAAY,GAAG;cAAS;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA,GA1BDnB,MAAM,CAAClE,EAAE;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2Bd,CACN,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNtG,OAAA;YAAK+F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BhG,OAAA;cAAK+F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BhG,OAAA;gBAAI+F,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDtG,OAAA;gBACE+F,SAAS,EAAE,mBAAmB3D,aAAa,GAAG,0BAA0B,GAAG,EAAE,EAAG;gBAChFoE,OAAO,EAAEb,gBAAiB;gBAC1BsB,QAAQ,EAAE7E,aAAc;gBAAA4D,QAAA,EAEvB5D,aAAa,GAAG,gBAAgB,GAAG;cAAU;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLpE,cAAc,CAAC0D,MAAM,KAAK,CAAC,gBAC1B5F,OAAA;cAAK+F,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChChG,OAAA;gBAAG+F,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrDtG,OAAA;gBAAG+F,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChC5D,aAAa,GAAG,yBAAyB,GAAG;cAAoC;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,GAENpE,cAAc,CAACmD,GAAG,CAAEF,MAAM,iBACxBnF,OAAA;cAAqB+F,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrDhG,OAAA;gBAAK+F,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BhG,OAAA;kBAAG+F,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEb,MAAM,CAACrC;gBAAI;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDtG,OAAA;kBAAG+F,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEb,MAAM,CAACpC;gBAAI;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDtG,OAAA;kBAAG+F,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEb,MAAM,CAAClC;gBAAQ;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNtG,OAAA;gBACE+F,SAAS,EAAC,aAAa;gBACvBS,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAACJ,MAAM,CAAClE,EAAE,CAAE;gBAAA+E,QAAA,EACtC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAXDnB,MAAM,CAAClE,EAAE;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYd,CACN,CACF,eAEDtG,OAAA;cAAK+F,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnChG,OAAA;gBAAQ+F,SAAS,EAAC,gBAAgB;gBAACS,OAAO,EAAEX,UAAW;gBAAAG,QAAA,EAAC;cAExD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtG,OAAA;gBAAQ+F,SAAS,EAAC,oBAAoB;gBAACS,OAAO,EAAEV,cAAe;gBAAAE,QAAA,EAAC;cAEhE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtG,OAAA;YAAK+F,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BhG,OAAA;cAAI+F,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtDtG,OAAA;cACE+F,SAAS,EAAC,cAAc;cACxBS,OAAO,EAAEA,CAAA,KAAMjF,mBAAmB,CAAC,IAAI,CAAE;cAAAyE,QAAA,gBAEzChG,OAAA;gBAAM+F,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDtG,OAAA;gBAAM+F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eAGTtG,OAAA;cACE+F,SAAS,EAAC,cAAc;cACxBS,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMU,QAAQ,GAAG,CAAClF,qBAAqB;gBACvCC,wBAAwB,CAACiF,QAAQ,CAAC;gBAClC9D,WAAW,CAAC8D,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B,CAAC;cAChF,CAAE;cAAAlB,QAAA,gBAEFhG,OAAA;gBAAM+F,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DtG,OAAA;gBAAM+F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EACjChE,qBAAqB,GAAG,IAAI,GAAG;cAAK;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGTtG,OAAA;cACE+F,SAAS,EAAC,cAAc;cACxBS,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIpE,aAAa,EAAE;kBACjBC,gBAAgB,CAAC,KAAK,CAAC;kBACvBe,WAAW,CAAC,2BAA2B,CAAC;gBAC1C,CAAC,MAAM;kBACLf,gBAAgB,CAAC,IAAI,CAAC;kBACtBe,WAAW,CAAC,2BAA2B,CAAC;gBAC1C;cACF,CAAE;cAAA4C,QAAA,gBAEFhG,OAAA;gBAAM+F,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5DtG,OAAA;gBAAM+F,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EACjC5D,aAAa,GAAG,IAAI,GAAG;cAAK;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACpG,EAAA,CA1jBQD,GAAG;AAAAkH,EAAA,GAAHlH,GAAG;AA4jBZ,eAAeA,GAAG;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}