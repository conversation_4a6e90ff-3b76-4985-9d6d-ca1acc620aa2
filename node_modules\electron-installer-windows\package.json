{"name": "electron-installer-windows", "description": "Create a Windows package for your Electron app.", "version": "3.0.0", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://unindented.org/"}, "repository": {"type": "git", "url": "git://github.com/electron-userland/electron-installer-windows.git"}, "keywords": ["electron", "windows"], "main": "src/installer.js", "bin": {"electron-installer-windows": "src/cli.js"}, "scripts": {"lint": "eslint .", "spec": "mocha", "test": "npm run lint && npm run spec"}, "engines": {"node": ">=8.0.0"}, "dependencies": {"debug": "^4.1.1", "electron-installer-common": "^0.10.0", "fs-extra": "^8.1.0", "lodash": "^4.17.15", "parse-author": "^2.0.0", "which": "^2.0.2", "yargs": "^15.1.0"}, "devDependencies": {"chai": "^4.2.0", "eslint": "^6.8.0", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.20.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "finalhandler": "^1.1.2", "mocha": "^7.0.0", "promise-retry": "^1.1.1", "serve-static": "^1.14.1", "tmp-promise": "^2.0.2"}}