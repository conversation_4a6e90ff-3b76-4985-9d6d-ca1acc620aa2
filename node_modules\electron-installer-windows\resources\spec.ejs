<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2010/07/nuspec.xsd">
  <metadata>
    <id><%- name %></id>
    <version><%- version %></version>
    <description><%- description %></description>
    <authors><%- authors.join(', ') %></authors>
    <% if (productName) { %><title><%- productName %></title><% } %>
    <% if (copyright) { %><copyright><%- copyright %></copyright><% } %>
    <% if (owners) { %><owners><%- owners.join(', ') %></owners><% } %>
    <% if (homepage) { %><projectUrl><%- homepage %></projectUrl><% } %>
    <% if (tags && tags.length) { %><tags><%- tags.join(' ') %></tags><% } %>
    <% if (iconNuget) { %><icon><%- iconNugetName %></icon><% } %>
  </metadata>
  <files>
    <file src="locales\**" target="lib\net45\locales" />
    <file src="resources\**" target="lib\net45\resources" />
    <file src="*.bin" target="lib\net45" />
    <file src="*.dll" target="lib\net45" />
    <file src="*.pak" target="lib\net45" />
    <file src="Update.exe" target="lib\net45\squirrel.exe" />
    <file src="icudtl.dat" target="lib\net45\icudtl.dat" />
    <file src="LICENSE" target="lib\net45\LICENSE" />
    <file src="<%- exe %>" target="lib\net45\<%- exe %>" />
    <% if (iconNuget) { %> <file src="<%- iconNugetName %>" target="" /> <% } %>
  </files>
</package>
