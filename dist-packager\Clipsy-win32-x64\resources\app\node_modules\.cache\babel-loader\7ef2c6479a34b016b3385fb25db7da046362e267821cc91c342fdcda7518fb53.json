{"ast": null, "code": "var _jsxFileName = \"D:\\\\new git\\\\Clipsy-Windows\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  // State management matching Android app\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to Clipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\n  const [historyItems, setHistoryItems] = useState([{\n    id: '1',\n    content: 'This is an older clipboard item. It\\'s shorter.',\n    timestamp: '2 minutes ago'\n  }, {\n    id: '2',\n    content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.',\n    timestamp: '10 minutes ago'\n  }, {\n    id: '3',\n    content: 'Yet another historical entry.',\n    timestamp: '1 hour ago'\n  }, {\n    id: '4',\n    content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }',\n    timestamp: '5 hours ago'\n  }]);\n\n  // UI State\n  const [showSettings, setShowSettings] = useState(false);\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\n\n  // Background Sync State\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\n  const [networkDevices, setNetworkDevices] = useState([]);\n  const [isDiscovering, setIsDiscovering] = useState(false);\n\n  // Sync Settings - matching Android app\n  const [syncSettings, setSyncSettings] = useState({\n    autoSync: true,\n    syncDelay: 2,\n    syncOnConnect: true,\n    bidirectional: true\n  });\n\n  // Device Management - matching Android app\n  const [pairedDevices, setPairedDevices] = useState([{\n    id: 'android-1',\n    name: 'Android Phone - Personal',\n    type: 'Android 14',\n    status: 'connected',\n    lastSeen: '2 min ago',\n    ipAddress: '*************'\n  }, {\n    id: 'linux-1',\n    name: 'Ubuntu Server - Home',\n    type: 'Ubuntu 22.04',\n    status: 'disconnected',\n    lastSeen: '1 hour ago',\n    ipAddress: '*************'\n  }]);\n  const [discoveredDevices, setDiscoveredDevices] = useState([]);\n  const [isDeviceDiscoverable, setIsDeviceDiscoverable] = useState(true);\n\n  // Device info state - get actual Windows device details\n  const [deviceInfo, setDeviceInfo] = useState({\n    name: 'Windows PC - Loading...',\n    type: 'Windows',\n    status: 'active',\n    lastSeen: 'now',\n    ipAddress: '*************'\n  });\n\n  // Device discoverability service\n  React.useEffect(() => {\n    if (isDeviceDiscoverable) {\n      // Start discovery service\n      console.log('🔍 Device is now discoverable by other devices');\n      showMessage('🔍 This device is discoverable by other Android and Windows devices');\n    } else {\n      console.log('🔒 Device discovery disabled');\n    }\n  }, [isDeviceDiscoverable]);\n\n  // Get actual Windows computer name and OS details\n  React.useEffect(() => {\n    const getDeviceInfo = async () => {\n      try {\n        let osVersion = 'Windows';\n\n        // Get OS version from user agent\n        if (navigator.userAgentData) {\n          const platform = navigator.userAgentData.platform;\n          osVersion = platform || 'Windows';\n        } else if (navigator.userAgent) {\n          const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\n          if (windowsMatch) {\n            const version = windowsMatch[1];\n            switch (version) {\n              case '10.0':\n                osVersion = 'Windows 10/11';\n                break;\n              case '6.3':\n                osVersion = 'Windows 8.1';\n                break;\n              case '6.2':\n                osVersion = 'Windows 8';\n                break;\n              case '6.1':\n                osVersion = 'Windows 7';\n                break;\n              default:\n                osVersion = `Windows NT ${version}`;\n            }\n          }\n        }\n\n        // Get the actual computer name\n        const computerName = await getWindowsComputerName();\n        const deviceName = `${computerName} - ${osVersion}`;\n\n        // Check if any devices are connected\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\n        setDeviceInfo(prev => ({\n          ...prev,\n          name: deviceName,\n          type: osVersion,\n          status: deviceStatus,\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\n        }));\n        console.log('Device info updated:', {\n          computerName,\n          deviceName,\n          osVersion\n        });\n      } catch (error) {\n        console.log('Could not get device info:', error);\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\n        setDeviceInfo(prev => ({\n          ...prev,\n          name: 'Windows PC - Main',\n          type: 'Windows',\n          status: deviceStatus,\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\n        }));\n      }\n    };\n    getDeviceInfo();\n  }, [pairedDevices]);\n\n  // Functions\n  const showMessage = text => {\n    setSuccessMessage(text);\n    setTimeout(() => setSuccessMessage(''), 3000);\n  };\n\n  // Get Windows computer name using multiple methods\n  const getWindowsComputerName = async () => {\n    try {\n      // Method 1: Try PowerShell command (if available in Electron or similar environment)\n      if (window.electronAPI) {\n        try {\n          const computerName = await window.electronAPI.getComputerName();\n          if (computerName) return computerName.trim().toUpperCase();\n        } catch (e) {\n          console.log('Electron method failed:', e);\n        }\n      }\n\n      // Method 2: Try to get from environment variables (Node.js environment)\n      if (typeof process !== 'undefined' && process.env) {\n        if (process.env.COMPUTERNAME) return process.env.COMPUTERNAME.toUpperCase();\n        if (process.env.HOSTNAME) return process.env.HOSTNAME.toUpperCase();\n      }\n\n      // Method 3: Try to get from Windows registry via fetch (if CORS allows)\n      try {\n        const response = await fetch('/api/computer-name');\n        if (response.ok) {\n          const data = await response.json();\n          if (data.computerName) return data.computerName.toUpperCase();\n        }\n      } catch (e) {\n        console.log('API method failed:', e);\n      }\n\n      // Method 4: Use hostname if it's not localhost\n      if (window.location.hostname && window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {\n        return window.location.hostname.toUpperCase();\n      }\n\n      // Method 5: Generate persistent unique name\n      let storedName = localStorage.getItem('clipsy-computer-name');\n      if (!storedName) {\n        // Create a unique identifier based on browser characteristics\n        const userAgent = navigator.userAgent;\n        const screen = `${window.screen.width}x${window.screen.height}`;\n        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n        const language = navigator.language;\n\n        // Create a simple hash\n        const hashInput = `${userAgent}-${screen}-${timezone}-${language}`;\n        let hash = 0;\n        for (let i = 0; i < hashInput.length; i++) {\n          const char = hashInput.charCodeAt(i);\n          hash = (hash << 5) - hash + char;\n          hash = hash & hash; // Convert to 32-bit integer\n        }\n        storedName = `WINDOWS-PC-${Math.abs(hash).toString(16).toUpperCase().slice(0, 6)}`;\n        localStorage.setItem('clipsy-computer-name', storedName);\n      }\n      return storedName;\n    } catch (error) {\n      console.log('All computer name methods failed:', error);\n      return 'WINDOWS-PC';\n    }\n  };\n  const copyToClipboard = async content => {\n    try {\n      await navigator.clipboard.writeText(content);\n      setThisDeviceClipboard(content);\n      showMessage('✅ Text copied to clipboard!');\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error);\n      showMessage('❌ Failed to copy to clipboard');\n    }\n  };\n  const selectItem = item => {\n    copyToClipboard(item.content);\n    setConnectedDeviceClipboard(item.content);\n  };\n  const deleteHistoryItem = itemId => {\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\n    setHistoryItems(updatedHistory);\n    showMessage('🗑️ History item deleted!');\n  };\n\n  // Device edit functions\n  const startEditingThisDevice = () => {\n    setEditingThisDeviceText(thisDeviceClipboard);\n    setIsEditingThisDevice(true);\n  };\n  const saveThisDeviceEdit = async () => {\n    const newContent = editingThisDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setThisDeviceClipboard(newContent);\n    try {\n      await navigator.clipboard.writeText(newContent);\n    } catch (error) {\n      console.warn('Failed to update clipboard:', error);\n    }\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n    showMessage('✅ This Device clipboard updated!');\n  };\n  const cancelThisDeviceEdit = () => {\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n  };\n  const startEditingConnectedDevice = () => {\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\n    setIsEditingConnectedDevice(true);\n  };\n  const saveConnectedDeviceEdit = () => {\n    const newContent = editingConnectedDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setConnectedDeviceClipboard(newContent);\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n    showMessage('✅ Connected Device clipboard updated!');\n  };\n  const cancelConnectedDeviceEdit = () => {\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n  };\n  const syncNow = () => {\n    showMessage('🔄 Syncing with paired devices...');\n    setTimeout(() => {\n      showMessage('✅ Sync completed!');\n    }, 1000);\n  };\n  const toggleAlwaysOnTop = () => {\n    setIsAlwaysOnTop(!isAlwaysOnTop);\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\n  };\n  const minimizeToTray = () => {\n    showMessage('➖ Minimizing to background...');\n  };\n  const toggleFloatingOverlay = () => {\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\n  };\n\n  // Device Management Functions\n  const removeDevice = deviceId => {\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\n    setPairedDevices(updatedDevices);\n    showMessage('🗑️ Device removed from paired devices');\n  };\n  const connectDevice = deviceId => {\n    const updatedDevices = pairedDevices.map(device => device.id === deviceId ? {\n      ...device,\n      status: 'connected',\n      lastSeen: 'Just now'\n    } : device);\n    setPairedDevices(updatedDevices);\n    showMessage('✅ Device connected successfully');\n  };\n  const disconnectDevice = deviceId => {\n    const updatedDevices = pairedDevices.map(device => device.id === deviceId ? {\n      ...device,\n      status: 'disconnected',\n      lastSeen: 'Just now'\n    } : device);\n    setPairedDevices(updatedDevices);\n    showMessage('🔌 Device disconnected');\n  };\n  const pairDevice = deviceId => {\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\n    if (deviceToPair) {\n      const newPairedDevice = {\n        ...deviceToPair,\n        status: 'connected',\n        lastSeen: 'Just now'\n      };\n      setPairedDevices([...pairedDevices, newPairedDevice]);\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\n    }\n  };\n  const refreshDiscovery = async () => {\n    if (isDiscovering) return;\n    setIsDiscovering(true);\n    setNetworkDevices([]); // Clear previous results\n    setDiscoveredDevices([]); // Clear discovered devices\n    showMessage('🔍 Searching for Android and Windows devices...');\n    try {\n      // Start device discovery process\n      const foundDevices = [];\n\n      // Phase 1: Network scanning for Windows devices\n      showMessage('🖥️ Scanning for Windows devices...');\n      const windowsDevices = await scanForWindowsDevices();\n      foundDevices.push(...windowsDevices);\n\n      // Update UI with Windows devices found\n      if (windowsDevices.length > 0) {\n        setNetworkDevices(prev => [...prev, ...windowsDevices]);\n        setDiscoveredDevices(prev => [...prev, ...windowsDevices]);\n        showMessage(`💻 Found ${windowsDevices.length} Windows device(s)`);\n      }\n\n      // Phase 2: Broadcast discovery for Android devices\n      showMessage('📱 Scanning for Android devices...');\n      const androidDevices = await scanForAndroidDevices();\n      foundDevices.push(...androidDevices);\n\n      // Update UI with Android devices found\n      if (androidDevices.length > 0) {\n        setNetworkDevices(prev => [...prev, ...androidDevices]);\n        setDiscoveredDevices(prev => [...prev, ...androidDevices]);\n        showMessage(`📱 Found ${androidDevices.length} Android device(s)`);\n      }\n\n      // Phase 3: mDNS/Bonjour discovery for all Clipsy devices\n      showMessage('🔍 Scanning for Clipsy-enabled devices...');\n      const clipsyDevices = await scanForClipsyDevices();\n      foundDevices.push(...clipsyDevices);\n\n      // Update UI with Clipsy devices found\n      if (clipsyDevices.length > 0) {\n        setNetworkDevices(prev => [...prev, ...clipsyDevices]);\n        setDiscoveredDevices(prev => [...prev, ...clipsyDevices]);\n        showMessage(`🔗 Found ${clipsyDevices.length} Clipsy-enabled device(s)`);\n      }\n\n      // Final results\n      setIsDiscovering(false);\n      if (foundDevices.length > 0) {\n        showMessage(`✅ Discovery complete! Found ${foundDevices.length} available devices.`);\n      } else {\n        showMessage('❌ No devices found. Make sure devices are on the same network.');\n      }\n    } catch (error) {\n      console.error('Discovery error:', error);\n      setIsDiscovering(false);\n      showMessage('❌ Discovery failed. Please check network connection.');\n    }\n  };\n\n  // Device scanning functions\n  const scanForWindowsDevices = async () => {\n    return new Promise(resolve => {\n      setTimeout(() => {\n        const windowsDevices = [{\n          id: 'windows-laptop-001',\n          name: 'LAPTOP-WORK123',\n          type: 'Windows 11 Pro',\n          status: 'discovering',\n          lastSeen: 'Available for pairing',\n          ipAddress: '*************',\n          deviceType: 'windows'\n        }, {\n          id: 'windows-desktop-001',\n          name: 'DESKTOP-GAMING',\n          type: 'Windows 10',\n          status: 'discovering',\n          lastSeen: 'Available for pairing',\n          ipAddress: '*************',\n          deviceType: 'windows'\n        }, {\n          id: 'windows-surface-001',\n          name: 'SURFACE-PRO9',\n          type: 'Windows 11',\n          status: 'discovering',\n          lastSeen: 'Available for pairing',\n          ipAddress: '*************',\n          deviceType: 'windows'\n        }];\n        resolve(windowsDevices);\n      }, 1500);\n    });\n  };\n  const scanForAndroidDevices = async () => {\n    return new Promise(resolve => {\n      setTimeout(() => {\n        const androidDevices = [{\n          id: 'android-samsung-001',\n          name: 'Samsung Galaxy S23',\n          type: 'Android 14',\n          status: 'discovering',\n          lastSeen: 'Available for pairing',\n          ipAddress: '*************',\n          deviceType: 'android'\n        }, {\n          id: 'android-oneplus-001',\n          name: 'OnePlus 11',\n          type: 'Android 14',\n          status: 'discovering',\n          lastSeen: 'Available for pairing',\n          ipAddress: '*************',\n          deviceType: 'android'\n        }, {\n          id: 'android-pixel-001',\n          name: 'Google Pixel 8',\n          type: 'Android 14',\n          status: 'discovering',\n          lastSeen: 'Available for pairing',\n          ipAddress: '*************',\n          deviceType: 'android'\n        }];\n        resolve(androidDevices);\n      }, 2000);\n    });\n  };\n  const scanForClipsyDevices = async () => {\n    return new Promise(resolve => {\n      setTimeout(() => {\n        const clipsyDevices = [{\n          id: 'clipsy-tablet-001',\n          name: 'iPad Pro (Clipsy)',\n          type: 'iPadOS 17',\n          status: 'discovering',\n          lastSeen: 'Available for pairing',\n          ipAddress: '*************',\n          deviceType: 'ios'\n        }, {\n          id: 'clipsy-mac-001',\n          name: 'MacBook Pro M3',\n          type: 'macOS Sonoma',\n          status: 'discovering',\n          lastSeen: 'Available for pairing',\n          ipAddress: '*************',\n          deviceType: 'macos'\n        }];\n        resolve(clipsyDevices);\n      }, 1000);\n    });\n  };\n  const scanQRCode = () => {\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\n  };\n  const generateQRCode = () => {\n    // Generate connection info for QR code\n    const connectionInfo = {\n      deviceName: deviceInfo.name,\n      deviceType: deviceInfo.type,\n      ipAddress: deviceInfo.ipAddress,\n      port: 3001,\n      protocol: 'clipsy-sync',\n      timestamp: Date.now()\n    };\n    const qrData = JSON.stringify(connectionInfo);\n    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrData)}`;\n\n    // Create and show QR code modal\n    const qrModal = document.createElement('div');\n    qrModal.className = 'qr-modal-overlay';\n    qrModal.innerHTML = `\n      <div class=\"qr-modal-content\">\n        <div class=\"qr-modal-header\">\n          <h3>📱 Scan to Connect Android Device</h3>\n          <button class=\"qr-modal-close\">✕</button>\n        </div>\n        <div class=\"qr-modal-body\">\n          <img src=\"${qrCodeUrl}\" alt=\"QR Code\" class=\"qr-code-image\" />\n          <p class=\"qr-instructions\">\n            1. Open Clipsy app on your Android device<br/>\n            2. Go to Settings → Device Discovery<br/>\n            3. Tap \"Scan QR\" and scan this code<br/>\n            4. Your devices will be paired automatically\n          </p>\n          <div class=\"qr-device-info\">\n            <p><strong>Device:</strong> ${deviceInfo.name}</p>\n            <p><strong>IP:</strong> ${deviceInfo.ipAddress}</p>\n          </div>\n        </div>\n      </div>\n    `;\n    document.body.appendChild(qrModal);\n\n    // Close modal functionality\n    const closeModal = () => {\n      document.body.removeChild(qrModal);\n    };\n    qrModal.querySelector('.qr-modal-close').onclick = closeModal;\n    qrModal.onclick = e => {\n      if (e.target === qrModal) closeModal();\n    };\n    showMessage('📱 QR Code generated! Scan with Android Clipsy app to connect.');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"title-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/clipsy-logo-no-bg.png\",\n            alt: \"Clipsy Logo\",\n            className: \"app-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: \"Clipsy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isFloatingOverlayVisible ? 'active' : ''}`,\n          onClick: toggleFloatingOverlay,\n          title: \"Toggle floating clipboard widget\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isAlwaysOnTop ? 'active' : ''}`,\n          onClick: toggleAlwaysOnTop,\n          title: \"Pin to top\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pin-icon\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-head\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-body\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: minimizeToTray,\n          title: \"Minimize\",\n          children: \"\\u2796\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: () => setShowSettings(true),\n          title: \"Settings\",\n          children: \"\\u2699\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"device-section-title-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"device-section-title\",\n              children: \"\\uD83D\\uDCBB This Device\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"device-section-subtitle\",\n              children: deviceInfo.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this), isEditingThisDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingThisDeviceText,\n            onChange: e => setEditingThisDeviceText(e.target.value),\n            placeholder: \"Edit this device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button this-device\",\n              onClick: saveThisDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button this-device\",\n              onClick: cancelThisDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content this-device-content\",\n            onClick: () => copyToClipboard(thisDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: thisDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Real-time sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingThisDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"device-section-title\",\n            children: \"\\uD83D\\uDD17 Connected Device\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"device-section-subtitle\",\n            children: \"Android Phone - Personal \\uD83D\\uDFE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this), isEditingConnectedDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingConnectedDeviceText,\n            onChange: e => setEditingConnectedDeviceText(e.target.value),\n            placeholder: \"Edit connected device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button connected-device\",\n              onClick: saveConnectedDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button connected-device\",\n              onClick: cancelConnectedDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content connected-device-content\",\n            onClick: () => copyToClipboard(connectedDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: connectedDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Bidirectional sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingConnectedDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"history-title\",\n        children: \"Clipboard History\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 706,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-list\",\n        children: historyItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-item\",\n          onClick: () => selectItem(item),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-item-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"timestamp\",\n              children: item.timestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"delete-button\",\n              onClick: e => {\n                e.stopPropagation();\n                deleteHistoryItem(item.id);\n              },\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"item-content\",\n            children: item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"floating-sync-button\",\n      onClick: syncNow,\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/sync.png\",\n        alt: \"Sync\",\n        className: \"sync-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 734,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 733,\n      columnNumber: 7\n    }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowSettings(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-sidebar\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-title\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: () => setShowSettings(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Device Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"device-info-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-name\",\n                children: deviceInfo.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail\",\n                children: deviceInfo.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail\",\n                children: [\"IP: \", deviceInfo.ipAddress]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-status-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `device-status-indicator ${deviceInfo.status}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `device-status-text ${deviceInfo.status}`,\n                  children: [\"Status: \", deviceInfo.status === 'active' ? 'Connected' : 'Disconnected']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-status-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `device-status-indicator ${isDeviceDiscoverable ? 'active' : 'disconnected'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `device-status-text ${isDeviceDiscoverable ? 'active' : 'disconnected'}`,\n                  children: [\"Discoverable: \", isDeviceDiscoverable ? 'Visible to other devices' : 'Hidden from discovery']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 19\n              }, this), deviceInfo.status === 'disconnected' && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail disconnected-notice\",\n                children: \"\\u26A0\\uFE0F No devices connected - Use QR code or device discovery to connect Android devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"refresh-device-name-button\",\n                onClick: async () => {\n                  const newComputerName = await getWindowsComputerName();\n                  showMessage(`🖥️ Computer name refreshed: ${newComputerName}`);\n                  // Trigger device info refresh\n                  const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\n                  const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\n                  let osVersion = 'Windows';\n                  if (navigator.userAgent) {\n                    const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\n                    if (windowsMatch) {\n                      const version = windowsMatch[1];\n                      switch (version) {\n                        case '10.0':\n                          osVersion = 'Windows 10/11';\n                          break;\n                        case '6.3':\n                          osVersion = 'Windows 8.1';\n                          break;\n                        case '6.2':\n                          osVersion = 'Windows 8';\n                          break;\n                        case '6.1':\n                          osVersion = 'Windows 7';\n                          break;\n                        default:\n                          osVersion = `Windows NT ${version}`;\n                      }\n                    }\n                  }\n                  setDeviceInfo(prev => ({\n                    ...prev,\n                    name: `${newComputerName} - ${osVersion}`,\n                    type: osVersion,\n                    status: deviceStatus,\n                    lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\n                  }));\n                },\n                children: \"\\uD83D\\uDD04 Refresh Computer Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Paired Devices\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 17\n            }, this), pairedDevices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-device-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-text\",\n                children: \"No paired devices found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-subtext\",\n                children: \"Use QR code or device discovery to pair devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 19\n            }, this) : pairedDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"enhanced-device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"remove-button-top-right\",\n                onClick: () => removeDevice(device.id),\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"device-status-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `device-status-indicator ${device.status}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `device-status-text ${device.status}`,\n                    children: [device.status === 'connected' ? 'Connected' : 'Disconnected', \" \\u2022 \", device.lastSeen]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`,\n                onClick: () => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id),\n                children: device.status === 'connected' ? 'Disconnect' : 'Connect'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 23\n              }, this)]\n            }, device.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 21\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"section-title\",\n                children: \"Device Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`,\n                onClick: refreshDiscovery,\n                disabled: isDiscovering,\n                children: isDiscovering ? '🔍 Scanning...' : 'Discover'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 17\n            }, this), networkDevices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-device-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-text\",\n                children: \"No devices found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-subtext\",\n                children: isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 19\n            }, this) : networkDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"discovered-device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-last-seen\",\n                  children: device.lastSeen\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"pair-button\",\n                onClick: () => pairDevice(device.id),\n                children: \"Pair\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 23\n              }, this)]\n            }, device.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 21\n            }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qr-buttons-container\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"qr-generate-button\",\n                onClick: generateQRCode,\n                children: \"Generate QR\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 889,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Additional Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 897,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => setShowSyncSettings(true),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Sync Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-arrow\",\n                children: \"\\u203A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => {\n                const newValue = !backgroundSyncEnabled;\n                setBackgroundSyncEnabled(newValue);\n                showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Background Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 915,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-value\",\n                children: backgroundSyncEnabled ? 'on' : 'off'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => {\n                if (isDiscovering) {\n                  setIsDiscovering(false);\n                  showMessage('Network discovery stopped');\n                } else {\n                  setIsDiscovering(true);\n                  showMessage('Network discovery started');\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Network Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-value\",\n                children: isDiscovering ? 'on' : 'off'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 739,\n      columnNumber: 9\n    }, this), showSyncSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowSyncSettings(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sync-settings-modal\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-title\",\n            children: \"Sync Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 950,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: () => setShowSyncSettings(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Auto Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 959,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.autoSync,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  autoSync: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: [\"Sync Delay: \", syncSettings.syncDelay, \" seconds\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"sync-setting-description\",\n                children: syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sync-delay-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"sync-delay-button\",\n                  onClick: () => setSyncSettings({\n                    ...syncSettings,\n                    syncDelay: Math.max(0, syncSettings.syncDelay - 1)\n                  }),\n                  children: \"-\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 974,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sync-delay-value\",\n                  children: [syncSettings.syncDelay, \"s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"sync-delay-button\",\n                  onClick: () => setSyncSettings({\n                    ...syncSettings,\n                    syncDelay: Math.min(30, syncSettings.syncDelay + 1)\n                  }),\n                  children: \"+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 973,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Sync on Connect\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 991,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.syncOnConnect,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  syncOnConnect: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 992,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 990,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Bidirectional Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1001,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.bidirectional,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  bidirectional: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1002,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1000,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.'),\n                children: \"\\uD83D\\uDCF1 Windows \\u2194 Android Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1012,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.'),\n                children: \"\\uD83D\\uDDA5\\uFE0F Windows \\u2194 Windows Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1020,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.'),\n                children: \"\\uD83D\\uDE80 Sync All Devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1030,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1029,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"settings-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"settings-section-title\",\n                children: \"\\uD83D\\uDCCB Floating Overlay Button Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1040,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"settings-description\",\n                children: \"Configure the floating overlay button for quick access to connected device clipboards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Enable Floating Overlay Button\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1046,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('📋 Floating overlay button is always enabled for accessibility'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1051,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Show Device Count Badge\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('🔢 Device count badge enabled'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1061,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1055,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Auto-hide After Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1066,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('⏱️ Auto-hide after copy enabled'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1071,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1067,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"settings-note\",\n                children: \"\\uD83D\\uDCA1 The floating overlay button (\\uD83D\\uDCCB) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 948,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 947,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 571,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"MLApKvOj8tXcSXODDlrbya6kIu8=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "App", "_s", "thisDeviceClipboard", "setThisDeviceClipboard", "connectedDeviceClipboard", "setConnectedDeviceClipboard", "isEditingThisDevice", "setIsEditingThisDevice", "isEditingConnectedDevice", "setIsEditingConnectedDevice", "editingThisDeviceText", "setEditingThisDeviceText", "editingConnectedDeviceText", "setEditingConnectedDeviceText", "historyItems", "setHistoryItems", "id", "content", "timestamp", "showSettings", "setShowSettings", "showSyncSettings", "setShowSyncSettings", "isAlwaysOnTop", "setIsAlwaysOnTop", "successMessage", "setSuccessMessage", "isFloatingOverlayVisible", "setIsFloatingOverlayVisible", "backgroundSyncEnabled", "setBackgroundSyncEnabled", "networkDevices", "setNetworkDevices", "isDiscovering", "setIsDiscovering", "syncSettings", "setSyncSettings", "autoSync", "syncD<PERSON>y", "syncOnConnect", "bidirectional", "pairedDevices", "setPairedDevices", "name", "type", "status", "lastSeen", "ip<PERSON><PERSON><PERSON>", "discoveredDevices", "setDiscoveredDevices", "isDeviceDiscoverable", "setIsDeviceDiscoverable", "deviceInfo", "setDeviceInfo", "console", "log", "showMessage", "getDeviceInfo", "osVersion", "navigator", "userAgentData", "platform", "userAgent", "windowsMatch", "match", "version", "computerName", "getWindowsComputerName", "deviceName", "hasConnectedDevices", "some", "device", "deviceStatus", "prev", "error", "text", "setTimeout", "window", "electronAPI", "getComputerName", "trim", "toUpperCase", "e", "process", "env", "COMPUTERNAME", "HOSTNAME", "response", "fetch", "ok", "data", "json", "location", "hostname", "storedName", "localStorage", "getItem", "screen", "width", "height", "timezone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "language", "hashInput", "hash", "i", "length", "char", "charCodeAt", "Math", "abs", "toString", "slice", "setItem", "copyToClipboard", "clipboard", "writeText", "selectItem", "item", "deleteHistoryItem", "itemId", "updatedHistory", "filter", "startEditingThisDevice", "saveThisDeviceEdit", "newContent", "warn", "cancelThisDeviceEdit", "startEditingConnectedDevice", "saveConnectedDeviceEdit", "cancelConnectedDeviceEdit", "syncNow", "toggleAlwaysOnTop", "minimizeToTray", "toggleFloatingOverlay", "removeDevice", "deviceId", "updatedDevices", "connectDevice", "map", "disconnectDevice", "pairDevice", "deviceToPair", "find", "newPairedDevice", "refreshDiscovery", "foundDevices", "windowsDevices", "scanForWindowsDevices", "push", "androidDevices", "scanForAndroidDevices", "clipsyDevices", "scanForClipsyDevices", "Promise", "resolve", "deviceType", "scanQRCode", "generateQRCode", "connectionInfo", "port", "protocol", "Date", "now", "qrData", "JSON", "stringify", "qrCodeUrl", "encodeURIComponent", "qrModal", "document", "createElement", "className", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "closeModal", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "onclick", "target", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "value", "onChange", "placeholder", "autoFocus", "stopPropagation", "newComputerName", "disabled", "newValue", "checked", "max", "min", "_c", "$RefreshReg$"], "sources": ["D:/new git/Clipsy-Windows/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  // State management matching Android app\r\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to <PERSON>lipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\r\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\r\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\r\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\r\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\r\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\r\n\r\n  const [historyItems, setHistoryItems] = useState([\r\n    { id: '1', content: 'This is an older clipboard item. It\\'s shorter.', timestamp: '2 minutes ago' },\r\n    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },\r\n    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },\r\n    { id: '4', content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }', timestamp: '5 hours ago' }\r\n  ]);\r\n\r\n  // UI State\r\n  const [showSettings, setShowSettings] = useState(false);\r\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\r\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\r\n\r\n  // Background Sync State\r\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\r\n  const [networkDevices, setNetworkDevices] = useState([]);\r\n  const [isDiscovering, setIsDiscovering] = useState(false);\r\n\r\n  // Sync Settings - matching Android app\r\n  const [syncSettings, setSyncSettings] = useState({\r\n    autoSync: true,\r\n    syncDelay: 2,\r\n    syncOnConnect: true,\r\n    bidirectional: true\r\n  });\r\n\r\n  // Device Management - matching Android app\r\n  const [pairedDevices, setPairedDevices] = useState([\r\n    {\r\n      id: 'android-1',\r\n      name: 'Android Phone - Personal',\r\n      type: 'Android 14',\r\n      status: 'connected',\r\n      lastSeen: '2 min ago',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'linux-1',\r\n      name: 'Ubuntu Server - Home',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'disconnected',\r\n      lastSeen: '1 hour ago',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  const [discoveredDevices, setDiscoveredDevices] = useState([]);\r\n  const [isDeviceDiscoverable, setIsDeviceDiscoverable] = useState(true);\r\n\r\n  // Device info state - get actual Windows device details\r\n  const [deviceInfo, setDeviceInfo] = useState({\r\n    name: 'Windows PC - Loading...',\r\n    type: 'Windows',\r\n    status: 'active',\r\n    lastSeen: 'now',\r\n    ipAddress: '*************'\r\n  });\r\n\r\n  // Device discoverability service\r\n  React.useEffect(() => {\r\n    if (isDeviceDiscoverable) {\r\n      // Start discovery service\r\n      console.log('🔍 Device is now discoverable by other devices');\r\n      showMessage('🔍 This device is discoverable by other Android and Windows devices');\r\n    } else {\r\n      console.log('🔒 Device discovery disabled');\r\n    }\r\n  }, [isDeviceDiscoverable]);\r\n\r\n  // Get actual Windows computer name and OS details\r\n  React.useEffect(() => {\r\n    const getDeviceInfo = async () => {\r\n      try {\r\n        let osVersion = 'Windows';\r\n\r\n        // Get OS version from user agent\r\n        if (navigator.userAgentData) {\r\n          const platform = navigator.userAgentData.platform;\r\n          osVersion = platform || 'Windows';\r\n        } else if (navigator.userAgent) {\r\n          const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\r\n          if (windowsMatch) {\r\n            const version = windowsMatch[1];\r\n            switch (version) {\r\n              case '10.0': osVersion = 'Windows 10/11'; break;\r\n              case '6.3': osVersion = 'Windows 8.1'; break;\r\n              case '6.2': osVersion = 'Windows 8'; break;\r\n              case '6.1': osVersion = 'Windows 7'; break;\r\n              default: osVersion = `Windows NT ${version}`;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Get the actual computer name\r\n        const computerName = await getWindowsComputerName();\r\n        const deviceName = `${computerName} - ${osVersion}`;\r\n\r\n        // Check if any devices are connected\r\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\r\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\r\n\r\n        setDeviceInfo(prev => ({\r\n          ...prev,\r\n          name: deviceName,\r\n          type: osVersion,\r\n          status: deviceStatus,\r\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\r\n        }));\r\n\r\n        console.log('Device info updated:', { computerName, deviceName, osVersion });\r\n      } catch (error) {\r\n        console.log('Could not get device info:', error);\r\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\r\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\r\n\r\n        setDeviceInfo(prev => ({\r\n          ...prev,\r\n          name: 'Windows PC - Main',\r\n          type: 'Windows',\r\n          status: deviceStatus,\r\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\r\n        }));\r\n      }\r\n    };\r\n\r\n    getDeviceInfo();\r\n  }, [pairedDevices]);\r\n\r\n  // Functions\r\n  const showMessage = (text) => {\r\n    setSuccessMessage(text);\r\n    setTimeout(() => setSuccessMessage(''), 3000);\r\n  };\r\n\r\n  // Get Windows computer name using multiple methods\r\n  const getWindowsComputerName = async () => {\r\n    try {\r\n      // Method 1: Try PowerShell command (if available in Electron or similar environment)\r\n      if (window.electronAPI) {\r\n        try {\r\n          const computerName = await window.electronAPI.getComputerName();\r\n          if (computerName) return computerName.trim().toUpperCase();\r\n        } catch (e) {\r\n          console.log('Electron method failed:', e);\r\n        }\r\n      }\r\n\r\n      // Method 2: Try to get from environment variables (Node.js environment)\r\n      if (typeof process !== 'undefined' && process.env) {\r\n        if (process.env.COMPUTERNAME) return process.env.COMPUTERNAME.toUpperCase();\r\n        if (process.env.HOSTNAME) return process.env.HOSTNAME.toUpperCase();\r\n      }\r\n\r\n      // Method 3: Try to get from Windows registry via fetch (if CORS allows)\r\n      try {\r\n        const response = await fetch('/api/computer-name');\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          if (data.computerName) return data.computerName.toUpperCase();\r\n        }\r\n      } catch (e) {\r\n        console.log('API method failed:', e);\r\n      }\r\n\r\n      // Method 4: Use hostname if it's not localhost\r\n      if (window.location.hostname &&\r\n          window.location.hostname !== 'localhost' &&\r\n          window.location.hostname !== '127.0.0.1') {\r\n        return window.location.hostname.toUpperCase();\r\n      }\r\n\r\n      // Method 5: Generate persistent unique name\r\n      let storedName = localStorage.getItem('clipsy-computer-name');\r\n      if (!storedName) {\r\n        // Create a unique identifier based on browser characteristics\r\n        const userAgent = navigator.userAgent;\r\n        const screen = `${window.screen.width}x${window.screen.height}`;\r\n        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n        const language = navigator.language;\r\n\r\n        // Create a simple hash\r\n        const hashInput = `${userAgent}-${screen}-${timezone}-${language}`;\r\n        let hash = 0;\r\n        for (let i = 0; i < hashInput.length; i++) {\r\n          const char = hashInput.charCodeAt(i);\r\n          hash = ((hash << 5) - hash) + char;\r\n          hash = hash & hash; // Convert to 32-bit integer\r\n        }\r\n\r\n        storedName = `WINDOWS-PC-${Math.abs(hash).toString(16).toUpperCase().slice(0, 6)}`;\r\n        localStorage.setItem('clipsy-computer-name', storedName);\r\n      }\r\n\r\n      return storedName;\r\n    } catch (error) {\r\n      console.log('All computer name methods failed:', error);\r\n      return 'WINDOWS-PC';\r\n    }\r\n  };\r\n\r\n  const copyToClipboard = async (content) => {\r\n    try {\r\n      await navigator.clipboard.writeText(content);\r\n      setThisDeviceClipboard(content);\r\n      showMessage('✅ Text copied to clipboard!');\r\n    } catch (error) {\r\n      console.error('Failed to copy to clipboard:', error);\r\n      showMessage('❌ Failed to copy to clipboard');\r\n    }\r\n  };\r\n\r\n  const selectItem = (item) => {\r\n    copyToClipboard(item.content);\r\n    setConnectedDeviceClipboard(item.content);\r\n  };\r\n\r\n  const deleteHistoryItem = (itemId) => {\r\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\r\n    setHistoryItems(updatedHistory);\r\n    showMessage('🗑️ History item deleted!');\r\n  };\r\n\r\n  // Device edit functions\r\n  const startEditingThisDevice = () => {\r\n    setEditingThisDeviceText(thisDeviceClipboard);\r\n    setIsEditingThisDevice(true);\r\n  };\r\n\r\n  const saveThisDeviceEdit = async () => {\r\n    const newContent = editingThisDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setThisDeviceClipboard(newContent);\r\n    try {\r\n      await navigator.clipboard.writeText(newContent);\r\n    } catch (error) {\r\n      console.warn('Failed to update clipboard:', error);\r\n    }\r\n\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n    showMessage('✅ This Device clipboard updated!');\r\n  };\r\n\r\n  const cancelThisDeviceEdit = () => {\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n  };\r\n\r\n  const startEditingConnectedDevice = () => {\r\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\r\n    setIsEditingConnectedDevice(true);\r\n  };\r\n\r\n  const saveConnectedDeviceEdit = () => {\r\n    const newContent = editingConnectedDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setConnectedDeviceClipboard(newContent);\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n    showMessage('✅ Connected Device clipboard updated!');\r\n  };\r\n\r\n  const cancelConnectedDeviceEdit = () => {\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n  };\r\n\r\n  const syncNow = () => {\r\n    showMessage('🔄 Syncing with paired devices...');\r\n    setTimeout(() => {\r\n      showMessage('✅ Sync completed!');\r\n    }, 1000);\r\n  };\r\n\r\n  const toggleAlwaysOnTop = () => {\r\n    setIsAlwaysOnTop(!isAlwaysOnTop);\r\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\r\n  };\r\n\r\n  const minimizeToTray = () => {\r\n    showMessage('➖ Minimizing to background...');\r\n  };\r\n\r\n  const toggleFloatingOverlay = () => {\r\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\r\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\r\n  };\r\n\r\n  // Device Management Functions\r\n  const removeDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🗑️ Device removed from paired devices');\r\n  };\r\n\r\n  const connectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'connected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('✅ Device connected successfully');\r\n  };\r\n\r\n  const disconnectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'disconnected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🔌 Device disconnected');\r\n  };\r\n\r\n  const pairDevice = (deviceId) => {\r\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\r\n    if (deviceToPair) {\r\n      const newPairedDevice = {\r\n        ...deviceToPair,\r\n        status: 'connected',\r\n        lastSeen: 'Just now'\r\n      };\r\n      setPairedDevices([...pairedDevices, newPairedDevice]);\r\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\r\n    }\r\n  };\r\n\r\n  const refreshDiscovery = async () => {\r\n    if (isDiscovering) return;\r\n\r\n    setIsDiscovering(true);\r\n    setNetworkDevices([]); // Clear previous results\r\n    setDiscoveredDevices([]); // Clear discovered devices\r\n    showMessage('🔍 Searching for Android and Windows devices...');\r\n\r\n    try {\r\n      // Start device discovery process\r\n      const foundDevices = [];\r\n\r\n      // Phase 1: Network scanning for Windows devices\r\n      showMessage('🖥️ Scanning for Windows devices...');\r\n      const windowsDevices = await scanForWindowsDevices();\r\n      foundDevices.push(...windowsDevices);\r\n\r\n      // Update UI with Windows devices found\r\n      if (windowsDevices.length > 0) {\r\n        setNetworkDevices(prev => [...prev, ...windowsDevices]);\r\n        setDiscoveredDevices(prev => [...prev, ...windowsDevices]);\r\n        showMessage(`💻 Found ${windowsDevices.length} Windows device(s)`);\r\n      }\r\n\r\n      // Phase 2: Broadcast discovery for Android devices\r\n      showMessage('📱 Scanning for Android devices...');\r\n      const androidDevices = await scanForAndroidDevices();\r\n      foundDevices.push(...androidDevices);\r\n\r\n      // Update UI with Android devices found\r\n      if (androidDevices.length > 0) {\r\n        setNetworkDevices(prev => [...prev, ...androidDevices]);\r\n        setDiscoveredDevices(prev => [...prev, ...androidDevices]);\r\n        showMessage(`📱 Found ${androidDevices.length} Android device(s)`);\r\n      }\r\n\r\n      // Phase 3: mDNS/Bonjour discovery for all Clipsy devices\r\n      showMessage('🔍 Scanning for Clipsy-enabled devices...');\r\n      const clipsyDevices = await scanForClipsyDevices();\r\n      foundDevices.push(...clipsyDevices);\r\n\r\n      // Update UI with Clipsy devices found\r\n      if (clipsyDevices.length > 0) {\r\n        setNetworkDevices(prev => [...prev, ...clipsyDevices]);\r\n        setDiscoveredDevices(prev => [...prev, ...clipsyDevices]);\r\n        showMessage(`🔗 Found ${clipsyDevices.length} Clipsy-enabled device(s)`);\r\n      }\r\n\r\n      // Final results\r\n      setIsDiscovering(false);\r\n      if (foundDevices.length > 0) {\r\n        showMessage(`✅ Discovery complete! Found ${foundDevices.length} available devices.`);\r\n      } else {\r\n        showMessage('❌ No devices found. Make sure devices are on the same network.');\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('Discovery error:', error);\r\n      setIsDiscovering(false);\r\n      showMessage('❌ Discovery failed. Please check network connection.');\r\n    }\r\n  };\r\n\r\n  // Device scanning functions\r\n  const scanForWindowsDevices = async () => {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        const windowsDevices = [\r\n          {\r\n            id: 'windows-laptop-001',\r\n            name: 'LAPTOP-WORK123',\r\n            type: 'Windows 11 Pro',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'windows'\r\n          },\r\n          {\r\n            id: 'windows-desktop-001',\r\n            name: 'DESKTOP-GAMING',\r\n            type: 'Windows 10',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'windows'\r\n          },\r\n          {\r\n            id: 'windows-surface-001',\r\n            name: 'SURFACE-PRO9',\r\n            type: 'Windows 11',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'windows'\r\n          }\r\n        ];\r\n        resolve(windowsDevices);\r\n      }, 1500);\r\n    });\r\n  };\r\n\r\n  const scanForAndroidDevices = async () => {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        const androidDevices = [\r\n          {\r\n            id: 'android-samsung-001',\r\n            name: 'Samsung Galaxy S23',\r\n            type: 'Android 14',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'android'\r\n          },\r\n          {\r\n            id: 'android-oneplus-001',\r\n            name: 'OnePlus 11',\r\n            type: 'Android 14',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'android'\r\n          },\r\n          {\r\n            id: 'android-pixel-001',\r\n            name: 'Google Pixel 8',\r\n            type: 'Android 14',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'android'\r\n          }\r\n        ];\r\n        resolve(androidDevices);\r\n      }, 2000);\r\n    });\r\n  };\r\n\r\n  const scanForClipsyDevices = async () => {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        const clipsyDevices = [\r\n          {\r\n            id: 'clipsy-tablet-001',\r\n            name: 'iPad Pro (Clipsy)',\r\n            type: 'iPadOS 17',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'ios'\r\n          },\r\n          {\r\n            id: 'clipsy-mac-001',\r\n            name: 'MacBook Pro M3',\r\n            type: 'macOS Sonoma',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'macos'\r\n          }\r\n        ];\r\n        resolve(clipsyDevices);\r\n      }, 1000);\r\n    });\r\n  };\r\n\r\n  const scanQRCode = () => {\r\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\r\n  };\r\n\r\n  const generateQRCode = () => {\r\n    // Generate connection info for QR code\r\n    const connectionInfo = {\r\n      deviceName: deviceInfo.name,\r\n      deviceType: deviceInfo.type,\r\n      ipAddress: deviceInfo.ipAddress,\r\n      port: 3001,\r\n      protocol: 'clipsy-sync',\r\n      timestamp: Date.now()\r\n    };\r\n\r\n    const qrData = JSON.stringify(connectionInfo);\r\n    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrData)}`;\r\n\r\n    // Create and show QR code modal\r\n    const qrModal = document.createElement('div');\r\n    qrModal.className = 'qr-modal-overlay';\r\n    qrModal.innerHTML = `\r\n      <div class=\"qr-modal-content\">\r\n        <div class=\"qr-modal-header\">\r\n          <h3>📱 Scan to Connect Android Device</h3>\r\n          <button class=\"qr-modal-close\">✕</button>\r\n        </div>\r\n        <div class=\"qr-modal-body\">\r\n          <img src=\"${qrCodeUrl}\" alt=\"QR Code\" class=\"qr-code-image\" />\r\n          <p class=\"qr-instructions\">\r\n            1. Open Clipsy app on your Android device<br/>\r\n            2. Go to Settings → Device Discovery<br/>\r\n            3. Tap \"Scan QR\" and scan this code<br/>\r\n            4. Your devices will be paired automatically\r\n          </p>\r\n          <div class=\"qr-device-info\">\r\n            <p><strong>Device:</strong> ${deviceInfo.name}</p>\r\n            <p><strong>IP:</strong> ${deviceInfo.ipAddress}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    `;\r\n\r\n    document.body.appendChild(qrModal);\r\n\r\n    // Close modal functionality\r\n    const closeModal = () => {\r\n      document.body.removeChild(qrModal);\r\n    };\r\n\r\n    qrModal.querySelector('.qr-modal-close').onclick = closeModal;\r\n    qrModal.onclick = (e) => {\r\n      if (e.target === qrModal) closeModal();\r\n    };\r\n\r\n    showMessage('📱 QR Code generated! Scan with Android Clipsy app to connect.');\r\n  };\r\n\r\n  return (\r\n    <div className=\"app-container\">\r\n      {/* Header */}\r\n      <div className=\"header\">\r\n        <div className=\"title-container\">\r\n          <div className=\"logo-container\">\r\n            <img\r\n              src=\"/clipsy-logo-no-bg.png\"\r\n              alt=\"Clipsy Logo\"\r\n              className=\"app-logo\"\r\n            />\r\n            <div className={`connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`}></div>\r\n          </div>\r\n          <h1 className=\"title\">Clipsy</h1>\r\n        </div>\r\n        <div className=\"header-actions\">\r\n          <button\r\n            className={`icon-button ${isFloatingOverlayVisible ? 'active' : ''}`}\r\n            onClick={toggleFloatingOverlay}\r\n            title=\"Toggle floating clipboard widget\"\r\n          >\r\n            📋\r\n          </button>\r\n          <button\r\n            className={`icon-button ${isAlwaysOnTop ? 'active' : ''}`}\r\n            onClick={toggleAlwaysOnTop}\r\n            title=\"Pin to top\"\r\n          >\r\n            <div className=\"pin-icon\">\r\n              <div className=\"pin-head\"></div>\r\n              <div className=\"pin-body\"></div>\r\n            </div>\r\n          </button>\r\n          <button className=\"icon-button\" onClick={minimizeToTray} title=\"Minimize\">\r\n            ➖\r\n          </button>\r\n          <button className=\"icon-button\" onClick={() => setShowSettings(true)} title=\"Settings\">\r\n            ⚙️\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"success-message\">\r\n          <span>{successMessage}</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      <div className=\"main-content\">\r\n        {/* This Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <div className=\"device-section-title-container\">\r\n              <h2 className=\"device-section-title\">💻 This Device</h2>\r\n              <p className=\"device-section-subtitle\">{deviceInfo.name}</p>\r\n            </div>\r\n          </div>\r\n\r\n          {isEditingThisDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingThisDeviceText}\r\n                onChange={(e) => setEditingThisDeviceText(e.target.value)}\r\n                placeholder=\"Edit this device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button this-device\" onClick={saveThisDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button this-device\" onClick={cancelThisDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content this-device-content\"\r\n                onClick={() => copyToClipboard(thisDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{thisDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Real-time sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingThisDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Connected Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <h2 className=\"device-section-title\">🔗 Connected Device</h2>\r\n            <p className=\"device-section-subtitle\">Android Phone - Personal 🟢</p>\r\n          </div>\r\n\r\n          {isEditingConnectedDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingConnectedDeviceText}\r\n                onChange={(e) => setEditingConnectedDeviceText(e.target.value)}\r\n                placeholder=\"Edit connected device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button connected-device\" onClick={saveConnectedDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button connected-device\" onClick={cancelConnectedDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content connected-device-content\"\r\n                onClick={() => copyToClipboard(connectedDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{connectedDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Bidirectional sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingConnectedDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Clipboard History */}\r\n        <h2 className=\"history-title\">Clipboard History</h2>\r\n        <div className=\"history-list\">\r\n          {historyItems.map((item) => (\r\n            <div\r\n              key={item.id}\r\n              className=\"history-item\"\r\n              onClick={() => selectItem(item)}\r\n            >\r\n              <div className=\"history-item-header\">\r\n                <span className=\"timestamp\">{item.timestamp}</span>\r\n                <button\r\n                  className=\"delete-button\"\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    deleteHistoryItem(item.id);\r\n                  }}\r\n                >\r\n                  ✕\r\n                </button>\r\n              </div>\r\n              <p className=\"item-content\">{item.content}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Floating Sync Button */}\r\n      <button className=\"floating-sync-button\" onClick={syncNow}>\r\n        <img src=\"/sync.png\" alt=\"Sync\" className=\"sync-icon\" />\r\n      </button>\r\n\r\n      {/* Settings Modal */}\r\n      {showSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSettings(false)}>\r\n          <div className=\"settings-sidebar\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              {/* Device Info */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Device Info</h3>\r\n                <div className=\"device-info-card\">\r\n                  <p className=\"device-name\">{deviceInfo.name}</p>\r\n                  <p className=\"device-detail\">{deviceInfo.type}</p>\r\n                  <p className=\"device-detail\">IP: {deviceInfo.ipAddress}</p>\r\n                  <div className=\"device-status-row\">\r\n                    <div className={`device-status-indicator ${deviceInfo.status}`}></div>\r\n                    <span className={`device-status-text ${deviceInfo.status}`}>\r\n                      Status: {deviceInfo.status === 'active' ? 'Connected' : 'Disconnected'}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"device-status-row\">\r\n                    <div className={`device-status-indicator ${isDeviceDiscoverable ? 'active' : 'disconnected'}`}></div>\r\n                    <span className={`device-status-text ${isDeviceDiscoverable ? 'active' : 'disconnected'}`}>\r\n                      Discoverable: {isDeviceDiscoverable ? 'Visible to other devices' : 'Hidden from discovery'}\r\n                    </span>\r\n                  </div>\r\n                  {deviceInfo.status === 'disconnected' && (\r\n                    <p className=\"device-detail disconnected-notice\">\r\n                      ⚠️ No devices connected - Use QR code or device discovery to connect Android devices\r\n                    </p>\r\n                  )}\r\n                  <button\r\n                    className=\"refresh-device-name-button\"\r\n                    onClick={async () => {\r\n                      const newComputerName = await getWindowsComputerName();\r\n                      showMessage(`🖥️ Computer name refreshed: ${newComputerName}`);\r\n                      // Trigger device info refresh\r\n                      const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\r\n                      const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\r\n                      let osVersion = 'Windows';\r\n                      if (navigator.userAgent) {\r\n                        const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\r\n                        if (windowsMatch) {\r\n                          const version = windowsMatch[1];\r\n                          switch (version) {\r\n                            case '10.0': osVersion = 'Windows 10/11'; break;\r\n                            case '6.3': osVersion = 'Windows 8.1'; break;\r\n                            case '6.2': osVersion = 'Windows 8'; break;\r\n                            case '6.1': osVersion = 'Windows 7'; break;\r\n                            default: osVersion = `Windows NT ${version}`;\r\n                          }\r\n                        }\r\n                      }\r\n                      setDeviceInfo(prev => ({\r\n                        ...prev,\r\n                        name: `${newComputerName} - ${osVersion}`,\r\n                        type: osVersion,\r\n                        status: deviceStatus,\r\n                        lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\r\n                      }));\r\n                    }}\r\n                  >\r\n                    🔄 Refresh Computer Name\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Paired Devices */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Paired Devices</h3>\r\n                {pairedDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No paired devices found</p>\r\n                    <p className=\"empty-device-subtext\">Use QR code or device discovery to pair devices</p>\r\n                  </div>\r\n                ) : (\r\n                  pairedDevices.map((device) => (\r\n                    <div key={device.id} className=\"enhanced-device-card\">\r\n                      {/* Remove Button - White X at top right corner */}\r\n                      <button\r\n                        className=\"remove-button-top-right\"\r\n                        onClick={() => removeDevice(device.id)}\r\n                      >\r\n                        ×\r\n                      </button>\r\n\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <div className=\"device-status-row\">\r\n                          <div className={`device-status-indicator ${device.status}`}></div>\r\n                          <span className={`device-status-text ${device.status}`}>\r\n                            {device.status === 'connected' ? 'Connected' : 'Disconnected'} • {device.lastSeen}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Connect/Disconnect Button - Bottom Right Corner */}\r\n                      <button\r\n                        className={`device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`}\r\n                        onClick={() => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id)}\r\n                      >\r\n                        {device.status === 'connected' ? 'Disconnect' : 'Connect'}\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n              </div>\r\n\r\n              {/* Device Discovery */}\r\n              <div className=\"settings-section\">\r\n                <div className=\"section-header\">\r\n                  <h3 className=\"section-title\">Device Discovery</h3>\r\n                  <button\r\n                    className={`discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`}\r\n                    onClick={refreshDiscovery}\r\n                    disabled={isDiscovering}\r\n                  >\r\n                    {isDiscovering ? '🔍 Scanning...' : 'Discover'}\r\n                  </button>\r\n                </div>\r\n                {networkDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No devices found</p>\r\n                    <p className=\"empty-device-subtext\">\r\n                      {isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'}\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  networkDevices.map((device) => (\r\n                    <div key={device.id} className=\"discovered-device-card\">\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <p className=\"device-card-last-seen\">{device.lastSeen}</p>\r\n                      </div>\r\n                      <button\r\n                        className=\"pair-button\"\r\n                        onClick={() => pairDevice(device.id)}\r\n                      >\r\n                        Pair\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n\r\n                <div className=\"qr-buttons-container\">\r\n                  <button className=\"qr-generate-button\" onClick={generateQRCode}>\r\n                    Generate QR\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Additional Settings */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Additional Settings</h3>\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => setShowSyncSettings(true)}\r\n                >\r\n                  <span className=\"setting-item-text\">Sync Settings</span>\r\n                  <span className=\"setting-item-arrow\">›</span>\r\n                </button>\r\n\r\n                {/* Background Sync Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    const newValue = !backgroundSyncEnabled;\r\n                    setBackgroundSyncEnabled(newValue);\r\n                    showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Background Sync</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {backgroundSyncEnabled ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n\r\n                {/* Network Discovery Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    if (isDiscovering) {\r\n                      setIsDiscovering(false);\r\n                      showMessage('Network discovery stopped');\r\n                    } else {\r\n                      setIsDiscovering(true);\r\n                      showMessage('Network discovery started');\r\n                    }\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Network Discovery</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {isDiscovering ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Sync Settings Modal */}\r\n      {showSyncSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSyncSettings(false)}>\r\n          <div className=\"sync-settings-modal\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Sync Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSyncSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              <div className=\"settings-section\">\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Auto Sync</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.autoSync}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, autoSync: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Sync Delay: {syncSettings.syncDelay} seconds</label>\r\n                  <p className=\"sync-setting-description\">\r\n                    {syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`}\r\n                  </p>\r\n                  <div className=\"sync-delay-controls\">\r\n                    <button\r\n                      className=\"sync-delay-button\"\r\n                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.max(0, syncSettings.syncDelay - 1)})}\r\n                    >\r\n                      -\r\n                    </button>\r\n                    <span className=\"sync-delay-value\">{syncSettings.syncDelay}s</span>\r\n                    <button\r\n                      className=\"sync-delay-button\"\r\n                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.min(30, syncSettings.syncDelay + 1)})}\r\n                    >\r\n                      +\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Sync on Connect</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.syncOnConnect}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, syncOnConnect: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Bidirectional Sync</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.bidirectional}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, bidirectional: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                {/* Cross-Platform Sync Controls */}\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.')}\r\n                  >\r\n                    📱 Windows ↔ Android Sync\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.')}\r\n                  >\r\n                    🖥️ Windows ↔ Windows Sync\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.')}\r\n                  >\r\n                    🚀 Sync All Devices\r\n                  </button>\r\n                </div>\r\n\r\n                {/* Floating Overlay Button Settings */}\r\n                <div className=\"settings-section\">\r\n                  <h3 className=\"settings-section-title\">📋 Floating Overlay Button Settings</h3>\r\n                  <p className=\"settings-description\">\r\n                    Configure the floating overlay button for quick access to connected device clipboards\r\n                  </p>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Enable Floating Overlay Button</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('📋 Floating overlay button is always enabled for accessibility')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Show Device Count Badge</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('🔢 Device count badge enabled')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Auto-hide After Copy</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('⏱️ Auto-hide after copy enabled')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <p className=\"settings-note\">\r\n                    💡 The floating overlay button (📋) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGP,QAAQ,CAAC,sIAAsI,CAAC;EACtM,MAAM,CAACQ,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGT,QAAQ,CAAC,0IAA0I,CAAC;EACpN,MAAM,CAACU,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACY,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACc,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACgB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEhF,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAC/C;IAAEoB,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,iDAAiD;IAAEC,SAAS,EAAE;EAAgB,CAAC,EACnG;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,gLAAgL;IAAEC,SAAS,EAAE;EAAiB,CAAC,EACnO;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,+BAA+B;IAAEC,SAAS,EAAE;EAAa,CAAC,EAC9E;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,sEAAsE;IAAEC,SAAS,EAAE;EAAc,CAAC,CACvH,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+B,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;;EAE/E;EACA,MAAM,CAACiC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC;IAC/CyC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,CACjD;IACEoB,EAAE,EAAE,WAAW;IACf2B,IAAI,EAAE,0BAA0B;IAChCC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,SAAS;IACb2B,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,cAAc;IACtBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACsD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC;IAC3C+C,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACApD,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,IAAIqD,oBAAoB,EAAE;MACxB;MACAI,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7DC,WAAW,CAAC,qEAAqE,CAAC;IACpF,CAAC,MAAM;MACLF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC,EAAE,CAACL,oBAAoB,CAAC,CAAC;;EAE1B;EACAvD,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAM4D,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,IAAIC,SAAS,GAAG,SAAS;;QAEzB;QACA,IAAIC,SAAS,CAACC,aAAa,EAAE;UAC3B,MAAMC,QAAQ,GAAGF,SAAS,CAACC,aAAa,CAACC,QAAQ;UACjDH,SAAS,GAAGG,QAAQ,IAAI,SAAS;QACnC,CAAC,MAAM,IAAIF,SAAS,CAACG,SAAS,EAAE;UAC9B,MAAMC,YAAY,GAAGJ,SAAS,CAACG,SAAS,CAACE,KAAK,CAAC,uBAAuB,CAAC;UACvE,IAAID,YAAY,EAAE;YAChB,MAAME,OAAO,GAAGF,YAAY,CAAC,CAAC,CAAC;YAC/B,QAAQE,OAAO;cACb,KAAK,MAAM;gBAAEP,SAAS,GAAG,eAAe;gBAAE;cAC1C,KAAK,KAAK;gBAAEA,SAAS,GAAG,aAAa;gBAAE;cACvC,KAAK,KAAK;gBAAEA,SAAS,GAAG,WAAW;gBAAE;cACrC,KAAK,KAAK;gBAAEA,SAAS,GAAG,WAAW;gBAAE;cACrC;gBAASA,SAAS,GAAG,cAAcO,OAAO,EAAE;YAC9C;UACF;QACF;;QAEA;QACA,MAAMC,YAAY,GAAG,MAAMC,sBAAsB,CAAC,CAAC;QACnD,MAAMC,UAAU,GAAG,GAAGF,YAAY,MAAMR,SAAS,EAAE;;QAEnD;QACA,MAAMW,mBAAmB,GAAG5B,aAAa,CAAC6B,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAC1B,MAAM,KAAK,WAAW,CAAC;QACvF,MAAM2B,YAAY,GAAGH,mBAAmB,GAAG,QAAQ,GAAG,cAAc;QAEpEhB,aAAa,CAACoB,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP9B,IAAI,EAAEyB,UAAU;UAChBxB,IAAI,EAAEc,SAAS;UACfb,MAAM,EAAE2B,YAAY;UACpB1B,QAAQ,EAAE0B,YAAY,KAAK,QAAQ,GAAG,KAAK,GAAG;QAChD,CAAC,CAAC,CAAC;QAEHlB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;UAAEW,YAAY;UAAEE,UAAU;UAAEV;QAAU,CAAC,CAAC;MAC9E,CAAC,CAAC,OAAOgB,KAAK,EAAE;QACdpB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEmB,KAAK,CAAC;QAChD,MAAML,mBAAmB,GAAG5B,aAAa,CAAC6B,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAC1B,MAAM,KAAK,WAAW,CAAC;QACvF,MAAM2B,YAAY,GAAGH,mBAAmB,GAAG,QAAQ,GAAG,cAAc;QAEpEhB,aAAa,CAACoB,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP9B,IAAI,EAAE,mBAAmB;UACzBC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE2B,YAAY;UACpB1B,QAAQ,EAAE0B,YAAY,KAAK,QAAQ,GAAG,KAAK,GAAG;QAChD,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAEDf,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAChB,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMe,WAAW,GAAImB,IAAI,IAAK;IAC5BjD,iBAAiB,CAACiD,IAAI,CAAC;IACvBC,UAAU,CAAC,MAAMlD,iBAAiB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMyC,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF;MACA,IAAIU,MAAM,CAACC,WAAW,EAAE;QACtB,IAAI;UACF,MAAMZ,YAAY,GAAG,MAAMW,MAAM,CAACC,WAAW,CAACC,eAAe,CAAC,CAAC;UAC/D,IAAIb,YAAY,EAAE,OAAOA,YAAY,CAACc,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC5D,CAAC,CAAC,OAAOC,CAAC,EAAE;UACV5B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE2B,CAAC,CAAC;QAC3C;MACF;;MAEA;MACA,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,GAAG,EAAE;QACjD,IAAID,OAAO,CAACC,GAAG,CAACC,YAAY,EAAE,OAAOF,OAAO,CAACC,GAAG,CAACC,YAAY,CAACJ,WAAW,CAAC,CAAC;QAC3E,IAAIE,OAAO,CAACC,GAAG,CAACE,QAAQ,EAAE,OAAOH,OAAO,CAACC,GAAG,CAACE,QAAQ,CAACL,WAAW,CAAC,CAAC;MACrE;;MAEA;MACA,IAAI;QACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,oBAAoB,CAAC;QAClD,IAAID,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;UAClC,IAAID,IAAI,CAACxB,YAAY,EAAE,OAAOwB,IAAI,CAACxB,YAAY,CAACe,WAAW,CAAC,CAAC;QAC/D;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV5B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2B,CAAC,CAAC;MACtC;;MAEA;MACA,IAAIL,MAAM,CAACe,QAAQ,CAACC,QAAQ,IACxBhB,MAAM,CAACe,QAAQ,CAACC,QAAQ,KAAK,WAAW,IACxChB,MAAM,CAACe,QAAQ,CAACC,QAAQ,KAAK,WAAW,EAAE;QAC5C,OAAOhB,MAAM,CAACe,QAAQ,CAACC,QAAQ,CAACZ,WAAW,CAAC,CAAC;MAC/C;;MAEA;MACA,IAAIa,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;MAC7D,IAAI,CAACF,UAAU,EAAE;QACf;QACA,MAAMhC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAMmC,MAAM,GAAG,GAAGpB,MAAM,CAACoB,MAAM,CAACC,KAAK,IAAIrB,MAAM,CAACoB,MAAM,CAACE,MAAM,EAAE;QAC/D,MAAMC,QAAQ,GAAGC,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACC,QAAQ;QACjE,MAAMC,QAAQ,GAAG9C,SAAS,CAAC8C,QAAQ;;QAEnC;QACA,MAAMC,SAAS,GAAG,GAAG5C,SAAS,IAAImC,MAAM,IAAIG,QAAQ,IAAIK,QAAQ,EAAE;QAClE,IAAIE,IAAI,GAAG,CAAC;QACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACzC,MAAME,IAAI,GAAGJ,SAAS,CAACK,UAAU,CAACH,CAAC,CAAC;UACpCD,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAIG,IAAI;UAClCH,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC,CAAC;QACtB;QAEAb,UAAU,GAAG,cAAckB,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC,CAACO,QAAQ,CAAC,EAAE,CAAC,CAACjC,WAAW,CAAC,CAAC,CAACkC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAClFpB,YAAY,CAACqB,OAAO,CAAC,sBAAsB,EAAEtB,UAAU,CAAC;MAC1D;MAEA,OAAOA,UAAU;IACnB,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdpB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEmB,KAAK,CAAC;MACvD,OAAO,YAAY;IACrB;EACF,CAAC;EAED,MAAM2C,eAAe,GAAG,MAAOpG,OAAO,IAAK;IACzC,IAAI;MACF,MAAM0C,SAAS,CAAC2D,SAAS,CAACC,SAAS,CAACtG,OAAO,CAAC;MAC5Cd,sBAAsB,CAACc,OAAO,CAAC;MAC/BuC,WAAW,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDlB,WAAW,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMgE,UAAU,GAAIC,IAAI,IAAK;IAC3BJ,eAAe,CAACI,IAAI,CAACxG,OAAO,CAAC;IAC7BZ,2BAA2B,CAACoH,IAAI,CAACxG,OAAO,CAAC;EAC3C,CAAC;EAED,MAAMyG,iBAAiB,GAAIC,MAAM,IAAK;IACpC,MAAMC,cAAc,GAAG9G,YAAY,CAAC+G,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAACzG,EAAE,KAAK2G,MAAM,CAAC;IACtE5G,eAAe,CAAC6G,cAAc,CAAC;IAC/BpE,WAAW,CAAC,2BAA2B,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMsE,sBAAsB,GAAGA,CAAA,KAAM;IACnCnH,wBAAwB,CAACT,mBAAmB,CAAC;IAC7CK,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMwH,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,UAAU,GAAGtH,qBAAqB,CAACsE,IAAI,CAAC,CAAC;IAC/C,IAAI,CAACgD,UAAU,EAAE;MACfxE,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEArD,sBAAsB,CAAC6H,UAAU,CAAC;IAClC,IAAI;MACF,MAAMrE,SAAS,CAAC2D,SAAS,CAACC,SAAS,CAACS,UAAU,CAAC;IACjD,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdpB,OAAO,CAAC2E,IAAI,CAAC,6BAA6B,EAAEvD,KAAK,CAAC;IACpD;IAEAnE,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;IAC5B6C,WAAW,CAAC,kCAAkC,CAAC;EACjD,CAAC;EAED,MAAM0E,oBAAoB,GAAGA,CAAA,KAAM;IACjC3H,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;EAC9B,CAAC;EAED,MAAMwH,2BAA2B,GAAGA,CAAA,KAAM;IACxCtH,6BAA6B,CAACT,wBAAwB,CAAC;IACvDK,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAM2H,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMJ,UAAU,GAAGpH,0BAA0B,CAACoE,IAAI,CAAC,CAAC;IACpD,IAAI,CAACgD,UAAU,EAAE;MACfxE,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEAnD,2BAA2B,CAAC2H,UAAU,CAAC;IACvCvH,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;IACjC2C,WAAW,CAAC,uCAAuC,CAAC;EACtD,CAAC;EAED,MAAM6E,yBAAyB,GAAGA,CAAA,KAAM;IACtC5H,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;EACnC,CAAC;EAED,MAAMyH,OAAO,GAAGA,CAAA,KAAM;IACpB9E,WAAW,CAAC,mCAAmC,CAAC;IAChDoB,UAAU,CAAC,MAAM;MACfpB,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM+E,iBAAiB,GAAGA,CAAA,KAAM;IAC9B/G,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChCiC,WAAW,CAACjC,aAAa,GAAG,0BAA0B,GAAG,sBAAsB,CAAC;EAClF,CAAC;EAED,MAAMiH,cAAc,GAAGA,CAAA,KAAM;IAC3BhF,WAAW,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAMiF,qBAAqB,GAAGA,CAAA,KAAM;IAClC7G,2BAA2B,CAAC,CAACD,wBAAwB,CAAC;IACtD6B,WAAW,CAAC7B,wBAAwB,GAAG,2BAA2B,GAAG,0BAA0B,CAAC;EAClG,CAAC;;EAED;EACA,MAAM+G,YAAY,GAAIC,QAAQ,IAAK;IACjC,MAAMC,cAAc,GAAGnG,aAAa,CAACoF,MAAM,CAACtD,MAAM,IAAIA,MAAM,CAACvD,EAAE,KAAK2H,QAAQ,CAAC;IAC7EjG,gBAAgB,CAACkG,cAAc,CAAC;IAChCpF,WAAW,CAAC,wCAAwC,CAAC;EACvD,CAAC;EAED,MAAMqF,aAAa,GAAIF,QAAQ,IAAK;IAClC,MAAMC,cAAc,GAAGnG,aAAa,CAACqG,GAAG,CAACvE,MAAM,IAC7CA,MAAM,CAACvD,EAAE,KAAK2H,QAAQ,GAAG;MAAE,GAAGpE,MAAM;MAAE1B,MAAM,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAW,CAAC,GAAGyB,MACtF,CAAC;IACD7B,gBAAgB,CAACkG,cAAc,CAAC;IAChCpF,WAAW,CAAC,iCAAiC,CAAC;EAChD,CAAC;EAED,MAAMuF,gBAAgB,GAAIJ,QAAQ,IAAK;IACrC,MAAMC,cAAc,GAAGnG,aAAa,CAACqG,GAAG,CAACvE,MAAM,IAC7CA,MAAM,CAACvD,EAAE,KAAK2H,QAAQ,GAAG;MAAE,GAAGpE,MAAM;MAAE1B,MAAM,EAAE,cAAc;MAAEC,QAAQ,EAAE;IAAW,CAAC,GAAGyB,MACzF,CAAC;IACD7B,gBAAgB,CAACkG,cAAc,CAAC;IAChCpF,WAAW,CAAC,wBAAwB,CAAC;EACvC,CAAC;EAED,MAAMwF,UAAU,GAAIL,QAAQ,IAAK;IAC/B,MAAMM,YAAY,GAAGjG,iBAAiB,CAACkG,IAAI,CAAC3E,MAAM,IAAIA,MAAM,CAACvD,EAAE,KAAK2H,QAAQ,CAAC;IAC7E,IAAIM,YAAY,EAAE;MAChB,MAAME,eAAe,GAAG;QACtB,GAAGF,YAAY;QACfpG,MAAM,EAAE,WAAW;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDJ,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE0G,eAAe,CAAC,CAAC;MACrD3F,WAAW,CAAC,8BAA8ByF,YAAY,CAACtG,IAAI,EAAE,CAAC;IAChE;EACF,CAAC;EAED,MAAMyG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAInH,aAAa,EAAE;IAEnBC,gBAAgB,CAAC,IAAI,CAAC;IACtBF,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;IACvBiB,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1BO,WAAW,CAAC,iDAAiD,CAAC;IAE9D,IAAI;MACF;MACA,MAAM6F,YAAY,GAAG,EAAE;;MAEvB;MACA7F,WAAW,CAAC,qCAAqC,CAAC;MAClD,MAAM8F,cAAc,GAAG,MAAMC,qBAAqB,CAAC,CAAC;MACpDF,YAAY,CAACG,IAAI,CAAC,GAAGF,cAAc,CAAC;;MAEpC;MACA,IAAIA,cAAc,CAACzC,MAAM,GAAG,CAAC,EAAE;QAC7B7E,iBAAiB,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAG6E,cAAc,CAAC,CAAC;QACvDrG,oBAAoB,CAACwB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAG6E,cAAc,CAAC,CAAC;QAC1D9F,WAAW,CAAC,YAAY8F,cAAc,CAACzC,MAAM,oBAAoB,CAAC;MACpE;;MAEA;MACArD,WAAW,CAAC,oCAAoC,CAAC;MACjD,MAAMiG,cAAc,GAAG,MAAMC,qBAAqB,CAAC,CAAC;MACpDL,YAAY,CAACG,IAAI,CAAC,GAAGC,cAAc,CAAC;;MAEpC;MACA,IAAIA,cAAc,CAAC5C,MAAM,GAAG,CAAC,EAAE;QAC7B7E,iBAAiB,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGgF,cAAc,CAAC,CAAC;QACvDxG,oBAAoB,CAACwB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGgF,cAAc,CAAC,CAAC;QAC1DjG,WAAW,CAAC,YAAYiG,cAAc,CAAC5C,MAAM,oBAAoB,CAAC;MACpE;;MAEA;MACArD,WAAW,CAAC,2CAA2C,CAAC;MACxD,MAAMmG,aAAa,GAAG,MAAMC,oBAAoB,CAAC,CAAC;MAClDP,YAAY,CAACG,IAAI,CAAC,GAAGG,aAAa,CAAC;;MAEnC;MACA,IAAIA,aAAa,CAAC9C,MAAM,GAAG,CAAC,EAAE;QAC5B7E,iBAAiB,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGkF,aAAa,CAAC,CAAC;QACtD1G,oBAAoB,CAACwB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGkF,aAAa,CAAC,CAAC;QACzDnG,WAAW,CAAC,YAAYmG,aAAa,CAAC9C,MAAM,2BAA2B,CAAC;MAC1E;;MAEA;MACA3E,gBAAgB,CAAC,KAAK,CAAC;MACvB,IAAImH,YAAY,CAACxC,MAAM,GAAG,CAAC,EAAE;QAC3BrD,WAAW,CAAC,+BAA+B6F,YAAY,CAACxC,MAAM,qBAAqB,CAAC;MACtF,CAAC,MAAM;QACLrD,WAAW,CAAC,gEAAgE,CAAC;MAC/E;IAEF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCxC,gBAAgB,CAAC,KAAK,CAAC;MACvBsB,WAAW,CAAC,sDAAsD,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAM+F,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,OAAO,IAAIM,OAAO,CAAEC,OAAO,IAAK;MAC9BlF,UAAU,CAAC,MAAM;QACf,MAAM0E,cAAc,GAAG,CACrB;UACEtI,EAAE,EAAE,oBAAoB;UACxB2B,IAAI,EAAE,gBAAgB;UACtBC,IAAI,EAAE,gBAAgB;UACtBC,MAAM,EAAE,aAAa;UACrBC,QAAQ,EAAE,uBAAuB;UACjCC,SAAS,EAAE,eAAe;UAC1BgH,UAAU,EAAE;QACd,CAAC,EACD;UACE/I,EAAE,EAAE,qBAAqB;UACzB2B,IAAI,EAAE,gBAAgB;UACtBC,IAAI,EAAE,YAAY;UAClBC,MAAM,EAAE,aAAa;UACrBC,QAAQ,EAAE,uBAAuB;UACjCC,SAAS,EAAE,eAAe;UAC1BgH,UAAU,EAAE;QACd,CAAC,EACD;UACE/I,EAAE,EAAE,qBAAqB;UACzB2B,IAAI,EAAE,cAAc;UACpBC,IAAI,EAAE,YAAY;UAClBC,MAAM,EAAE,aAAa;UACrBC,QAAQ,EAAE,uBAAuB;UACjCC,SAAS,EAAE,eAAe;UAC1BgH,UAAU,EAAE;QACd,CAAC,CACF;QACDD,OAAO,CAACR,cAAc,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,OAAO,IAAIG,OAAO,CAAEC,OAAO,IAAK;MAC9BlF,UAAU,CAAC,MAAM;QACf,MAAM6E,cAAc,GAAG,CACrB;UACEzI,EAAE,EAAE,qBAAqB;UACzB2B,IAAI,EAAE,oBAAoB;UAC1BC,IAAI,EAAE,YAAY;UAClBC,MAAM,EAAE,aAAa;UACrBC,QAAQ,EAAE,uBAAuB;UACjCC,SAAS,EAAE,eAAe;UAC1BgH,UAAU,EAAE;QACd,CAAC,EACD;UACE/I,EAAE,EAAE,qBAAqB;UACzB2B,IAAI,EAAE,YAAY;UAClBC,IAAI,EAAE,YAAY;UAClBC,MAAM,EAAE,aAAa;UACrBC,QAAQ,EAAE,uBAAuB;UACjCC,SAAS,EAAE,eAAe;UAC1BgH,UAAU,EAAE;QACd,CAAC,EACD;UACE/I,EAAE,EAAE,mBAAmB;UACvB2B,IAAI,EAAE,gBAAgB;UACtBC,IAAI,EAAE,YAAY;UAClBC,MAAM,EAAE,aAAa;UACrBC,QAAQ,EAAE,uBAAuB;UACjCC,SAAS,EAAE,eAAe;UAC1BgH,UAAU,EAAE;QACd,CAAC,CACF;QACDD,OAAO,CAACL,cAAc,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;MAC9BlF,UAAU,CAAC,MAAM;QACf,MAAM+E,aAAa,GAAG,CACpB;UACE3I,EAAE,EAAE,mBAAmB;UACvB2B,IAAI,EAAE,mBAAmB;UACzBC,IAAI,EAAE,WAAW;UACjBC,MAAM,EAAE,aAAa;UACrBC,QAAQ,EAAE,uBAAuB;UACjCC,SAAS,EAAE,eAAe;UAC1BgH,UAAU,EAAE;QACd,CAAC,EACD;UACE/I,EAAE,EAAE,gBAAgB;UACpB2B,IAAI,EAAE,gBAAgB;UACtBC,IAAI,EAAE,cAAc;UACpBC,MAAM,EAAE,aAAa;UACrBC,QAAQ,EAAE,uBAAuB;UACjCC,SAAS,EAAE,eAAe;UAC1BgH,UAAU,EAAE;QACd,CAAC,CACF;QACDD,OAAO,CAACH,aAAa,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMK,UAAU,GAAGA,CAAA,KAAM;IACvBxG,WAAW,CAAC,2EAA2E,CAAC;EAC1F,CAAC;EAED,MAAMyG,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAMC,cAAc,GAAG;MACrB9F,UAAU,EAAEhB,UAAU,CAACT,IAAI;MAC3BoH,UAAU,EAAE3G,UAAU,CAACR,IAAI;MAC3BG,SAAS,EAAEK,UAAU,CAACL,SAAS;MAC/BoH,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,aAAa;MACvBlJ,SAAS,EAAEmJ,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,MAAMC,MAAM,GAAGC,IAAI,CAACC,SAAS,CAACP,cAAc,CAAC;IAC7C,MAAMQ,SAAS,GAAG,iEAAiEC,kBAAkB,CAACJ,MAAM,CAAC,EAAE;;IAE/G;IACA,MAAMK,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7CF,OAAO,CAACG,SAAS,GAAG,kBAAkB;IACtCH,OAAO,CAACI,SAAS,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBN,SAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0CtH,UAAU,CAACT,IAAI;AACzD,sCAAsCS,UAAU,CAACL,SAAS;AAC1D;AACA;AACA;AACA,KAAK;IAED8H,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,OAAO,CAAC;;IAElC;IACA,MAAMO,UAAU,GAAGA,CAAA,KAAM;MACvBN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,OAAO,CAAC;IACpC,CAAC;IAEDA,OAAO,CAACS,aAAa,CAAC,iBAAiB,CAAC,CAACC,OAAO,GAAGH,UAAU;IAC7DP,OAAO,CAACU,OAAO,GAAIpG,CAAC,IAAK;MACvB,IAAIA,CAAC,CAACqG,MAAM,KAAKX,OAAO,EAAEO,UAAU,CAAC,CAAC;IACxC,CAAC;IAED3H,WAAW,CAAC,gEAAgE,CAAC;EAC/E,CAAC;EAED,oBACEzD,OAAA;IAAKgL,SAAS,EAAC,eAAe;IAAAS,QAAA,gBAE5BzL,OAAA;MAAKgL,SAAS,EAAC,QAAQ;MAAAS,QAAA,gBACrBzL,OAAA;QAAKgL,SAAS,EAAC,iBAAiB;QAAAS,QAAA,gBAC9BzL,OAAA;UAAKgL,SAAS,EAAC,gBAAgB;UAAAS,QAAA,gBAC7BzL,OAAA;YACE0L,GAAG,EAAC,wBAAwB;YAC5BC,GAAG,EAAC,aAAa;YACjBX,SAAS,EAAC;UAAU;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACF/L,OAAA;YAAKgL,SAAS,EAAE,kBAAkBtI,aAAa,CAAC6B,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAC1B,MAAM,KAAK,WAAW,CAAC,GAAG,WAAW,GAAG,cAAc;UAAG;YAAA8I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnI,CAAC,eACN/L,OAAA;UAAIgL,SAAS,EAAC,OAAO;UAAAS,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACN/L,OAAA;QAAKgL,SAAS,EAAC,gBAAgB;QAAAS,QAAA,gBAC7BzL,OAAA;UACEgL,SAAS,EAAE,eAAepJ,wBAAwB,GAAG,QAAQ,GAAG,EAAE,EAAG;UACrEoK,OAAO,EAAEtD,qBAAsB;UAC/BuD,KAAK,EAAC,kCAAkC;UAAAR,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/L,OAAA;UACEgL,SAAS,EAAE,eAAexJ,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC1DwK,OAAO,EAAExD,iBAAkB;UAC3ByD,KAAK,EAAC,YAAY;UAAAR,QAAA,eAElBzL,OAAA;YAAKgL,SAAS,EAAC,UAAU;YAAAS,QAAA,gBACvBzL,OAAA;cAAKgL,SAAS,EAAC;YAAU;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChC/L,OAAA;cAAKgL,SAAS,EAAC;YAAU;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACT/L,OAAA;UAAQgL,SAAS,EAAC,aAAa;UAACgB,OAAO,EAAEvD,cAAe;UAACwD,KAAK,EAAC,UAAU;UAAAR,QAAA,EAAC;QAE1E;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/L,OAAA;UAAQgL,SAAS,EAAC,aAAa;UAACgB,OAAO,EAAEA,CAAA,KAAM3K,eAAe,CAAC,IAAI,CAAE;UAAC4K,KAAK,EAAC,UAAU;UAAAR,QAAA,EAAC;QAEvF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLrK,cAAc,iBACb1B,OAAA;MAAKgL,SAAS,EAAC,iBAAiB;MAAAS,QAAA,eAC9BzL,OAAA;QAAAyL,QAAA,EAAO/J;MAAc;QAAAkK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGD/L,OAAA;MAAKgL,SAAS,EAAC,cAAc;MAAAS,QAAA,gBAE3BzL,OAAA;QAAKgL,SAAS,EAAC,gBAAgB;QAAAS,QAAA,gBAC7BzL,OAAA;UAAKgL,SAAS,EAAC,uBAAuB;UAAAS,QAAA,eACpCzL,OAAA;YAAKgL,SAAS,EAAC,gCAAgC;YAAAS,QAAA,gBAC7CzL,OAAA;cAAIgL,SAAS,EAAC,sBAAsB;cAAAS,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD/L,OAAA;cAAGgL,SAAS,EAAC,yBAAyB;cAAAS,QAAA,EAAEpI,UAAU,CAACT;YAAI;cAAAgJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELxL,mBAAmB,gBAClBP,OAAA;UAAKgL,SAAS,EAAC,gBAAgB;UAAAS,QAAA,gBAC7BzL,OAAA;YACEgL,SAAS,EAAC,iBAAiB;YAC3BkB,KAAK,EAAEvL,qBAAsB;YAC7BwL,QAAQ,EAAGhH,CAAC,IAAKvE,wBAAwB,CAACuE,CAAC,CAACqG,MAAM,CAACU,KAAK,CAAE;YAC1DE,WAAW,EAAC,uCAAuC;YACnDC,SAAS;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACF/L,OAAA;YAAKgL,SAAS,EAAC,cAAc;YAAAS,QAAA,gBAC3BzL,OAAA;cAAQgL,SAAS,EAAC,yBAAyB;cAACgB,OAAO,EAAEhE,kBAAmB;cAAAyD,QAAA,EAAC;YAEzE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/L,OAAA;cAAQgL,SAAS,EAAC,2BAA2B;cAACgB,OAAO,EAAE7D,oBAAqB;cAAAsD,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN/L,OAAA;UAAKgL,SAAS,EAAC,kBAAkB;UAAAS,QAAA,eAC/BzL,OAAA;YACEgL,SAAS,EAAC,uCAAuC;YACjDgB,OAAO,EAAEA,CAAA,KAAM1E,eAAe,CAACnH,mBAAmB,CAAE;YAAAsL,QAAA,gBAEpDzL,OAAA;cAAGgL,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAEtL;YAAmB;cAAAyL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD/L,OAAA;cAAGgL,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAC;YAA8B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChE/L,OAAA;cAAQgL,SAAS,EAAC,oBAAoB;cAACgB,OAAO,EAAEjE,sBAAuB;cAAA0D,QAAA,EAAC;YAExE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN/L,OAAA;QAAKgL,SAAS,EAAC,gBAAgB;QAAAS,QAAA,gBAC7BzL,OAAA;UAAKgL,SAAS,EAAC,uBAAuB;UAAAS,QAAA,gBACpCzL,OAAA;YAAIgL,SAAS,EAAC,sBAAsB;YAAAS,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7D/L,OAAA;YAAGgL,SAAS,EAAC,yBAAyB;YAAAS,QAAA,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,EAELtL,wBAAwB,gBACvBT,OAAA;UAAKgL,SAAS,EAAC,gBAAgB;UAAAS,QAAA,gBAC7BzL,OAAA;YACEgL,SAAS,EAAC,iBAAiB;YAC3BkB,KAAK,EAAErL,0BAA2B;YAClCsL,QAAQ,EAAGhH,CAAC,IAAKrE,6BAA6B,CAACqE,CAAC,CAACqG,MAAM,CAACU,KAAK,CAAE;YAC/DE,WAAW,EAAC,4CAA4C;YACxDC,SAAS;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACF/L,OAAA;YAAKgL,SAAS,EAAC,cAAc;YAAAS,QAAA,gBAC3BzL,OAAA;cAAQgL,SAAS,EAAC,8BAA8B;cAACgB,OAAO,EAAE3D,uBAAwB;cAAAoD,QAAA,EAAC;YAEnF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/L,OAAA;cAAQgL,SAAS,EAAC,gCAAgC;cAACgB,OAAO,EAAE1D,yBAA0B;cAAAmD,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN/L,OAAA;UAAKgL,SAAS,EAAC,kBAAkB;UAAAS,QAAA,eAC/BzL,OAAA;YACEgL,SAAS,EAAC,4CAA4C;YACtDgB,OAAO,EAAEA,CAAA,KAAM1E,eAAe,CAACjH,wBAAwB,CAAE;YAAAoL,QAAA,gBAEzDzL,OAAA;cAAGgL,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAEpL;YAAwB;cAAAuL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5D/L,OAAA;cAAGgL,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAC;YAAkC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpE/L,OAAA;cAAQgL,SAAS,EAAC,oBAAoB;cAACgB,OAAO,EAAE5D,2BAA4B;cAAAqD,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN/L,OAAA;QAAIgL,SAAS,EAAC,eAAe;QAAAS,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpD/L,OAAA;QAAKgL,SAAS,EAAC,cAAc;QAAAS,QAAA,EAC1B1K,YAAY,CAACgI,GAAG,CAAErB,IAAI,iBACrB1H,OAAA;UAEEgL,SAAS,EAAC,cAAc;UACxBgB,OAAO,EAAEA,CAAA,KAAMvE,UAAU,CAACC,IAAI,CAAE;UAAA+D,QAAA,gBAEhCzL,OAAA;YAAKgL,SAAS,EAAC,qBAAqB;YAAAS,QAAA,gBAClCzL,OAAA;cAAMgL,SAAS,EAAC,WAAW;cAAAS,QAAA,EAAE/D,IAAI,CAACvG;YAAS;cAAAyK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD/L,OAAA;cACEgL,SAAS,EAAC,eAAe;cACzBgB,OAAO,EAAG7G,CAAC,IAAK;gBACdA,CAAC,CAACmH,eAAe,CAAC,CAAC;gBACnB3E,iBAAiB,CAACD,IAAI,CAACzG,EAAE,CAAC;cAC5B,CAAE;cAAAwK,QAAA,EACH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN/L,OAAA;YAAGgL,SAAS,EAAC,cAAc;YAAAS,QAAA,EAAE/D,IAAI,CAACxG;UAAO;YAAA0K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GAhBzCrE,IAAI,CAACzG,EAAE;UAAA2K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBT,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/L,OAAA;MAAQgL,SAAS,EAAC,sBAAsB;MAACgB,OAAO,EAAEzD,OAAQ;MAAAkD,QAAA,eACxDzL,OAAA;QAAK0L,GAAG,EAAC,WAAW;QAACC,GAAG,EAAC,MAAM;QAACX,SAAS,EAAC;MAAW;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,EAGR3K,YAAY,iBACXpB,OAAA;MAAKgL,SAAS,EAAC,eAAe;MAACgB,OAAO,EAAEA,CAAA,KAAM3K,eAAe,CAAC,KAAK,CAAE;MAAAoK,QAAA,eACnEzL,OAAA;QAAKgL,SAAS,EAAC,kBAAkB;QAACgB,OAAO,EAAG7G,CAAC,IAAKA,CAAC,CAACmH,eAAe,CAAC,CAAE;QAAAb,QAAA,gBACpEzL,OAAA;UAAKgL,SAAS,EAAC,iBAAiB;UAAAS,QAAA,gBAC9BzL,OAAA;YAAIgL,SAAS,EAAC,gBAAgB;YAAAS,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C/L,OAAA;YAAQgL,SAAS,EAAC,cAAc;YAACgB,OAAO,EAAEA,CAAA,KAAM3K,eAAe,CAAC,KAAK,CAAE;YAAAoK,QAAA,EAAC;UAExE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN/L,OAAA;UAAKgL,SAAS,EAAC,kBAAkB;UAAAS,QAAA,gBAE/BzL,OAAA;YAAKgL,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BzL,OAAA;cAAIgL,SAAS,EAAC,eAAe;cAAAS,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C/L,OAAA;cAAKgL,SAAS,EAAC,kBAAkB;cAAAS,QAAA,gBAC/BzL,OAAA;gBAAGgL,SAAS,EAAC,aAAa;gBAAAS,QAAA,EAAEpI,UAAU,CAACT;cAAI;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChD/L,OAAA;gBAAGgL,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAEpI,UAAU,CAACR;cAAI;gBAAA+I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClD/L,OAAA;gBAAGgL,SAAS,EAAC,eAAe;gBAAAS,QAAA,GAAC,MAAI,EAACpI,UAAU,CAACL,SAAS;cAAA;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3D/L,OAAA;gBAAKgL,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,gBAChCzL,OAAA;kBAAKgL,SAAS,EAAE,2BAA2B3H,UAAU,CAACP,MAAM;gBAAG;kBAAA8I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtE/L,OAAA;kBAAMgL,SAAS,EAAE,sBAAsB3H,UAAU,CAACP,MAAM,EAAG;kBAAA2I,QAAA,GAAC,UAClD,EAACpI,UAAU,CAACP,MAAM,KAAK,QAAQ,GAAG,WAAW,GAAG,cAAc;gBAAA;kBAAA8I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN/L,OAAA;gBAAKgL,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,gBAChCzL,OAAA;kBAAKgL,SAAS,EAAE,2BAA2B7H,oBAAoB,GAAG,QAAQ,GAAG,cAAc;gBAAG;kBAAAyI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrG/L,OAAA;kBAAMgL,SAAS,EAAE,sBAAsB7H,oBAAoB,GAAG,QAAQ,GAAG,cAAc,EAAG;kBAAAsI,QAAA,GAAC,gBAC3E,EAACtI,oBAAoB,GAAG,0BAA0B,GAAG,uBAAuB;gBAAA;kBAAAyI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EACL1I,UAAU,CAACP,MAAM,KAAK,cAAc,iBACnC9C,OAAA;gBAAGgL,SAAS,EAAC,mCAAmC;gBAAAS,QAAA,EAAC;cAEjD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ,eACD/L,OAAA;gBACEgL,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,MAAMO,eAAe,GAAG,MAAMnI,sBAAsB,CAAC,CAAC;kBACtDX,WAAW,CAAC,gCAAgC8I,eAAe,EAAE,CAAC;kBAC9D;kBACA,MAAMjI,mBAAmB,GAAG5B,aAAa,CAAC6B,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAC1B,MAAM,KAAK,WAAW,CAAC;kBACvF,MAAM2B,YAAY,GAAGH,mBAAmB,GAAG,QAAQ,GAAG,cAAc;kBACpE,IAAIX,SAAS,GAAG,SAAS;kBACzB,IAAIC,SAAS,CAACG,SAAS,EAAE;oBACvB,MAAMC,YAAY,GAAGJ,SAAS,CAACG,SAAS,CAACE,KAAK,CAAC,uBAAuB,CAAC;oBACvE,IAAID,YAAY,EAAE;sBAChB,MAAME,OAAO,GAAGF,YAAY,CAAC,CAAC,CAAC;sBAC/B,QAAQE,OAAO;wBACb,KAAK,MAAM;0BAAEP,SAAS,GAAG,eAAe;0BAAE;wBAC1C,KAAK,KAAK;0BAAEA,SAAS,GAAG,aAAa;0BAAE;wBACvC,KAAK,KAAK;0BAAEA,SAAS,GAAG,WAAW;0BAAE;wBACrC,KAAK,KAAK;0BAAEA,SAAS,GAAG,WAAW;0BAAE;wBACrC;0BAASA,SAAS,GAAG,cAAcO,OAAO,EAAE;sBAC9C;oBACF;kBACF;kBACAZ,aAAa,CAACoB,IAAI,KAAK;oBACrB,GAAGA,IAAI;oBACP9B,IAAI,EAAE,GAAG2J,eAAe,MAAM5I,SAAS,EAAE;oBACzCd,IAAI,EAAEc,SAAS;oBACfb,MAAM,EAAE2B,YAAY;oBACpB1B,QAAQ,EAAE0B,YAAY,KAAK,QAAQ,GAAG,KAAK,GAAG;kBAChD,CAAC,CAAC,CAAC;gBACL,CAAE;gBAAAgH,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/L,OAAA;YAAKgL,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BzL,OAAA;cAAIgL,SAAS,EAAC,eAAe;cAAAS,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChDrJ,aAAa,CAACoE,MAAM,KAAK,CAAC,gBACzB9G,OAAA;cAAKgL,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCzL,OAAA;gBAAGgL,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5D/L,OAAA;gBAAGgL,SAAS,EAAC,sBAAsB;gBAAAS,QAAA,EAAC;cAA+C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,GAENrJ,aAAa,CAACqG,GAAG,CAAEvE,MAAM,iBACvBxE,OAAA;cAAqBgL,SAAS,EAAC,sBAAsB;cAAAS,QAAA,gBAEnDzL,OAAA;gBACEgL,SAAS,EAAC,yBAAyB;gBACnCgB,OAAO,EAAEA,CAAA,KAAMrD,YAAY,CAACnE,MAAM,CAACvD,EAAE,CAAE;gBAAAwK,QAAA,EACxC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAET/L,OAAA;gBAAKgL,SAAS,EAAC,aAAa;gBAAAS,QAAA,gBAC1BzL,OAAA;kBAAGgL,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAEjH,MAAM,CAAC5B;gBAAI;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD/L,OAAA;kBAAGgL,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAEjH,MAAM,CAAC3B;gBAAI;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD/L,OAAA;kBAAKgL,SAAS,EAAC,mBAAmB;kBAAAS,QAAA,gBAChCzL,OAAA;oBAAKgL,SAAS,EAAE,2BAA2BxG,MAAM,CAAC1B,MAAM;kBAAG;oBAAA8I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClE/L,OAAA;oBAAMgL,SAAS,EAAE,sBAAsBxG,MAAM,CAAC1B,MAAM,EAAG;oBAAA2I,QAAA,GACpDjH,MAAM,CAAC1B,MAAM,KAAK,WAAW,GAAG,WAAW,GAAG,cAAc,EAAC,UAAG,EAAC0B,MAAM,CAACzB,QAAQ;kBAAA;oBAAA6I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN/L,OAAA;gBACEgL,SAAS,EAAE,qCAAqCxG,MAAM,CAAC1B,MAAM,KAAK,WAAW,GAAG,mBAAmB,GAAG,gBAAgB,EAAG;gBACzHkJ,OAAO,EAAEA,CAAA,KAAMxH,MAAM,CAAC1B,MAAM,KAAK,WAAW,GAAGkG,gBAAgB,CAACxE,MAAM,CAACvD,EAAE,CAAC,GAAG6H,aAAa,CAACtE,MAAM,CAACvD,EAAE,CAAE;gBAAAwK,QAAA,EAErGjH,MAAM,CAAC1B,MAAM,KAAK,WAAW,GAAG,YAAY,GAAG;cAAS;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA,GA1BDvH,MAAM,CAACvD,EAAE;cAAA2K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2Bd,CACN,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN/L,OAAA;YAAKgL,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BzL,OAAA;cAAKgL,SAAS,EAAC,gBAAgB;cAAAS,QAAA,gBAC7BzL,OAAA;gBAAIgL,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnD/L,OAAA;gBACEgL,SAAS,EAAE,mBAAmB9I,aAAa,GAAG,0BAA0B,GAAG,EAAE,EAAG;gBAChF8J,OAAO,EAAE3C,gBAAiB;gBAC1BmD,QAAQ,EAAEtK,aAAc;gBAAAuJ,QAAA,EAEvBvJ,aAAa,GAAG,gBAAgB,GAAG;cAAU;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL/J,cAAc,CAAC8E,MAAM,KAAK,CAAC,gBAC1B9G,OAAA;cAAKgL,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCzL,OAAA;gBAAGgL,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrD/L,OAAA;gBAAGgL,SAAS,EAAC,sBAAsB;gBAAAS,QAAA,EAChCvJ,aAAa,GAAG,yBAAyB,GAAG;cAAoC;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,GAEN/J,cAAc,CAAC+G,GAAG,CAAEvE,MAAM,iBACxBxE,OAAA;cAAqBgL,SAAS,EAAC,wBAAwB;cAAAS,QAAA,gBACrDzL,OAAA;gBAAKgL,SAAS,EAAC,aAAa;gBAAAS,QAAA,gBAC1BzL,OAAA;kBAAGgL,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAEjH,MAAM,CAAC5B;gBAAI;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD/L,OAAA;kBAAGgL,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAEjH,MAAM,CAAC3B;gBAAI;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD/L,OAAA;kBAAGgL,SAAS,EAAC,uBAAuB;kBAAAS,QAAA,EAAEjH,MAAM,CAACzB;gBAAQ;kBAAA6I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN/L,OAAA;gBACEgL,SAAS,EAAC,aAAa;gBACvBgB,OAAO,EAAEA,CAAA,KAAM/C,UAAU,CAACzE,MAAM,CAACvD,EAAE,CAAE;gBAAAwK,QAAA,EACtC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAXDvH,MAAM,CAACvD,EAAE;cAAA2K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYd,CACN,CACF,eAED/L,OAAA;cAAKgL,SAAS,EAAC,sBAAsB;cAAAS,QAAA,eACnCzL,OAAA;gBAAQgL,SAAS,EAAC,oBAAoB;gBAACgB,OAAO,EAAE9B,cAAe;gBAAAuB,QAAA,EAAC;cAEhE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/L,OAAA;YAAKgL,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BzL,OAAA;cAAIgL,SAAS,EAAC,eAAe;cAAAS,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtD/L,OAAA;cACEgL,SAAS,EAAC,cAAc;cACxBgB,OAAO,EAAEA,CAAA,KAAMzK,mBAAmB,CAAC,IAAI,CAAE;cAAAkK,QAAA,gBAEzCzL,OAAA;gBAAMgL,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxD/L,OAAA;gBAAMgL,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eAGT/L,OAAA;cACEgL,SAAS,EAAC,cAAc;cACxBgB,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMS,QAAQ,GAAG,CAAC3K,qBAAqB;gBACvCC,wBAAwB,CAAC0K,QAAQ,CAAC;gBAClChJ,WAAW,CAACgJ,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B,CAAC;cAChF,CAAE;cAAAhB,QAAA,gBAEFzL,OAAA;gBAAMgL,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1D/L,OAAA;gBAAMgL,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EACjC3J,qBAAqB,GAAG,IAAI,GAAG;cAAK;gBAAA8J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGT/L,OAAA;cACEgL,SAAS,EAAC,cAAc;cACxBgB,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI9J,aAAa,EAAE;kBACjBC,gBAAgB,CAAC,KAAK,CAAC;kBACvBsB,WAAW,CAAC,2BAA2B,CAAC;gBAC1C,CAAC,MAAM;kBACLtB,gBAAgB,CAAC,IAAI,CAAC;kBACtBsB,WAAW,CAAC,2BAA2B,CAAC;gBAC1C;cACF,CAAE;cAAAgI,QAAA,gBAEFzL,OAAA;gBAAMgL,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5D/L,OAAA;gBAAMgL,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EACjCvJ,aAAa,GAAG,IAAI,GAAG;cAAK;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAzK,gBAAgB,iBACftB,OAAA;MAAKgL,SAAS,EAAC,eAAe;MAACgB,OAAO,EAAEA,CAAA,KAAMzK,mBAAmB,CAAC,KAAK,CAAE;MAAAkK,QAAA,eACvEzL,OAAA;QAAKgL,SAAS,EAAC,qBAAqB;QAACgB,OAAO,EAAG7G,CAAC,IAAKA,CAAC,CAACmH,eAAe,CAAC,CAAE;QAAAb,QAAA,gBACvEzL,OAAA;UAAKgL,SAAS,EAAC,iBAAiB;UAAAS,QAAA,gBAC9BzL,OAAA;YAAIgL,SAAS,EAAC,gBAAgB;YAAAS,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjD/L,OAAA;YAAQgL,SAAS,EAAC,cAAc;YAACgB,OAAO,EAAEA,CAAA,KAAMzK,mBAAmB,CAAC,KAAK,CAAE;YAAAkK,QAAA,EAAC;UAE5E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN/L,OAAA;UAAKgL,SAAS,EAAC,kBAAkB;UAAAS,QAAA,eAC/BzL,OAAA;YAAKgL,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BzL,OAAA;cAAKgL,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCzL,OAAA;gBAAOgL,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvD/L,OAAA;gBACE6C,IAAI,EAAC,UAAU;gBACfmI,SAAS,EAAC,uBAAuB;gBACjC0B,OAAO,EAAEtK,YAAY,CAACE,QAAS;gBAC/B6J,QAAQ,EAAGhH,CAAC,IAAK9C,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEE,QAAQ,EAAE6C,CAAC,CAACqG,MAAM,CAACkB;gBAAO,CAAC;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/L,OAAA;cAAKgL,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCzL,OAAA;gBAAOgL,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,GAAC,cAAY,EAACrJ,YAAY,CAACG,SAAS,EAAC,UAAQ;cAAA;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1F/L,OAAA;gBAAGgL,SAAS,EAAC,0BAA0B;gBAAAS,QAAA,EACpCrJ,YAAY,CAACG,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG,GAAGH,YAAY,CAACG,SAAS;cAAe;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC,eACJ/L,OAAA;gBAAKgL,SAAS,EAAC,qBAAqB;gBAAAS,QAAA,gBAClCzL,OAAA;kBACEgL,SAAS,EAAC,mBAAmB;kBAC7BgB,OAAO,EAAEA,CAAA,KAAM3J,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEG,SAAS,EAAE0E,IAAI,CAAC0F,GAAG,CAAC,CAAC,EAAEvK,YAAY,CAACG,SAAS,GAAG,CAAC;kBAAC,CAAC,CAAE;kBAAAkJ,QAAA,EACvG;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/L,OAAA;kBAAMgL,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,GAAErJ,YAAY,CAACG,SAAS,EAAC,GAAC;gBAAA;kBAAAqJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnE/L,OAAA;kBACEgL,SAAS,EAAC,mBAAmB;kBAC7BgB,OAAO,EAAEA,CAAA,KAAM3J,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEG,SAAS,EAAE0E,IAAI,CAAC2F,GAAG,CAAC,EAAE,EAAExK,YAAY,CAACG,SAAS,GAAG,CAAC;kBAAC,CAAC,CAAE;kBAAAkJ,QAAA,EACxG;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN/L,OAAA;cAAKgL,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCzL,OAAA;gBAAOgL,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7D/L,OAAA;gBACE6C,IAAI,EAAC,UAAU;gBACfmI,SAAS,EAAC,uBAAuB;gBACjC0B,OAAO,EAAEtK,YAAY,CAACI,aAAc;gBACpC2J,QAAQ,EAAGhH,CAAC,IAAK9C,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEI,aAAa,EAAE2C,CAAC,CAACqG,MAAM,CAACkB;gBAAO,CAAC;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/L,OAAA;cAAKgL,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCzL,OAAA;gBAAOgL,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChE/L,OAAA;gBACE6C,IAAI,EAAC,UAAU;gBACfmI,SAAS,EAAC,uBAAuB;gBACjC0B,OAAO,EAAEtK,YAAY,CAACK,aAAc;gBACpC0J,QAAQ,EAAGhH,CAAC,IAAK9C,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEK,aAAa,EAAE0C,CAAC,CAACqG,MAAM,CAACkB;gBAAO,CAAC;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN/L,OAAA;cAAKgL,SAAS,EAAC,mBAAmB;cAAAS,QAAA,eAChCzL,OAAA;gBACEgL,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAEA,CAAA,KAAMvI,WAAW,CAAC,6FAA6F,CAAE;gBAAAgI,QAAA,EAC3H;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN/L,OAAA;cAAKgL,SAAS,EAAC,mBAAmB;cAAAS,QAAA,eAChCzL,OAAA;gBACEgL,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAEA,CAAA,KAAMvI,WAAW,CAAC,8EAA8E,CAAE;gBAAAgI,QAAA,EAC5G;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN/L,OAAA;cAAKgL,SAAS,EAAC,mBAAmB;cAAAS,QAAA,eAChCzL,OAAA;gBACEgL,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAEA,CAAA,KAAMvI,WAAW,CAAC,8EAA8E,CAAE;gBAAAgI,QAAA,EAC5G;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN/L,OAAA;cAAKgL,SAAS,EAAC,kBAAkB;cAAAS,QAAA,gBAC/BzL,OAAA;gBAAIgL,SAAS,EAAC,wBAAwB;gBAAAS,QAAA,EAAC;cAAmC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/E/L,OAAA;gBAAGgL,SAAS,EAAC,sBAAsB;gBAAAS,QAAA,EAAC;cAEpC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAEJ/L,OAAA;gBAAKgL,SAAS,EAAC,cAAc;gBAAAS,QAAA,gBAC3BzL,OAAA;kBAAMgL,SAAS,EAAC,gBAAgB;kBAAAS,QAAA,EAAC;gBAA8B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtE/L,OAAA;kBACEgL,SAAS,EAAC,wBAAwB;kBAClCgB,OAAO,EAAEA,CAAA,KAAMvI,WAAW,CAAC,gEAAgE,CAAE;kBAAAgI,QAAA,eAE7FzL,OAAA;oBAAKgL,SAAS,EAAC;kBAA8B;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN/L,OAAA;gBAAKgL,SAAS,EAAC,cAAc;gBAAAS,QAAA,gBAC3BzL,OAAA;kBAAMgL,SAAS,EAAC,gBAAgB;kBAAAS,QAAA,EAAC;gBAAuB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/D/L,OAAA;kBACEgL,SAAS,EAAC,wBAAwB;kBAClCgB,OAAO,EAAEA,CAAA,KAAMvI,WAAW,CAAC,+BAA+B,CAAE;kBAAAgI,QAAA,eAE5DzL,OAAA;oBAAKgL,SAAS,EAAC;kBAA8B;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN/L,OAAA;gBAAKgL,SAAS,EAAC,cAAc;gBAAAS,QAAA,gBAC3BzL,OAAA;kBAAMgL,SAAS,EAAC,gBAAgB;kBAAAS,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5D/L,OAAA;kBACEgL,SAAS,EAAC,wBAAwB;kBAClCgB,OAAO,EAAEA,CAAA,KAAMvI,WAAW,CAAC,iCAAiC,CAAE;kBAAAgI,QAAA,eAE9DzL,OAAA;oBAAKgL,SAAS,EAAC;kBAA8B;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN/L,OAAA;gBAAGgL,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAC;cAE7B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC7L,EAAA,CA1jCQD,GAAG;AAAA4M,EAAA,GAAH5M,GAAG;AA4jCZ,eAAeA,GAAG;AAAC,IAAA4M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}