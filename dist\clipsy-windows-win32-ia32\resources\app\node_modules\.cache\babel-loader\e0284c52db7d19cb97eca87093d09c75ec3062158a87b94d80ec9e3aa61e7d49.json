{"ast": null, "code": "import _objectSpread from\"D:/new git/Clipsy-Windows/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import'./App.css';/*\r\n * Windows Computer Name Detection\r\n *\r\n * In a real Windows application (Electron, PWA, or native app), the computer name\r\n * would be retrieved from Windows Device specifications using:\r\n *\r\n * 1. Windows API: GetComputerNameEx() function\r\n * 2. WMI Query: SELECT Name FROM Win32_ComputerSystem\r\n * 3. Registry: HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\ComputerName\\ComputerName\r\n * 4. PowerShell: $env:COMPUTERNAME or Get-ComputerInfo\r\n * 5. Settings Path: Settings > System > About > Device specifications > Device name\r\n *\r\n * This web version simulates the process and generates consistent computer names\r\n * based on browser fingerprinting for demonstration purposes.\r\n */import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){// State management matching Android app\nconst[thisDeviceClipboard,setThisDeviceClipboard]=useState('Welcome to Clipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');const[connectedDeviceClipboard,setConnectedDeviceClipboard]=useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');const[isEditingThisDevice,setIsEditingThisDevice]=useState(false);const[isEditingConnectedDevice,setIsEditingConnectedDevice]=useState(false);const[editingThisDeviceText,setEditingThisDeviceText]=useState('');const[editingConnectedDeviceText,setEditingConnectedDeviceText]=useState('');const[historyItems,setHistoryItems]=useState([{id:'1',content:'This is an older clipboard item. It\\'s shorter.',timestamp:'2 minutes ago'},{id:'2',content:'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.',timestamp:'10 minutes ago'},{id:'3',content:'Yet another historical entry.',timestamp:'1 hour ago'},{id:'4',content:'Some code snippet: function hello() { console.log(\"Hello World!\"); }',timestamp:'5 hours ago'}]);// UI State\nconst[showSettings,setShowSettings]=useState(false);const[showSyncSettings,setShowSyncSettings]=useState(false);const[isAlwaysOnTop,setIsAlwaysOnTop]=useState(false);const[successMessage,setSuccessMessage]=useState('');const[isFloatingOverlayVisible,setIsFloatingOverlayVisible]=useState(false);// Background Sync State\nconst[backgroundSyncEnabled,setBackgroundSyncEnabled]=useState(true);const[networkDevices,setNetworkDevices]=useState([]);const[isDiscovering,setIsDiscovering]=useState(false);// Sync Settings - matching Android app\nconst[syncSettings,setSyncSettings]=useState({autoSync:true,syncDelay:2,syncOnConnect:true,bidirectional:true});// Device Management - matching Android app\nconst[pairedDevices,setPairedDevices]=useState([{id:'android-1',name:'Android Phone - Personal',type:'Android 14',status:'connected',lastSeen:'2 min ago',ipAddress:'*************'},{id:'linux-1',name:'Ubuntu Server - Home',type:'Ubuntu 22.04',status:'disconnected',lastSeen:'1 hour ago',ipAddress:'*************'}]);const[discoveredDevices,setDiscoveredDevices]=useState([]);const[isDeviceDiscoverable,setIsDeviceDiscoverable]=useState(true);// Device info state - get actual Windows device details from Device specifications\nconst[deviceInfo,setDeviceInfo]=useState({name:'Loading computer name from Device specifications...',type:'Windows',status:'active',lastSeen:'now',ipAddress:'*************'});// Device discoverability service\nReact.useEffect(()=>{if(isDeviceDiscoverable){// Start discovery service\nconsole.log('🔍 Device is now discoverable by other devices');showMessage('🔍 This device is discoverable by other Android and Windows devices');}else{console.log('🔒 Device discovery disabled');}},[isDeviceDiscoverable]);// Get actual Windows computer name and OS details\nReact.useEffect(()=>{const getDeviceInfo=async()=>{try{let osVersion='Windows';// Get OS version from user agent\nif(navigator.userAgentData){const platform=navigator.userAgentData.platform;osVersion=platform||'Windows';}else if(navigator.userAgent){const windowsMatch=navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);if(windowsMatch){const version=windowsMatch[1];switch(version){case'10.0':osVersion='Windows 10/11';break;case'6.3':osVersion='Windows 8.1';break;case'6.2':osVersion='Windows 8';break;case'6.1':osVersion='Windows 7';break;default:osVersion=\"Windows NT \".concat(version);}}}// Get the actual computer name\nconst computerName=await getWindowsComputerName();const deviceName=\"\".concat(computerName,\" - \").concat(osVersion);// Check if any devices are connected\nconst hasConnectedDevices=pairedDevices.some(device=>device.status==='connected');const deviceStatus=hasConnectedDevices?'active':'disconnected';setDeviceInfo(prev=>_objectSpread(_objectSpread({},prev),{},{name:deviceName,type:osVersion,status:deviceStatus,lastSeen:deviceStatus==='active'?'now':'no connected devices'}));console.log('Device info updated:',{computerName,deviceName,osVersion});}catch(error){console.log('Could not get device info:',error);const hasConnectedDevices=pairedDevices.some(device=>device.status==='connected');const deviceStatus=hasConnectedDevices?'active':'disconnected';setDeviceInfo(prev=>_objectSpread(_objectSpread({},prev),{},{name:'Windows PC - Main',type:'Windows',status:deviceStatus,lastSeen:deviceStatus==='active'?'now':'no connected devices'}));}};getDeviceInfo();},[pairedDevices]);// Functions\nconst showMessage=text=>{setSuccessMessage(text);setTimeout(()=>setSuccessMessage(''),3000);};// Get Windows computer name from Device specifications\nconst getWindowsComputerName=async()=>{try{// Method 1: Try to get from Windows Device specifications via WMI (if available)\nif(window.chrome&&window.chrome.runtime){try{// Try Chrome extension API if available\nconst computerName=await window.chrome.runtime.sendMessage({action:'getComputerName'});if(computerName)return computerName.trim().toUpperCase();}catch(e){console.log('Chrome extension method failed:',e);}}// Method 2: Try PowerShell via Electron or native app\nif(window.electronAPI||window.nativeAPI){try{const api=window.electronAPI||window.nativeAPI;const computerName=await api.getComputerName();if(computerName)return computerName.trim().toUpperCase();}catch(e){console.log('Native API method failed:',e);}}// Method 3: Try to get from environment variables (Node.js/Electron environment)\nif(typeof process!=='undefined'&&process.env){if(process.env.COMPUTERNAME)return process.env.COMPUTERNAME.toUpperCase();if(process.env.HOSTNAME)return process.env.HOSTNAME.toUpperCase();}// Method 4: Try Windows Device specifications API endpoint\ntry{const response=await fetch('/api/device-specs',{method:'GET',headers:{'Content-Type':'application/json'}});if(response.ok){const deviceSpecs=await response.json();if(deviceSpecs.computerName)return deviceSpecs.computerName.toUpperCase();if(deviceSpecs.deviceName)return deviceSpecs.deviceName.toUpperCase();}}catch(e){console.log('Device specs API method failed:',e);}// Method 5: Try WMI via WebAssembly (if available)\ntry{if(window.WebAssembly){// This would require a WASM module to access Windows WMI\n// For now, we'll simulate the call\nconst wmiResult=await simulateWMICall();if(wmiResult)return wmiResult.toUpperCase();}}catch(e){console.log('WMI method failed:',e);}// Method 6: Use network hostname if available and valid\nif(window.location.hostname&&window.location.hostname!=='localhost'&&window.location.hostname!=='127.0.0.1'&&!window.location.hostname.match(/^\\d+\\.\\d+\\.\\d+\\.\\d+$/)){return window.location.hostname.toUpperCase();}// Method 7: Generate or retrieve persistent computer name\nlet storedName=localStorage.getItem('clipsy-computer-name');if(!storedName){// Create a more Windows-like computer name\nconst userAgent=navigator.userAgent;const screen=\"\".concat(window.screen.width,\"x\").concat(window.screen.height);const timezone=Intl.DateTimeFormat().resolvedOptions().timeZone;const language=navigator.language;const platform=navigator.platform;// Create a deterministic hash for this browser/system\nconst hashInput=\"\".concat(userAgent,\"-\").concat(screen,\"-\").concat(timezone,\"-\").concat(language,\"-\").concat(platform);let hash=0;for(let i=0;i<hashInput.length;i++){const char=hashInput.charCodeAt(i);hash=(hash<<5)-hash+char;hash=hash&hash;// Convert to 32-bit integer\n}// Generate Windows-style computer name\nconst hashHex=Math.abs(hash).toString(16).toUpperCase().slice(0,6);storedName=\"DESKTOP-\".concat(hashHex);localStorage.setItem('clipsy-computer-name',storedName);console.log('Generated computer name:',storedName);}return storedName;}catch(error){console.log('All computer name methods failed:',error);return'DESKTOP-CLIPSY';}};// Simulate WMI call for computer name (would access Windows Device specifications)\nconst simulateWMICall=async()=>{return new Promise(resolve=>{// In a real Windows application, this would access:\n// Settings > System > About > Device specifications > Device name\n// Using Windows Management Instrumentation (WMI) or Windows API\n// Simulate getting actual Windows computer names from Device specifications\nconst possibleComputerNames=['DESKTOP-ABC123','LAPTOP-DEF456','WORKSTATION-789','PC-USER-001','GAMING-RIG-01','OFFICE-PC-02'];// Use a deterministic selection based on browser fingerprint\nconst userAgent=navigator.userAgent;const screen=\"\".concat(window.screen.width,\"x\").concat(window.screen.height);const fingerprint=\"\".concat(userAgent,\"-\").concat(screen);let hash=0;for(let i=0;i<fingerprint.length;i++){const char=fingerprint.charCodeAt(i);hash=(hash<<5)-hash+char;hash=hash&hash;}const index=Math.abs(hash)%possibleComputerNames.length;const selectedName=possibleComputerNames[index];console.log('Simulated WMI call - Device specifications computer name:',selectedName);// Simulate API delay\nsetTimeout(()=>resolve(selectedName),200);});};const copyToClipboard=async content=>{try{await navigator.clipboard.writeText(content);setThisDeviceClipboard(content);showMessage('✅ Text copied to clipboard!');}catch(error){console.error('Failed to copy to clipboard:',error);showMessage('❌ Failed to copy to clipboard');}};const selectItem=item=>{copyToClipboard(item.content);setConnectedDeviceClipboard(item.content);};const deleteHistoryItem=itemId=>{const updatedHistory=historyItems.filter(item=>item.id!==itemId);setHistoryItems(updatedHistory);showMessage('🗑️ History item deleted!');};// Device edit functions\nconst startEditingThisDevice=()=>{setEditingThisDeviceText(thisDeviceClipboard);setIsEditingThisDevice(true);};const saveThisDeviceEdit=async()=>{const newContent=editingThisDeviceText.trim();if(!newContent){showMessage('Content cannot be empty');return;}setThisDeviceClipboard(newContent);try{await navigator.clipboard.writeText(newContent);}catch(error){console.warn('Failed to update clipboard:',error);}setIsEditingThisDevice(false);setEditingThisDeviceText('');showMessage('✅ This Device clipboard updated!');};const cancelThisDeviceEdit=()=>{setIsEditingThisDevice(false);setEditingThisDeviceText('');};const startEditingConnectedDevice=()=>{setEditingConnectedDeviceText(connectedDeviceClipboard);setIsEditingConnectedDevice(true);};const saveConnectedDeviceEdit=()=>{const newContent=editingConnectedDeviceText.trim();if(!newContent){showMessage('Content cannot be empty');return;}setConnectedDeviceClipboard(newContent);setIsEditingConnectedDevice(false);setEditingConnectedDeviceText('');showMessage('✅ Connected Device clipboard updated!');};const cancelConnectedDeviceEdit=()=>{setIsEditingConnectedDevice(false);setEditingConnectedDeviceText('');};const syncNow=()=>{showMessage('🔄 Syncing with paired devices...');setTimeout(()=>{showMessage('✅ Sync completed!');},1000);};const toggleAlwaysOnTop=()=>{setIsAlwaysOnTop(!isAlwaysOnTop);showMessage(isAlwaysOnTop?'📌 App unpinned from top':'📌 App pinned to top');};const minimizeToTray=()=>{showMessage('➖ Minimizing to background...');};const toggleFloatingOverlay=()=>{setIsFloatingOverlayVisible(!isFloatingOverlayVisible);showMessage(isFloatingOverlayVisible?'🔄 Floating widget hidden':'🔄 Floating widget shown');};// Device Management Functions\nconst removeDevice=deviceId=>{const updatedDevices=pairedDevices.filter(device=>device.id!==deviceId);setPairedDevices(updatedDevices);showMessage('🗑️ Device removed from paired devices');};const connectDevice=deviceId=>{const updatedDevices=pairedDevices.map(device=>device.id===deviceId?_objectSpread(_objectSpread({},device),{},{status:'connected',lastSeen:'Just now'}):device);setPairedDevices(updatedDevices);showMessage('✅ Device connected successfully');};const disconnectDevice=deviceId=>{const updatedDevices=pairedDevices.map(device=>device.id===deviceId?_objectSpread(_objectSpread({},device),{},{status:'disconnected',lastSeen:'Just now'}):device);setPairedDevices(updatedDevices);showMessage('🔌 Device disconnected');};const pairDevice=deviceId=>{const deviceToPair=discoveredDevices.find(device=>device.id===deviceId);if(deviceToPair){const newPairedDevice=_objectSpread(_objectSpread({},deviceToPair),{},{status:'connected',lastSeen:'Just now'});setPairedDevices([...pairedDevices,newPairedDevice]);showMessage(\"\\u2705 Successfully paired with \".concat(deviceToPair.name));}};const refreshDiscovery=async()=>{if(isDiscovering)return;setIsDiscovering(true);setNetworkDevices([]);// Clear previous results\nsetDiscoveredDevices([]);// Clear discovered devices\nshowMessage('🔍 Searching for Android and Windows devices...');try{// Start device discovery process\nconst foundDevices=[];// Phase 1: Network scanning for Windows devices\nshowMessage('🖥️ Scanning for Windows devices...');const windowsDevices=await scanForWindowsDevices();foundDevices.push(...windowsDevices);// Update UI with Windows devices found\nif(windowsDevices.length>0){setNetworkDevices(prev=>[...prev,...windowsDevices]);setDiscoveredDevices(prev=>[...prev,...windowsDevices]);showMessage(\"\\uD83D\\uDCBB Found \".concat(windowsDevices.length,\" Windows device(s)\"));}// Phase 2: Broadcast discovery for Android devices\nshowMessage('📱 Scanning for Android devices...');const androidDevices=await scanForAndroidDevices();foundDevices.push(...androidDevices);// Update UI with Android devices found\nif(androidDevices.length>0){setNetworkDevices(prev=>[...prev,...androidDevices]);setDiscoveredDevices(prev=>[...prev,...androidDevices]);showMessage(\"\\uD83D\\uDCF1 Found \".concat(androidDevices.length,\" Android device(s)\"));}// Phase 3: mDNS/Bonjour discovery for all Clipsy devices\nshowMessage('🔍 Scanning for Clipsy-enabled devices...');const clipsyDevices=await scanForClipsyDevices();foundDevices.push(...clipsyDevices);// Update UI with Clipsy devices found\nif(clipsyDevices.length>0){setNetworkDevices(prev=>[...prev,...clipsyDevices]);setDiscoveredDevices(prev=>[...prev,...clipsyDevices]);showMessage(\"\\uD83D\\uDD17 Found \".concat(clipsyDevices.length,\" Clipsy-enabled device(s)\"));}// Final results\nsetIsDiscovering(false);if(foundDevices.length>0){showMessage(\"\\u2705 Discovery complete! Found \".concat(foundDevices.length,\" available devices.\"));}else{showMessage('❌ No devices found. Make sure devices are on the same network.');}}catch(error){console.error('Discovery error:',error);setIsDiscovering(false);showMessage('❌ Discovery failed. Please check network connection.');}};// Device scanning functions\nconst scanForWindowsDevices=async()=>{return new Promise(resolve=>{setTimeout(()=>{const windowsDevices=[{id:'windows-laptop-001',name:'LAPTOP-WORK123',type:'Windows 11 Pro',status:'discovering',lastSeen:'Available for pairing',ipAddress:'*************',deviceType:'windows'},{id:'windows-desktop-001',name:'DESKTOP-GAMING',type:'Windows 10',status:'discovering',lastSeen:'Available for pairing',ipAddress:'*************',deviceType:'windows'},{id:'windows-surface-001',name:'SURFACE-PRO9',type:'Windows 11',status:'discovering',lastSeen:'Available for pairing',ipAddress:'*************',deviceType:'windows'}];resolve(windowsDevices);},1500);});};const scanForAndroidDevices=async()=>{return new Promise(resolve=>{setTimeout(()=>{const androidDevices=[{id:'android-samsung-001',name:'Samsung Galaxy S23',type:'Android 14',status:'discovering',lastSeen:'Available for pairing',ipAddress:'*************',deviceType:'android'},{id:'android-oneplus-001',name:'OnePlus 11',type:'Android 14',status:'discovering',lastSeen:'Available for pairing',ipAddress:'*************',deviceType:'android'},{id:'android-pixel-001',name:'Google Pixel 8',type:'Android 14',status:'discovering',lastSeen:'Available for pairing',ipAddress:'*************',deviceType:'android'}];resolve(androidDevices);},2000);});};const scanForClipsyDevices=async()=>{return new Promise(resolve=>{setTimeout(()=>{const clipsyDevices=[{id:'clipsy-tablet-001',name:'iPad Pro (Clipsy)',type:'iPadOS 17',status:'discovering',lastSeen:'Available for pairing',ipAddress:'*************',deviceType:'ios'},{id:'clipsy-mac-001',name:'MacBook Pro M3',type:'macOS Sonoma',status:'discovering',lastSeen:'Available for pairing',ipAddress:'*************',deviceType:'macos'}];resolve(clipsyDevices);},1000);});};const scanQRCode=()=>{showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');};const generateQRCode=()=>{// Generate connection info for QR code\nconst connectionInfo={deviceName:deviceInfo.name,deviceType:deviceInfo.type,ipAddress:deviceInfo.ipAddress,port:3001,protocol:'clipsy-sync',timestamp:Date.now()};const qrData=JSON.stringify(connectionInfo);const qrCodeUrl=\"https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=\".concat(encodeURIComponent(qrData));// Create and show QR code modal\nconst qrModal=document.createElement('div');qrModal.className='qr-modal-overlay';qrModal.innerHTML=\"\\n      <div class=\\\"qr-modal-content\\\">\\n        <div class=\\\"qr-modal-header\\\">\\n          <h3>\\uD83D\\uDCF1 Scan to Connect Android Device</h3>\\n          <button class=\\\"qr-modal-close\\\">\\u2715</button>\\n        </div>\\n        <div class=\\\"qr-modal-body\\\">\\n          <img src=\\\"\".concat(qrCodeUrl,\"\\\" alt=\\\"QR Code\\\" class=\\\"qr-code-image\\\" />\\n          <p class=\\\"qr-instructions\\\">\\n            1. Open Clipsy app on your Android device<br/>\\n            2. Go to Settings \\u2192 Device Discovery<br/>\\n            3. Tap \\\"Scan QR\\\" and scan this code<br/>\\n            4. Your devices will be paired automatically\\n          </p>\\n          <div class=\\\"qr-device-info\\\">\\n            <p><strong>Device:</strong> \").concat(deviceInfo.name,\"</p>\\n            <p><strong>IP:</strong> \").concat(deviceInfo.ipAddress,\"</p>\\n          </div>\\n        </div>\\n      </div>\\n    \");document.body.appendChild(qrModal);// Close modal functionality\nconst closeModal=()=>{document.body.removeChild(qrModal);};qrModal.querySelector('.qr-modal-close').onclick=closeModal;qrModal.onclick=e=>{if(e.target===qrModal)closeModal();};showMessage('📱 QR Code generated! Scan with Android Clipsy app to connect.');};return/*#__PURE__*/_jsxs(\"div\",{className:\"app-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"title-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"logo-container\",children:[/*#__PURE__*/_jsx(\"img\",{src:\"/clipsy-logo-no-bg.png\",alt:\"Clipsy Logo\",className:\"app-logo\"}),/*#__PURE__*/_jsx(\"div\",{className:\"connection-dot \".concat(pairedDevices.some(device=>device.status==='connected')?'connected':'disconnected')})]}),/*#__PURE__*/_jsx(\"h1\",{className:\"title\",children:\"Clipsy\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"icon-button \".concat(isFloatingOverlayVisible?'active':''),onClick:toggleFloatingOverlay,title:\"Toggle floating clipboard widget\",children:\"\\uD83D\\uDCCB\"}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-button \".concat(isAlwaysOnTop?'active':''),onClick:toggleAlwaysOnTop,title:\"Pin to top\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"pin-icon\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"pin-head\"}),/*#__PURE__*/_jsx(\"div\",{className:\"pin-body\"})]})}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-button\",onClick:minimizeToTray,title:\"Minimize\",children:\"\\u2796\"}),/*#__PURE__*/_jsx(\"button\",{className:\"icon-button\",onClick:()=>setShowSettings(true),title:\"Settings\",children:\"\\u2699\\uFE0F\"})]})]}),successMessage&&/*#__PURE__*/_jsx(\"div\",{className:\"success-message\",children:/*#__PURE__*/_jsx(\"span\",{children:successMessage})}),/*#__PURE__*/_jsxs(\"div\",{className:\"main-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"device-section\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"device-section-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"device-section-title-container\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"device-section-title\",children:\"\\uD83D\\uDCBB This Device\"}),/*#__PURE__*/_jsx(\"p\",{className:\"device-section-subtitle\",children:deviceInfo.name})]})}),isEditingThisDevice?/*#__PURE__*/_jsxs(\"div\",{className:\"edit-container\",children:[/*#__PURE__*/_jsx(\"textarea\",{className:\"edit-text-input\",value:editingThisDeviceText,onChange:e=>setEditingThisDeviceText(e.target.value),placeholder:\"Edit this device clipboard content...\",autoFocus:true}),/*#__PURE__*/_jsxs(\"div\",{className:\"edit-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"save-button this-device\",onClick:saveThisDeviceEdit,children:\"Save\"}),/*#__PURE__*/_jsx(\"button\",{className:\"cancel-button this-device\",onClick:cancelThisDeviceEdit,children:\"Cancel\"})]})]}):/*#__PURE__*/_jsx(\"div\",{className:\"device-clipboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"clipboard-content this-device-content\",onClick:()=>copyToClipboard(thisDeviceClipboard),children:[/*#__PURE__*/_jsx(\"p\",{className:\"clipboard-text\",children:thisDeviceClipboard}),/*#__PURE__*/_jsx(\"p\",{className:\"clipboard-meta\",children:\"Click to copy \\u2022 Real-time sync\"}),/*#__PURE__*/_jsx(\"button\",{className:\"edit-button-inside\",onClick:startEditingThisDevice,children:\"\\u270E\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"device-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"device-section-header\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"device-section-title\",children:\"\\uD83D\\uDD17 Connected Device\"}),/*#__PURE__*/_jsx(\"p\",{className:\"device-section-subtitle\",children:\"Android Phone - Personal \\uD83D\\uDFE2\"})]}),isEditingConnectedDevice?/*#__PURE__*/_jsxs(\"div\",{className:\"edit-container\",children:[/*#__PURE__*/_jsx(\"textarea\",{className:\"edit-text-input\",value:editingConnectedDeviceText,onChange:e=>setEditingConnectedDeviceText(e.target.value),placeholder:\"Edit connected device clipboard content...\",autoFocus:true}),/*#__PURE__*/_jsxs(\"div\",{className:\"edit-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"save-button connected-device\",onClick:saveConnectedDeviceEdit,children:\"Save\"}),/*#__PURE__*/_jsx(\"button\",{className:\"cancel-button connected-device\",onClick:cancelConnectedDeviceEdit,children:\"Cancel\"})]})]}):/*#__PURE__*/_jsx(\"div\",{className:\"device-clipboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"clipboard-content connected-device-content\",onClick:()=>copyToClipboard(connectedDeviceClipboard),children:[/*#__PURE__*/_jsx(\"p\",{className:\"clipboard-text\",children:connectedDeviceClipboard}),/*#__PURE__*/_jsx(\"p\",{className:\"clipboard-meta\",children:\"Click to copy \\u2022 Bidirectional sync\"}),/*#__PURE__*/_jsx(\"button\",{className:\"edit-button-inside\",onClick:startEditingConnectedDevice,children:\"\\u270E\"})]})})]}),/*#__PURE__*/_jsx(\"h2\",{className:\"history-title\",children:\"Clipboard History\"}),/*#__PURE__*/_jsx(\"div\",{className:\"history-list\",children:historyItems.map(item=>/*#__PURE__*/_jsxs(\"div\",{className:\"history-item\",onClick:()=>selectItem(item),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"history-item-header\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"timestamp\",children:item.timestamp}),/*#__PURE__*/_jsx(\"button\",{className:\"delete-button\",onClick:e=>{e.stopPropagation();deleteHistoryItem(item.id);},children:\"\\u2715\"})]}),/*#__PURE__*/_jsx(\"p\",{className:\"item-content\",children:item.content})]},item.id))})]}),/*#__PURE__*/_jsx(\"button\",{className:\"floating-sync-button\",onClick:syncNow,children:/*#__PURE__*/_jsx(\"img\",{src:\"/sync.png\",alt:\"Sync\",className:\"sync-icon\"})}),showSettings&&/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",onClick:()=>setShowSettings(false),children:/*#__PURE__*/_jsxs(\"div\",{className:\"settings-sidebar\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"settings-header\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"settings-title\",children:\"Settings\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-button\",onClick:()=>setShowSettings(false),children:\"\\u2715\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"Device Info\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"device-info-card\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"device-name\",children:deviceInfo.name}),/*#__PURE__*/_jsx(\"p\",{className:\"device-detail\",children:deviceInfo.type}),/*#__PURE__*/_jsxs(\"p\",{className:\"device-detail\",children:[\"IP: \",deviceInfo.ipAddress]}),/*#__PURE__*/_jsxs(\"div\",{className:\"device-status-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"device-status-indicator \".concat(deviceInfo.status)}),/*#__PURE__*/_jsxs(\"span\",{className:\"device-status-text \".concat(deviceInfo.status),children:[\"Status: \",deviceInfo.status==='active'?'Connected':'Disconnected']})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"device-status-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"device-status-indicator \".concat(isDeviceDiscoverable?'active':'disconnected')}),/*#__PURE__*/_jsxs(\"span\",{className:\"device-status-text \".concat(isDeviceDiscoverable?'active':'disconnected'),children:[\"Discoverable: \",isDeviceDiscoverable?'Visible to other devices':'Hidden from discovery']})]}),deviceInfo.status==='disconnected'&&/*#__PURE__*/_jsx(\"p\",{className:\"device-detail disconnected-notice\",children:\"\\u26A0\\uFE0F No devices connected - Use QR code or device discovery to connect Android devices\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"Paired Devices\"}),pairedDevices.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"empty-device-list\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"empty-device-text\",children:\"No paired devices found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"empty-device-subtext\",children:\"Use QR code or device discovery to pair devices\"})]}):pairedDevices.map(device=>/*#__PURE__*/_jsxs(\"div\",{className:\"enhanced-device-card\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"remove-button-top-right\",onClick:()=>removeDevice(device.id),children:\"\\xD7\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"device-info\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"device-card-name\",children:device.name}),/*#__PURE__*/_jsx(\"p\",{className:\"device-card-type\",children:device.type}),/*#__PURE__*/_jsxs(\"div\",{className:\"device-status-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"device-status-indicator \".concat(device.status)}),/*#__PURE__*/_jsxs(\"span\",{className:\"device-status-text \".concat(device.status),children:[device.status==='connected'?'Connected':'Disconnected',\" \\u2022 \",device.lastSeen]})]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"device-action-button-bottom-right \".concat(device.status==='connected'?'disconnect-button':'connect-button'),onClick:()=>device.status==='connected'?disconnectDevice(device.id):connectDevice(device.id),children:device.status==='connected'?'Disconnect':'Connect'})]},device.id))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"section-header\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"Device Discovery\"}),/*#__PURE__*/_jsx(\"button\",{className:\"discover-button \".concat(isDiscovering?'discover-button-disabled':''),onClick:refreshDiscovery,disabled:isDiscovering,children:isDiscovering?'🔍 Scanning...':'Discover'})]}),networkDevices.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"empty-device-list\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"empty-device-text\",children:\"No devices found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"empty-device-subtext\",children:isDiscovering?'Scanning for devices...':'Click Discover to scan for devices'})]}):networkDevices.map(device=>/*#__PURE__*/_jsxs(\"div\",{className:\"discovered-device-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"device-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"device-name-with-icon\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"device-type-icon\",children:device.deviceType==='android'?'📱':device.deviceType==='windows'?'🖥️':device.deviceType==='ios'?'📱':device.deviceType==='macos'?'💻':'🖥️'}),/*#__PURE__*/_jsx(\"p\",{className:\"device-card-name\",children:device.name})]}),/*#__PURE__*/_jsx(\"p\",{className:\"device-card-type\",children:device.type}),/*#__PURE__*/_jsx(\"p\",{className:\"device-card-last-seen\",children:device.lastSeen}),/*#__PURE__*/_jsxs(\"p\",{className:\"device-card-ip\",children:[\"IP: \",device.ipAddress]})]}),/*#__PURE__*/_jsx(\"button\",{className:\"pair-button\",onClick:()=>pairDevice(device.id),children:device.deviceType==='android'?'📱 Pair Android':device.deviceType==='windows'?'🖥️ Pair Windows':'🔗 Pair Device'})]},device.id)),/*#__PURE__*/_jsx(\"div\",{className:\"qr-buttons-container\",children:/*#__PURE__*/_jsx(\"button\",{className:\"qr-generate-button\",onClick:generateQRCode,children:\"Generate QR\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"section-title\",children:\"Additional Settings\"}),/*#__PURE__*/_jsxs(\"button\",{className:\"setting-item\",onClick:()=>setShowSyncSettings(true),children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-item-text\",children:\"Sync Settings\"}),/*#__PURE__*/_jsx(\"span\",{className:\"setting-item-arrow\",children:\"\\u203A\"})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"setting-item\",onClick:()=>{const newValue=!backgroundSyncEnabled;setBackgroundSyncEnabled(newValue);showMessage(newValue?'Background sync enabled':'Background sync disabled');},children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-item-text\",children:\"Background Sync\"}),/*#__PURE__*/_jsx(\"span\",{className:\"setting-item-value\",children:backgroundSyncEnabled?'on':'off'})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"setting-item\",onClick:()=>{setIsDeviceDiscoverable(!isDeviceDiscoverable);showMessage(!isDeviceDiscoverable?'🔍 Device is now discoverable by other Android and Windows devices':'🔒 Device is now hidden from discovery');},children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-item-text\",children:\"Device Discoverability\"}),/*#__PURE__*/_jsx(\"span\",{className:\"setting-item-value\",children:isDeviceDiscoverable?'on':'off'})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"setting-item\",onClick:()=>{if(isDiscovering){setIsDiscovering(false);showMessage('🔍 Network discovery stopped');}else{setIsDiscovering(true);showMessage('🔍 Network discovery started');}},children:[/*#__PURE__*/_jsx(\"span\",{className:\"setting-item-text\",children:\"Network Discovery\"}),/*#__PURE__*/_jsx(\"span\",{className:\"setting-item-value\",children:isDiscovering?'on':'off'})]})]})]})]})}),showSyncSettings&&/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",onClick:()=>setShowSyncSettings(false),children:/*#__PURE__*/_jsxs(\"div\",{className:\"sync-settings-modal\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"settings-header\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"settings-title\",children:\"Sync Settings\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-button\",onClick:()=>setShowSyncSettings(false),children:\"\\u2715\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"settings-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"sync-setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"sync-setting-label\",children:\"Auto Sync\"}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",className:\"sync-setting-checkbox\",checked:syncSettings.autoSync,onChange:e=>setSyncSettings(_objectSpread(_objectSpread({},syncSettings),{},{autoSync:e.target.checked}))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"sync-setting-item\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"sync-setting-label\",children:[\"Sync Delay: \",syncSettings.syncDelay,\" seconds\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"sync-setting-description\",children:syncSettings.syncDelay===0?'Instant sync':\"\".concat(syncSettings.syncDelay,\" second delay\")}),/*#__PURE__*/_jsxs(\"div\",{className:\"sync-delay-controls\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"sync-delay-button\",onClick:()=>setSyncSettings(_objectSpread(_objectSpread({},syncSettings),{},{syncDelay:Math.max(0,syncSettings.syncDelay-1)})),children:\"-\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"sync-delay-value\",children:[syncSettings.syncDelay,\"s\"]}),/*#__PURE__*/_jsx(\"button\",{className:\"sync-delay-button\",onClick:()=>setSyncSettings(_objectSpread(_objectSpread({},syncSettings),{},{syncDelay:Math.min(30,syncSettings.syncDelay+1)})),children:\"+\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"sync-setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"sync-setting-label\",children:\"Sync on Connect\"}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",className:\"sync-setting-checkbox\",checked:syncSettings.syncOnConnect,onChange:e=>setSyncSettings(_objectSpread(_objectSpread({},syncSettings),{},{syncOnConnect:e.target.checked}))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"sync-setting-item\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"sync-setting-label\",children:\"Bidirectional Sync\"}),/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",className:\"sync-setting-checkbox\",checked:syncSettings.bidirectional,onChange:e=>setSyncSettings(_objectSpread(_objectSpread({},syncSettings),{},{bidirectional:e.target.checked}))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"sync-setting-item\",children:/*#__PURE__*/_jsx(\"button\",{className:\"cross-platform-sync-button\",onClick:()=>showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.'),children:\"\\uD83D\\uDCF1 Windows \\u2194 Android Sync\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"sync-setting-item\",children:/*#__PURE__*/_jsx(\"button\",{className:\"cross-platform-sync-button\",onClick:()=>showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.'),children:\"\\uD83D\\uDDA5\\uFE0F Windows \\u2194 Windows Sync\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"sync-setting-item\",children:/*#__PURE__*/_jsx(\"button\",{className:\"cross-platform-sync-button\",onClick:()=>showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.'),children:\"\\uD83D\\uDE80 Sync All Devices\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-section\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"settings-section-title\",children:\"\\uD83D\\uDCCB Floating Overlay Button Settings\"}),/*#__PURE__*/_jsx(\"p\",{className:\"settings-description\",children:\"Configure the floating overlay button for quick access to connected device clipboards\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"settings-label\",children:\"Enable Floating Overlay Button\"}),/*#__PURE__*/_jsx(\"button\",{className:\"settings-toggle active\",onClick:()=>showMessage('📋 Floating overlay button is always enabled for accessibility'),children:/*#__PURE__*/_jsx(\"div\",{className:\"settings-toggle-thumb active\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"settings-label\",children:\"Show Device Count Badge\"}),/*#__PURE__*/_jsx(\"button\",{className:\"settings-toggle active\",onClick:()=>showMessage('🔢 Device count badge enabled'),children:/*#__PURE__*/_jsx(\"div\",{className:\"settings-toggle-thumb active\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-row\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"settings-label\",children:\"Auto-hide After Copy\"}),/*#__PURE__*/_jsx(\"button\",{className:\"settings-toggle active\",onClick:()=>showMessage('⏱️ Auto-hide after copy enabled'),children:/*#__PURE__*/_jsx(\"div\",{className:\"settings-toggle-thumb active\"})})]}),/*#__PURE__*/_jsx(\"p\",{className:\"settings-note\",children:\"\\uD83D\\uDCA1 The floating overlay button (\\uD83D\\uDCCB) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.\"})]})]})})]})})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "App", "thisDeviceClipboard", "setThisDeviceClipboard", "connectedDeviceClipboard", "setConnectedDeviceClipboard", "isEditingThisDevice", "setIsEditingThisDevice", "isEditingConnectedDevice", "setIsEditingConnectedDevice", "editingThisDeviceText", "setEditingThisDeviceText", "editingConnectedDeviceText", "setEditingConnectedDeviceText", "historyItems", "setHistoryItems", "id", "content", "timestamp", "showSettings", "setShowSettings", "showSyncSettings", "setShowSyncSettings", "isAlwaysOnTop", "setIsAlwaysOnTop", "successMessage", "setSuccessMessage", "isFloatingOverlayVisible", "setIsFloatingOverlayVisible", "backgroundSyncEnabled", "setBackgroundSyncEnabled", "networkDevices", "setNetworkDevices", "isDiscovering", "setIsDiscovering", "syncSettings", "setSyncSettings", "autoSync", "syncD<PERSON>y", "syncOnConnect", "bidirectional", "pairedDevices", "setPairedDevices", "name", "type", "status", "lastSeen", "ip<PERSON><PERSON><PERSON>", "discoveredDevices", "setDiscoveredDevices", "isDeviceDiscoverable", "setIsDeviceDiscoverable", "deviceInfo", "setDeviceInfo", "console", "log", "showMessage", "getDeviceInfo", "osVersion", "navigator", "userAgentData", "platform", "userAgent", "windowsMatch", "match", "version", "concat", "computerName", "getWindowsComputerName", "deviceName", "hasConnectedDevices", "some", "device", "deviceStatus", "prev", "_objectSpread", "error", "text", "setTimeout", "window", "chrome", "runtime", "sendMessage", "action", "trim", "toUpperCase", "e", "electronAPI", "nativeAPI", "api", "getComputerName", "process", "env", "COMPUTERNAME", "HOSTNAME", "response", "fetch", "method", "headers", "ok", "deviceSpecs", "json", "WebAssembly", "wmiResult", "simulateWMICall", "location", "hostname", "storedName", "localStorage", "getItem", "screen", "width", "height", "timezone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "language", "hashInput", "hash", "i", "length", "char", "charCodeAt", "hashHex", "Math", "abs", "toString", "slice", "setItem", "Promise", "resolve", "possibleComputerNames", "fingerprint", "index", "<PERSON><PERSON><PERSON>", "copyToClipboard", "clipboard", "writeText", "selectItem", "item", "deleteHistoryItem", "itemId", "updatedHistory", "filter", "startEditingThisDevice", "saveThisDeviceEdit", "newContent", "warn", "cancelThisDeviceEdit", "startEditingConnectedDevice", "saveConnectedDeviceEdit", "cancelConnectedDeviceEdit", "syncNow", "toggleAlwaysOnTop", "minimizeToTray", "toggleFloatingOverlay", "removeDevice", "deviceId", "updatedDevices", "connectDevice", "map", "disconnectDevice", "pairDevice", "deviceToPair", "find", "newPairedDevice", "refreshDiscovery", "foundDevices", "windowsDevices", "scanForWindowsDevices", "push", "androidDevices", "scanForAndroidDevices", "clipsyDevices", "scanForClipsyDevices", "deviceType", "scanQRCode", "generateQRCode", "connectionInfo", "port", "protocol", "Date", "now", "qrData", "JSON", "stringify", "qrCodeUrl", "encodeURIComponent", "qrModal", "document", "createElement", "className", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "closeModal", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "onclick", "target", "children", "src", "alt", "onClick", "title", "value", "onChange", "placeholder", "autoFocus", "stopPropagation", "disabled", "newValue", "checked", "max", "min"], "sources": ["D:/new git/Clipsy-Windows/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './App.css';\r\n\r\n/*\r\n * Windows Computer Name Detection\r\n *\r\n * In a real Windows application (Electron, PWA, or native app), the computer name\r\n * would be retrieved from Windows Device specifications using:\r\n *\r\n * 1. Windows API: GetComputerNameEx() function\r\n * 2. WMI Query: SELECT Name FROM Win32_ComputerSystem\r\n * 3. Registry: HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\ComputerName\\ComputerName\r\n * 4. PowerShell: $env:COMPUTERNAME or Get-ComputerInfo\r\n * 5. Settings Path: Settings > System > About > Device specifications > Device name\r\n *\r\n * This web version simulates the process and generates consistent computer names\r\n * based on browser fingerprinting for demonstration purposes.\r\n */\r\n\r\nfunction App() {\r\n  // State management matching Android app\r\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to Clipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\r\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\r\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\r\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\r\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\r\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\r\n\r\n  const [historyItems, setHistoryItems] = useState([\r\n    { id: '1', content: 'This is an older clipboard item. It\\'s shorter.', timestamp: '2 minutes ago' },\r\n    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },\r\n    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },\r\n    { id: '4', content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }', timestamp: '5 hours ago' }\r\n  ]);\r\n\r\n  // UI State\r\n  const [showSettings, setShowSettings] = useState(false);\r\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\r\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\r\n\r\n  // Background Sync State\r\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\r\n  const [networkDevices, setNetworkDevices] = useState([]);\r\n  const [isDiscovering, setIsDiscovering] = useState(false);\r\n\r\n  // Sync Settings - matching Android app\r\n  const [syncSettings, setSyncSettings] = useState({\r\n    autoSync: true,\r\n    syncDelay: 2,\r\n    syncOnConnect: true,\r\n    bidirectional: true\r\n  });\r\n\r\n  // Device Management - matching Android app\r\n  const [pairedDevices, setPairedDevices] = useState([\r\n    {\r\n      id: 'android-1',\r\n      name: 'Android Phone - Personal',\r\n      type: 'Android 14',\r\n      status: 'connected',\r\n      lastSeen: '2 min ago',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'linux-1',\r\n      name: 'Ubuntu Server - Home',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'disconnected',\r\n      lastSeen: '1 hour ago',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  const [discoveredDevices, setDiscoveredDevices] = useState([]);\r\n  const [isDeviceDiscoverable, setIsDeviceDiscoverable] = useState(true);\r\n\r\n  // Device info state - get actual Windows device details from Device specifications\r\n  const [deviceInfo, setDeviceInfo] = useState({\r\n    name: 'Loading computer name from Device specifications...',\r\n    type: 'Windows',\r\n    status: 'active',\r\n    lastSeen: 'now',\r\n    ipAddress: '*************'\r\n  });\r\n\r\n  // Device discoverability service\r\n  React.useEffect(() => {\r\n    if (isDeviceDiscoverable) {\r\n      // Start discovery service\r\n      console.log('🔍 Device is now discoverable by other devices');\r\n      showMessage('🔍 This device is discoverable by other Android and Windows devices');\r\n    } else {\r\n      console.log('🔒 Device discovery disabled');\r\n    }\r\n  }, [isDeviceDiscoverable]);\r\n\r\n  // Get actual Windows computer name and OS details\r\n  React.useEffect(() => {\r\n    const getDeviceInfo = async () => {\r\n      try {\r\n        let osVersion = 'Windows';\r\n\r\n        // Get OS version from user agent\r\n        if (navigator.userAgentData) {\r\n          const platform = navigator.userAgentData.platform;\r\n          osVersion = platform || 'Windows';\r\n        } else if (navigator.userAgent) {\r\n          const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\r\n          if (windowsMatch) {\r\n            const version = windowsMatch[1];\r\n            switch (version) {\r\n              case '10.0': osVersion = 'Windows 10/11'; break;\r\n              case '6.3': osVersion = 'Windows 8.1'; break;\r\n              case '6.2': osVersion = 'Windows 8'; break;\r\n              case '6.1': osVersion = 'Windows 7'; break;\r\n              default: osVersion = `Windows NT ${version}`;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Get the actual computer name\r\n        const computerName = await getWindowsComputerName();\r\n        const deviceName = `${computerName} - ${osVersion}`;\r\n\r\n        // Check if any devices are connected\r\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\r\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\r\n\r\n        setDeviceInfo(prev => ({\r\n          ...prev,\r\n          name: deviceName,\r\n          type: osVersion,\r\n          status: deviceStatus,\r\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\r\n        }));\r\n\r\n        console.log('Device info updated:', { computerName, deviceName, osVersion });\r\n      } catch (error) {\r\n        console.log('Could not get device info:', error);\r\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\r\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\r\n\r\n        setDeviceInfo(prev => ({\r\n          ...prev,\r\n          name: 'Windows PC - Main',\r\n          type: 'Windows',\r\n          status: deviceStatus,\r\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\r\n        }));\r\n      }\r\n    };\r\n\r\n    getDeviceInfo();\r\n  }, [pairedDevices]);\r\n\r\n  // Functions\r\n  const showMessage = (text) => {\r\n    setSuccessMessage(text);\r\n    setTimeout(() => setSuccessMessage(''), 3000);\r\n  };\r\n\r\n  // Get Windows computer name from Device specifications\r\n  const getWindowsComputerName = async () => {\r\n    try {\r\n      // Method 1: Try to get from Windows Device specifications via WMI (if available)\r\n      if (window.chrome && window.chrome.runtime) {\r\n        try {\r\n          // Try Chrome extension API if available\r\n          const computerName = await window.chrome.runtime.sendMessage({\r\n            action: 'getComputerName'\r\n          });\r\n          if (computerName) return computerName.trim().toUpperCase();\r\n        } catch (e) {\r\n          console.log('Chrome extension method failed:', e);\r\n        }\r\n      }\r\n\r\n      // Method 2: Try PowerShell via Electron or native app\r\n      if (window.electronAPI || window.nativeAPI) {\r\n        try {\r\n          const api = window.electronAPI || window.nativeAPI;\r\n          const computerName = await api.getComputerName();\r\n          if (computerName) return computerName.trim().toUpperCase();\r\n        } catch (e) {\r\n          console.log('Native API method failed:', e);\r\n        }\r\n      }\r\n\r\n      // Method 3: Try to get from environment variables (Node.js/Electron environment)\r\n      if (typeof process !== 'undefined' && process.env) {\r\n        if (process.env.COMPUTERNAME) return process.env.COMPUTERNAME.toUpperCase();\r\n        if (process.env.HOSTNAME) return process.env.HOSTNAME.toUpperCase();\r\n      }\r\n\r\n      // Method 4: Try Windows Device specifications API endpoint\r\n      try {\r\n        const response = await fetch('/api/device-specs', {\r\n          method: 'GET',\r\n          headers: { 'Content-Type': 'application/json' }\r\n        });\r\n        if (response.ok) {\r\n          const deviceSpecs = await response.json();\r\n          if (deviceSpecs.computerName) return deviceSpecs.computerName.toUpperCase();\r\n          if (deviceSpecs.deviceName) return deviceSpecs.deviceName.toUpperCase();\r\n        }\r\n      } catch (e) {\r\n        console.log('Device specs API method failed:', e);\r\n      }\r\n\r\n      // Method 5: Try WMI via WebAssembly (if available)\r\n      try {\r\n        if (window.WebAssembly) {\r\n          // This would require a WASM module to access Windows WMI\r\n          // For now, we'll simulate the call\r\n          const wmiResult = await simulateWMICall();\r\n          if (wmiResult) return wmiResult.toUpperCase();\r\n        }\r\n      } catch (e) {\r\n        console.log('WMI method failed:', e);\r\n      }\r\n\r\n      // Method 6: Use network hostname if available and valid\r\n      if (window.location.hostname &&\r\n          window.location.hostname !== 'localhost' &&\r\n          window.location.hostname !== '127.0.0.1' &&\r\n          !window.location.hostname.match(/^\\d+\\.\\d+\\.\\d+\\.\\d+$/)) {\r\n        return window.location.hostname.toUpperCase();\r\n      }\r\n\r\n      // Method 7: Generate or retrieve persistent computer name\r\n      let storedName = localStorage.getItem('clipsy-computer-name');\r\n      if (!storedName) {\r\n        // Create a more Windows-like computer name\r\n        const userAgent = navigator.userAgent;\r\n        const screen = `${window.screen.width}x${window.screen.height}`;\r\n        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n        const language = navigator.language;\r\n        const platform = navigator.platform;\r\n\r\n        // Create a deterministic hash for this browser/system\r\n        const hashInput = `${userAgent}-${screen}-${timezone}-${language}-${platform}`;\r\n        let hash = 0;\r\n        for (let i = 0; i < hashInput.length; i++) {\r\n          const char = hashInput.charCodeAt(i);\r\n          hash = ((hash << 5) - hash) + char;\r\n          hash = hash & hash; // Convert to 32-bit integer\r\n        }\r\n\r\n        // Generate Windows-style computer name\r\n        const hashHex = Math.abs(hash).toString(16).toUpperCase().slice(0, 6);\r\n        storedName = `DESKTOP-${hashHex}`;\r\n        localStorage.setItem('clipsy-computer-name', storedName);\r\n\r\n        console.log('Generated computer name:', storedName);\r\n      }\r\n\r\n      return storedName;\r\n    } catch (error) {\r\n      console.log('All computer name methods failed:', error);\r\n      return 'DESKTOP-CLIPSY';\r\n    }\r\n  };\r\n\r\n  // Simulate WMI call for computer name (would access Windows Device specifications)\r\n  const simulateWMICall = async () => {\r\n    return new Promise((resolve) => {\r\n      // In a real Windows application, this would access:\r\n      // Settings > System > About > Device specifications > Device name\r\n      // Using Windows Management Instrumentation (WMI) or Windows API\r\n\r\n      // Simulate getting actual Windows computer names from Device specifications\r\n      const possibleComputerNames = [\r\n        'DESKTOP-ABC123',\r\n        'LAPTOP-DEF456',\r\n        'WORKSTATION-789',\r\n        'PC-USER-001',\r\n        'GAMING-RIG-01',\r\n        'OFFICE-PC-02'\r\n      ];\r\n\r\n      // Use a deterministic selection based on browser fingerprint\r\n      const userAgent = navigator.userAgent;\r\n      const screen = `${window.screen.width}x${window.screen.height}`;\r\n      const fingerprint = `${userAgent}-${screen}`;\r\n\r\n      let hash = 0;\r\n      for (let i = 0; i < fingerprint.length; i++) {\r\n        const char = fingerprint.charCodeAt(i);\r\n        hash = ((hash << 5) - hash) + char;\r\n        hash = hash & hash;\r\n      }\r\n\r\n      const index = Math.abs(hash) % possibleComputerNames.length;\r\n      const selectedName = possibleComputerNames[index];\r\n\r\n      console.log('Simulated WMI call - Device specifications computer name:', selectedName);\r\n\r\n      // Simulate API delay\r\n      setTimeout(() => resolve(selectedName), 200);\r\n    });\r\n  };\r\n\r\n  const copyToClipboard = async (content) => {\r\n    try {\r\n      await navigator.clipboard.writeText(content);\r\n      setThisDeviceClipboard(content);\r\n      showMessage('✅ Text copied to clipboard!');\r\n    } catch (error) {\r\n      console.error('Failed to copy to clipboard:', error);\r\n      showMessage('❌ Failed to copy to clipboard');\r\n    }\r\n  };\r\n\r\n  const selectItem = (item) => {\r\n    copyToClipboard(item.content);\r\n    setConnectedDeviceClipboard(item.content);\r\n  };\r\n\r\n  const deleteHistoryItem = (itemId) => {\r\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\r\n    setHistoryItems(updatedHistory);\r\n    showMessage('🗑️ History item deleted!');\r\n  };\r\n\r\n  // Device edit functions\r\n  const startEditingThisDevice = () => {\r\n    setEditingThisDeviceText(thisDeviceClipboard);\r\n    setIsEditingThisDevice(true);\r\n  };\r\n\r\n  const saveThisDeviceEdit = async () => {\r\n    const newContent = editingThisDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setThisDeviceClipboard(newContent);\r\n    try {\r\n      await navigator.clipboard.writeText(newContent);\r\n    } catch (error) {\r\n      console.warn('Failed to update clipboard:', error);\r\n    }\r\n\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n    showMessage('✅ This Device clipboard updated!');\r\n  };\r\n\r\n  const cancelThisDeviceEdit = () => {\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n  };\r\n\r\n  const startEditingConnectedDevice = () => {\r\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\r\n    setIsEditingConnectedDevice(true);\r\n  };\r\n\r\n  const saveConnectedDeviceEdit = () => {\r\n    const newContent = editingConnectedDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setConnectedDeviceClipboard(newContent);\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n    showMessage('✅ Connected Device clipboard updated!');\r\n  };\r\n\r\n  const cancelConnectedDeviceEdit = () => {\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n  };\r\n\r\n  const syncNow = () => {\r\n    showMessage('🔄 Syncing with paired devices...');\r\n    setTimeout(() => {\r\n      showMessage('✅ Sync completed!');\r\n    }, 1000);\r\n  };\r\n\r\n  const toggleAlwaysOnTop = () => {\r\n    setIsAlwaysOnTop(!isAlwaysOnTop);\r\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\r\n  };\r\n\r\n  const minimizeToTray = () => {\r\n    showMessage('➖ Minimizing to background...');\r\n  };\r\n\r\n  const toggleFloatingOverlay = () => {\r\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\r\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\r\n  };\r\n\r\n  // Device Management Functions\r\n  const removeDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🗑️ Device removed from paired devices');\r\n  };\r\n\r\n  const connectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'connected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('✅ Device connected successfully');\r\n  };\r\n\r\n  const disconnectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'disconnected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🔌 Device disconnected');\r\n  };\r\n\r\n  const pairDevice = (deviceId) => {\r\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\r\n    if (deviceToPair) {\r\n      const newPairedDevice = {\r\n        ...deviceToPair,\r\n        status: 'connected',\r\n        lastSeen: 'Just now'\r\n      };\r\n      setPairedDevices([...pairedDevices, newPairedDevice]);\r\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\r\n    }\r\n  };\r\n\r\n  const refreshDiscovery = async () => {\r\n    if (isDiscovering) return;\r\n\r\n    setIsDiscovering(true);\r\n    setNetworkDevices([]); // Clear previous results\r\n    setDiscoveredDevices([]); // Clear discovered devices\r\n    showMessage('🔍 Searching for Android and Windows devices...');\r\n\r\n    try {\r\n      // Start device discovery process\r\n      const foundDevices = [];\r\n\r\n      // Phase 1: Network scanning for Windows devices\r\n      showMessage('🖥️ Scanning for Windows devices...');\r\n      const windowsDevices = await scanForWindowsDevices();\r\n      foundDevices.push(...windowsDevices);\r\n\r\n      // Update UI with Windows devices found\r\n      if (windowsDevices.length > 0) {\r\n        setNetworkDevices(prev => [...prev, ...windowsDevices]);\r\n        setDiscoveredDevices(prev => [...prev, ...windowsDevices]);\r\n        showMessage(`💻 Found ${windowsDevices.length} Windows device(s)`);\r\n      }\r\n\r\n      // Phase 2: Broadcast discovery for Android devices\r\n      showMessage('📱 Scanning for Android devices...');\r\n      const androidDevices = await scanForAndroidDevices();\r\n      foundDevices.push(...androidDevices);\r\n\r\n      // Update UI with Android devices found\r\n      if (androidDevices.length > 0) {\r\n        setNetworkDevices(prev => [...prev, ...androidDevices]);\r\n        setDiscoveredDevices(prev => [...prev, ...androidDevices]);\r\n        showMessage(`📱 Found ${androidDevices.length} Android device(s)`);\r\n      }\r\n\r\n      // Phase 3: mDNS/Bonjour discovery for all Clipsy devices\r\n      showMessage('🔍 Scanning for Clipsy-enabled devices...');\r\n      const clipsyDevices = await scanForClipsyDevices();\r\n      foundDevices.push(...clipsyDevices);\r\n\r\n      // Update UI with Clipsy devices found\r\n      if (clipsyDevices.length > 0) {\r\n        setNetworkDevices(prev => [...prev, ...clipsyDevices]);\r\n        setDiscoveredDevices(prev => [...prev, ...clipsyDevices]);\r\n        showMessage(`🔗 Found ${clipsyDevices.length} Clipsy-enabled device(s)`);\r\n      }\r\n\r\n      // Final results\r\n      setIsDiscovering(false);\r\n      if (foundDevices.length > 0) {\r\n        showMessage(`✅ Discovery complete! Found ${foundDevices.length} available devices.`);\r\n      } else {\r\n        showMessage('❌ No devices found. Make sure devices are on the same network.');\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('Discovery error:', error);\r\n      setIsDiscovering(false);\r\n      showMessage('❌ Discovery failed. Please check network connection.');\r\n    }\r\n  };\r\n\r\n  // Device scanning functions\r\n  const scanForWindowsDevices = async () => {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        const windowsDevices = [\r\n          {\r\n            id: 'windows-laptop-001',\r\n            name: 'LAPTOP-WORK123',\r\n            type: 'Windows 11 Pro',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'windows'\r\n          },\r\n          {\r\n            id: 'windows-desktop-001',\r\n            name: 'DESKTOP-GAMING',\r\n            type: 'Windows 10',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'windows'\r\n          },\r\n          {\r\n            id: 'windows-surface-001',\r\n            name: 'SURFACE-PRO9',\r\n            type: 'Windows 11',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'windows'\r\n          }\r\n        ];\r\n        resolve(windowsDevices);\r\n      }, 1500);\r\n    });\r\n  };\r\n\r\n  const scanForAndroidDevices = async () => {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        const androidDevices = [\r\n          {\r\n            id: 'android-samsung-001',\r\n            name: 'Samsung Galaxy S23',\r\n            type: 'Android 14',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'android'\r\n          },\r\n          {\r\n            id: 'android-oneplus-001',\r\n            name: 'OnePlus 11',\r\n            type: 'Android 14',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'android'\r\n          },\r\n          {\r\n            id: 'android-pixel-001',\r\n            name: 'Google Pixel 8',\r\n            type: 'Android 14',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'android'\r\n          }\r\n        ];\r\n        resolve(androidDevices);\r\n      }, 2000);\r\n    });\r\n  };\r\n\r\n  const scanForClipsyDevices = async () => {\r\n    return new Promise((resolve) => {\r\n      setTimeout(() => {\r\n        const clipsyDevices = [\r\n          {\r\n            id: 'clipsy-tablet-001',\r\n            name: 'iPad Pro (Clipsy)',\r\n            type: 'iPadOS 17',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'ios'\r\n          },\r\n          {\r\n            id: 'clipsy-mac-001',\r\n            name: 'MacBook Pro M3',\r\n            type: 'macOS Sonoma',\r\n            status: 'discovering',\r\n            lastSeen: 'Available for pairing',\r\n            ipAddress: '*************',\r\n            deviceType: 'macos'\r\n          }\r\n        ];\r\n        resolve(clipsyDevices);\r\n      }, 1000);\r\n    });\r\n  };\r\n\r\n  const scanQRCode = () => {\r\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\r\n  };\r\n\r\n  const generateQRCode = () => {\r\n    // Generate connection info for QR code\r\n    const connectionInfo = {\r\n      deviceName: deviceInfo.name,\r\n      deviceType: deviceInfo.type,\r\n      ipAddress: deviceInfo.ipAddress,\r\n      port: 3001,\r\n      protocol: 'clipsy-sync',\r\n      timestamp: Date.now()\r\n    };\r\n\r\n    const qrData = JSON.stringify(connectionInfo);\r\n    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrData)}`;\r\n\r\n    // Create and show QR code modal\r\n    const qrModal = document.createElement('div');\r\n    qrModal.className = 'qr-modal-overlay';\r\n    qrModal.innerHTML = `\r\n      <div class=\"qr-modal-content\">\r\n        <div class=\"qr-modal-header\">\r\n          <h3>📱 Scan to Connect Android Device</h3>\r\n          <button class=\"qr-modal-close\">✕</button>\r\n        </div>\r\n        <div class=\"qr-modal-body\">\r\n          <img src=\"${qrCodeUrl}\" alt=\"QR Code\" class=\"qr-code-image\" />\r\n          <p class=\"qr-instructions\">\r\n            1. Open Clipsy app on your Android device<br/>\r\n            2. Go to Settings → Device Discovery<br/>\r\n            3. Tap \"Scan QR\" and scan this code<br/>\r\n            4. Your devices will be paired automatically\r\n          </p>\r\n          <div class=\"qr-device-info\">\r\n            <p><strong>Device:</strong> ${deviceInfo.name}</p>\r\n            <p><strong>IP:</strong> ${deviceInfo.ipAddress}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    `;\r\n\r\n    document.body.appendChild(qrModal);\r\n\r\n    // Close modal functionality\r\n    const closeModal = () => {\r\n      document.body.removeChild(qrModal);\r\n    };\r\n\r\n    qrModal.querySelector('.qr-modal-close').onclick = closeModal;\r\n    qrModal.onclick = (e) => {\r\n      if (e.target === qrModal) closeModal();\r\n    };\r\n\r\n    showMessage('📱 QR Code generated! Scan with Android Clipsy app to connect.');\r\n  };\r\n\r\n  return (\r\n    <div className=\"app-container\">\r\n      {/* Header */}\r\n      <div className=\"header\">\r\n        <div className=\"title-container\">\r\n          <div className=\"logo-container\">\r\n            <img\r\n              src=\"/clipsy-logo-no-bg.png\"\r\n              alt=\"Clipsy Logo\"\r\n              className=\"app-logo\"\r\n            />\r\n            <div className={`connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`}></div>\r\n          </div>\r\n          <h1 className=\"title\">Clipsy</h1>\r\n        </div>\r\n        <div className=\"header-actions\">\r\n          <button\r\n            className={`icon-button ${isFloatingOverlayVisible ? 'active' : ''}`}\r\n            onClick={toggleFloatingOverlay}\r\n            title=\"Toggle floating clipboard widget\"\r\n          >\r\n            📋\r\n          </button>\r\n          <button\r\n            className={`icon-button ${isAlwaysOnTop ? 'active' : ''}`}\r\n            onClick={toggleAlwaysOnTop}\r\n            title=\"Pin to top\"\r\n          >\r\n            <div className=\"pin-icon\">\r\n              <div className=\"pin-head\"></div>\r\n              <div className=\"pin-body\"></div>\r\n            </div>\r\n          </button>\r\n          <button className=\"icon-button\" onClick={minimizeToTray} title=\"Minimize\">\r\n            ➖\r\n          </button>\r\n          <button className=\"icon-button\" onClick={() => setShowSettings(true)} title=\"Settings\">\r\n            ⚙️\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"success-message\">\r\n          <span>{successMessage}</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      <div className=\"main-content\">\r\n        {/* This Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <div className=\"device-section-title-container\">\r\n              <h2 className=\"device-section-title\">💻 This Device</h2>\r\n              <p className=\"device-section-subtitle\">{deviceInfo.name}</p>\r\n            </div>\r\n          </div>\r\n\r\n          {isEditingThisDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingThisDeviceText}\r\n                onChange={(e) => setEditingThisDeviceText(e.target.value)}\r\n                placeholder=\"Edit this device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button this-device\" onClick={saveThisDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button this-device\" onClick={cancelThisDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content this-device-content\"\r\n                onClick={() => copyToClipboard(thisDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{thisDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Real-time sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingThisDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Connected Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <h2 className=\"device-section-title\">🔗 Connected Device</h2>\r\n            <p className=\"device-section-subtitle\">Android Phone - Personal 🟢</p>\r\n          </div>\r\n\r\n          {isEditingConnectedDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingConnectedDeviceText}\r\n                onChange={(e) => setEditingConnectedDeviceText(e.target.value)}\r\n                placeholder=\"Edit connected device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button connected-device\" onClick={saveConnectedDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button connected-device\" onClick={cancelConnectedDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content connected-device-content\"\r\n                onClick={() => copyToClipboard(connectedDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{connectedDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Bidirectional sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingConnectedDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Clipboard History */}\r\n        <h2 className=\"history-title\">Clipboard History</h2>\r\n        <div className=\"history-list\">\r\n          {historyItems.map((item) => (\r\n            <div\r\n              key={item.id}\r\n              className=\"history-item\"\r\n              onClick={() => selectItem(item)}\r\n            >\r\n              <div className=\"history-item-header\">\r\n                <span className=\"timestamp\">{item.timestamp}</span>\r\n                <button\r\n                  className=\"delete-button\"\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    deleteHistoryItem(item.id);\r\n                  }}\r\n                >\r\n                  ✕\r\n                </button>\r\n              </div>\r\n              <p className=\"item-content\">{item.content}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Floating Sync Button */}\r\n      <button className=\"floating-sync-button\" onClick={syncNow}>\r\n        <img src=\"/sync.png\" alt=\"Sync\" className=\"sync-icon\" />\r\n      </button>\r\n\r\n      {/* Settings Modal */}\r\n      {showSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSettings(false)}>\r\n          <div className=\"settings-sidebar\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              {/* Device Info */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Device Info</h3>\r\n                <div className=\"device-info-card\">\r\n                  <p className=\"device-name\">{deviceInfo.name}</p>\r\n                  <p className=\"device-detail\">{deviceInfo.type}</p>\r\n                  <p className=\"device-detail\">IP: {deviceInfo.ipAddress}</p>\r\n                  <div className=\"device-status-row\">\r\n                    <div className={`device-status-indicator ${deviceInfo.status}`}></div>\r\n                    <span className={`device-status-text ${deviceInfo.status}`}>\r\n                      Status: {deviceInfo.status === 'active' ? 'Connected' : 'Disconnected'}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"device-status-row\">\r\n                    <div className={`device-status-indicator ${isDeviceDiscoverable ? 'active' : 'disconnected'}`}></div>\r\n                    <span className={`device-status-text ${isDeviceDiscoverable ? 'active' : 'disconnected'}`}>\r\n                      Discoverable: {isDeviceDiscoverable ? 'Visible to other devices' : 'Hidden from discovery'}\r\n                    </span>\r\n                  </div>\r\n                  {deviceInfo.status === 'disconnected' && (\r\n                    <p className=\"device-detail disconnected-notice\">\r\n                      ⚠️ No devices connected - Use QR code or device discovery to connect Android devices\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Paired Devices */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Paired Devices</h3>\r\n                {pairedDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No paired devices found</p>\r\n                    <p className=\"empty-device-subtext\">Use QR code or device discovery to pair devices</p>\r\n                  </div>\r\n                ) : (\r\n                  pairedDevices.map((device) => (\r\n                    <div key={device.id} className=\"enhanced-device-card\">\r\n                      {/* Remove Button - White X at top right corner */}\r\n                      <button\r\n                        className=\"remove-button-top-right\"\r\n                        onClick={() => removeDevice(device.id)}\r\n                      >\r\n                        ×\r\n                      </button>\r\n\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <div className=\"device-status-row\">\r\n                          <div className={`device-status-indicator ${device.status}`}></div>\r\n                          <span className={`device-status-text ${device.status}`}>\r\n                            {device.status === 'connected' ? 'Connected' : 'Disconnected'} • {device.lastSeen}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Connect/Disconnect Button - Bottom Right Corner */}\r\n                      <button\r\n                        className={`device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`}\r\n                        onClick={() => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id)}\r\n                      >\r\n                        {device.status === 'connected' ? 'Disconnect' : 'Connect'}\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n              </div>\r\n\r\n              {/* Device Discovery */}\r\n              <div className=\"settings-section\">\r\n                <div className=\"section-header\">\r\n                  <h3 className=\"section-title\">Device Discovery</h3>\r\n                  <button\r\n                    className={`discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`}\r\n                    onClick={refreshDiscovery}\r\n                    disabled={isDiscovering}\r\n                  >\r\n                    {isDiscovering ? '🔍 Scanning...' : 'Discover'}\r\n                  </button>\r\n                </div>\r\n                {networkDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No devices found</p>\r\n                    <p className=\"empty-device-subtext\">\r\n                      {isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'}\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  networkDevices.map((device) => (\r\n                    <div key={device.id} className=\"discovered-device-card\">\r\n                      <div className=\"device-info\">\r\n                        <div className=\"device-name-with-icon\">\r\n                          <span className=\"device-type-icon\">\r\n                            {device.deviceType === 'android' ? '📱' :\r\n                             device.deviceType === 'windows' ? '🖥️' :\r\n                             device.deviceType === 'ios' ? '📱' :\r\n                             device.deviceType === 'macos' ? '💻' : '🖥️'}\r\n                          </span>\r\n                          <p className=\"device-card-name\">{device.name}</p>\r\n                        </div>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <p className=\"device-card-last-seen\">{device.lastSeen}</p>\r\n                        <p className=\"device-card-ip\">IP: {device.ipAddress}</p>\r\n                      </div>\r\n                      <button\r\n                        className=\"pair-button\"\r\n                        onClick={() => pairDevice(device.id)}\r\n                      >\r\n                        {device.deviceType === 'android' ? '📱 Pair Android' :\r\n                         device.deviceType === 'windows' ? '🖥️ Pair Windows' :\r\n                         '🔗 Pair Device'}\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n\r\n                <div className=\"qr-buttons-container\">\r\n                  <button className=\"qr-generate-button\" onClick={generateQRCode}>\r\n                    Generate QR\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Additional Settings */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Additional Settings</h3>\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => setShowSyncSettings(true)}\r\n                >\r\n                  <span className=\"setting-item-text\">Sync Settings</span>\r\n                  <span className=\"setting-item-arrow\">›</span>\r\n                </button>\r\n\r\n                {/* Background Sync Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    const newValue = !backgroundSyncEnabled;\r\n                    setBackgroundSyncEnabled(newValue);\r\n                    showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Background Sync</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {backgroundSyncEnabled ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n\r\n                {/* Device Discoverability Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    setIsDeviceDiscoverable(!isDeviceDiscoverable);\r\n                    showMessage(\r\n                      !isDeviceDiscoverable\r\n                        ? '🔍 Device is now discoverable by other Android and Windows devices'\r\n                        : '🔒 Device is now hidden from discovery'\r\n                    );\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Device Discoverability</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {isDeviceDiscoverable ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n\r\n                {/* Network Discovery Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    if (isDiscovering) {\r\n                      setIsDiscovering(false);\r\n                      showMessage('🔍 Network discovery stopped');\r\n                    } else {\r\n                      setIsDiscovering(true);\r\n                      showMessage('🔍 Network discovery started');\r\n                    }\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Network Discovery</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {isDiscovering ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Sync Settings Modal */}\r\n      {showSyncSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSyncSettings(false)}>\r\n          <div className=\"sync-settings-modal\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Sync Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSyncSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              <div className=\"settings-section\">\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Auto Sync</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.autoSync}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, autoSync: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Sync Delay: {syncSettings.syncDelay} seconds</label>\r\n                  <p className=\"sync-setting-description\">\r\n                    {syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`}\r\n                  </p>\r\n                  <div className=\"sync-delay-controls\">\r\n                    <button\r\n                      className=\"sync-delay-button\"\r\n                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.max(0, syncSettings.syncDelay - 1)})}\r\n                    >\r\n                      -\r\n                    </button>\r\n                    <span className=\"sync-delay-value\">{syncSettings.syncDelay}s</span>\r\n                    <button\r\n                      className=\"sync-delay-button\"\r\n                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.min(30, syncSettings.syncDelay + 1)})}\r\n                    >\r\n                      +\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Sync on Connect</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.syncOnConnect}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, syncOnConnect: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Bidirectional Sync</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.bidirectional}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, bidirectional: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                {/* Cross-Platform Sync Controls */}\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.')}\r\n                  >\r\n                    📱 Windows ↔ Android Sync\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.')}\r\n                  >\r\n                    🖥️ Windows ↔ Windows Sync\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.')}\r\n                  >\r\n                    🚀 Sync All Devices\r\n                  </button>\r\n                </div>\r\n\r\n                {/* Floating Overlay Button Settings */}\r\n                <div className=\"settings-section\">\r\n                  <h3 className=\"settings-section-title\">📋 Floating Overlay Button Settings</h3>\r\n                  <p className=\"settings-description\">\r\n                    Configure the floating overlay button for quick access to connected device clipboards\r\n                  </p>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Enable Floating Overlay Button</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('📋 Floating overlay button is always enabled for accessibility')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Show Device Count Badge</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('🔢 Device count badge enabled')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Auto-hide After Copy</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('⏱️ Auto-hide after copy enabled')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <p className=\"settings-note\">\r\n                    💡 The floating overlay button (📋) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": "8GAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,WAAW,CAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAdA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAgBA,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb;AACA,KAAM,CAACC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGR,QAAQ,CAAC,sIAAsI,CAAC,CACtM,KAAM,CAACS,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGV,QAAQ,CAAC,0IAA0I,CAAC,CACpN,KAAM,CAACW,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAACa,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CAC/E,KAAM,CAACe,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGhB,QAAQ,CAAC,EAAE,CAAC,CACtE,KAAM,CAACiB,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGlB,QAAQ,CAAC,EAAE,CAAC,CAEhF,KAAM,CAACmB,YAAY,CAAEC,eAAe,CAAC,CAAGpB,QAAQ,CAAC,CAC/C,CAAEqB,EAAE,CAAE,GAAG,CAAEC,OAAO,CAAE,iDAAiD,CAAEC,SAAS,CAAE,eAAgB,CAAC,CACnG,CAAEF,EAAE,CAAE,GAAG,CAAEC,OAAO,CAAE,gLAAgL,CAAEC,SAAS,CAAE,gBAAiB,CAAC,CACnO,CAAEF,EAAE,CAAE,GAAG,CAAEC,OAAO,CAAE,+BAA+B,CAAEC,SAAS,CAAE,YAAa,CAAC,CAC9E,CAAEF,EAAE,CAAE,GAAG,CAAEC,OAAO,CAAE,sEAAsE,CAAEC,SAAS,CAAE,aAAc,CAAC,CACvH,CAAC,CAEF;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC0B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC4B,aAAa,CAAEC,gBAAgB,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAC8B,cAAc,CAAEC,iBAAiB,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACgC,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CAE/E;AACA,KAAM,CAACkC,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CACxE,KAAM,CAACoC,cAAc,CAAEC,iBAAiB,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACsC,aAAa,CAAEC,gBAAgB,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CAEzD;AACA,KAAM,CAACwC,YAAY,CAAEC,eAAe,CAAC,CAAGzC,QAAQ,CAAC,CAC/C0C,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,CAAC,CACZC,aAAa,CAAE,IAAI,CACnBC,aAAa,CAAE,IACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG/C,QAAQ,CAAC,CACjD,CACEqB,EAAE,CAAE,WAAW,CACf2B,IAAI,CAAE,0BAA0B,CAChCC,IAAI,CAAE,YAAY,CAClBC,MAAM,CAAE,WAAW,CACnBC,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,eACb,CAAC,CACD,CACE/B,EAAE,CAAE,SAAS,CACb2B,IAAI,CAAE,sBAAsB,CAC5BC,IAAI,CAAE,cAAc,CACpBC,MAAM,CAAE,cAAc,CACtBC,QAAQ,CAAE,YAAY,CACtBC,SAAS,CAAE,eACb,CAAC,CACF,CAAC,CAEF,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACuD,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CAEtE;AACA,KAAM,CAACyD,UAAU,CAAEC,aAAa,CAAC,CAAG1D,QAAQ,CAAC,CAC3CgD,IAAI,CAAE,qDAAqD,CAC3DC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,KAAK,CACfC,SAAS,CAAE,eACb,CAAC,CAAC,CAEF;AACArD,KAAK,CAACE,SAAS,CAAC,IAAM,CACpB,GAAIsD,oBAAoB,CAAE,CACxB;AACAI,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC,CAC7DC,WAAW,CAAC,qEAAqE,CAAC,CACpF,CAAC,IAAM,CACLF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAC7C,CACF,CAAC,CAAE,CAACL,oBAAoB,CAAC,CAAC,CAE1B;AACAxD,KAAK,CAACE,SAAS,CAAC,IAAM,CACpB,KAAM,CAAA6D,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,GAAI,CAAAC,SAAS,CAAG,SAAS,CAEzB;AACA,GAAIC,SAAS,CAACC,aAAa,CAAE,CAC3B,KAAM,CAAAC,QAAQ,CAAGF,SAAS,CAACC,aAAa,CAACC,QAAQ,CACjDH,SAAS,CAAGG,QAAQ,EAAI,SAAS,CACnC,CAAC,IAAM,IAAIF,SAAS,CAACG,SAAS,CAAE,CAC9B,KAAM,CAAAC,YAAY,CAAGJ,SAAS,CAACG,SAAS,CAACE,KAAK,CAAC,uBAAuB,CAAC,CACvE,GAAID,YAAY,CAAE,CAChB,KAAM,CAAAE,OAAO,CAAGF,YAAY,CAAC,CAAC,CAAC,CAC/B,OAAQE,OAAO,EACb,IAAK,MAAM,CAAEP,SAAS,CAAG,eAAe,CAAE,MAC1C,IAAK,KAAK,CAAEA,SAAS,CAAG,aAAa,CAAE,MACvC,IAAK,KAAK,CAAEA,SAAS,CAAG,WAAW,CAAE,MACrC,IAAK,KAAK,CAAEA,SAAS,CAAG,WAAW,CAAE,MACrC,QAASA,SAAS,eAAAQ,MAAA,CAAiBD,OAAO,CAAE,CAC9C,CACF,CACF,CAEA;AACA,KAAM,CAAAE,YAAY,CAAG,KAAM,CAAAC,sBAAsB,CAAC,CAAC,CACnD,KAAM,CAAAC,UAAU,IAAAH,MAAA,CAAMC,YAAY,QAAAD,MAAA,CAAMR,SAAS,CAAE,CAEnD;AACA,KAAM,CAAAY,mBAAmB,CAAG7B,aAAa,CAAC8B,IAAI,CAACC,MAAM,EAAIA,MAAM,CAAC3B,MAAM,GAAK,WAAW,CAAC,CACvF,KAAM,CAAA4B,YAAY,CAAGH,mBAAmB,CAAG,QAAQ,CAAG,cAAc,CAEpEjB,aAAa,CAACqB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACbD,IAAI,MACP/B,IAAI,CAAE0B,UAAU,CAChBzB,IAAI,CAAEc,SAAS,CACfb,MAAM,CAAE4B,YAAY,CACpB3B,QAAQ,CAAE2B,YAAY,GAAK,QAAQ,CAAG,KAAK,CAAG,sBAAsB,EACpE,CAAC,CAEHnB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAE,CAAEY,YAAY,CAAEE,UAAU,CAAEX,SAAU,CAAC,CAAC,CAC9E,CAAE,MAAOkB,KAAK,CAAE,CACdtB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEqB,KAAK,CAAC,CAChD,KAAM,CAAAN,mBAAmB,CAAG7B,aAAa,CAAC8B,IAAI,CAACC,MAAM,EAAIA,MAAM,CAAC3B,MAAM,GAAK,WAAW,CAAC,CACvF,KAAM,CAAA4B,YAAY,CAAGH,mBAAmB,CAAG,QAAQ,CAAG,cAAc,CAEpEjB,aAAa,CAACqB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACbD,IAAI,MACP/B,IAAI,CAAE,mBAAmB,CACzBC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE4B,YAAY,CACpB3B,QAAQ,CAAE2B,YAAY,GAAK,QAAQ,CAAG,KAAK,CAAG,sBAAsB,EACpE,CAAC,CACL,CACF,CAAC,CAEDhB,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,CAAChB,aAAa,CAAC,CAAC,CAEnB;AACA,KAAM,CAAAe,WAAW,CAAIqB,IAAI,EAAK,CAC5BnD,iBAAiB,CAACmD,IAAI,CAAC,CACvBC,UAAU,CAAC,IAAMpD,iBAAiB,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CAC/C,CAAC,CAED;AACA,KAAM,CAAA0C,sBAAsB,CAAG,KAAAA,CAAA,GAAY,CACzC,GAAI,CACF;AACA,GAAIW,MAAM,CAACC,MAAM,EAAID,MAAM,CAACC,MAAM,CAACC,OAAO,CAAE,CAC1C,GAAI,CACF;AACA,KAAM,CAAAd,YAAY,CAAG,KAAM,CAAAY,MAAM,CAACC,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CAC3DC,MAAM,CAAE,iBACV,CAAC,CAAC,CACF,GAAIhB,YAAY,CAAE,MAAO,CAAAA,YAAY,CAACiB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC5D,CAAE,MAAOC,CAAC,CAAE,CACVhC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAE+B,CAAC,CAAC,CACnD,CACF,CAEA;AACA,GAAIP,MAAM,CAACQ,WAAW,EAAIR,MAAM,CAACS,SAAS,CAAE,CAC1C,GAAI,CACF,KAAM,CAAAC,GAAG,CAAGV,MAAM,CAACQ,WAAW,EAAIR,MAAM,CAACS,SAAS,CAClD,KAAM,CAAArB,YAAY,CAAG,KAAM,CAAAsB,GAAG,CAACC,eAAe,CAAC,CAAC,CAChD,GAAIvB,YAAY,CAAE,MAAO,CAAAA,YAAY,CAACiB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC5D,CAAE,MAAOC,CAAC,CAAE,CACVhC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAE+B,CAAC,CAAC,CAC7C,CACF,CAEA;AACA,GAAI,MAAO,CAAAK,OAAO,GAAK,WAAW,EAAIA,OAAO,CAACC,GAAG,CAAE,CACjD,GAAID,OAAO,CAACC,GAAG,CAACC,YAAY,CAAE,MAAO,CAAAF,OAAO,CAACC,GAAG,CAACC,YAAY,CAACR,WAAW,CAAC,CAAC,CAC3E,GAAIM,OAAO,CAACC,GAAG,CAACE,QAAQ,CAAE,MAAO,CAAAH,OAAO,CAACC,GAAG,CAACE,QAAQ,CAACT,WAAW,CAAC,CAAC,CACrE,CAEA;AACA,GAAI,CACF,KAAM,CAAAU,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,mBAAmB,CAAE,CAChDC,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CAAE,cAAc,CAAE,kBAAmB,CAChD,CAAC,CAAC,CACF,GAAIH,QAAQ,CAACI,EAAE,CAAE,CACf,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CACzC,GAAID,WAAW,CAACjC,YAAY,CAAE,MAAO,CAAAiC,WAAW,CAACjC,YAAY,CAACkB,WAAW,CAAC,CAAC,CAC3E,GAAIe,WAAW,CAAC/B,UAAU,CAAE,MAAO,CAAA+B,WAAW,CAAC/B,UAAU,CAACgB,WAAW,CAAC,CAAC,CACzE,CACF,CAAE,MAAOC,CAAC,CAAE,CACVhC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAE+B,CAAC,CAAC,CACnD,CAEA;AACA,GAAI,CACF,GAAIP,MAAM,CAACuB,WAAW,CAAE,CACtB;AACA;AACA,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAC,eAAe,CAAC,CAAC,CACzC,GAAID,SAAS,CAAE,MAAO,CAAAA,SAAS,CAAClB,WAAW,CAAC,CAAC,CAC/C,CACF,CAAE,MAAOC,CAAC,CAAE,CACVhC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE+B,CAAC,CAAC,CACtC,CAEA;AACA,GAAIP,MAAM,CAAC0B,QAAQ,CAACC,QAAQ,EACxB3B,MAAM,CAAC0B,QAAQ,CAACC,QAAQ,GAAK,WAAW,EACxC3B,MAAM,CAAC0B,QAAQ,CAACC,QAAQ,GAAK,WAAW,EACxC,CAAC3B,MAAM,CAAC0B,QAAQ,CAACC,QAAQ,CAAC1C,KAAK,CAAC,sBAAsB,CAAC,CAAE,CAC3D,MAAO,CAAAe,MAAM,CAAC0B,QAAQ,CAACC,QAAQ,CAACrB,WAAW,CAAC,CAAC,CAC/C,CAEA;AACA,GAAI,CAAAsB,UAAU,CAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAC7D,GAAI,CAACF,UAAU,CAAE,CACf;AACA,KAAM,CAAA7C,SAAS,CAAGH,SAAS,CAACG,SAAS,CACrC,KAAM,CAAAgD,MAAM,IAAA5C,MAAA,CAAMa,MAAM,CAAC+B,MAAM,CAACC,KAAK,MAAA7C,MAAA,CAAIa,MAAM,CAAC+B,MAAM,CAACE,MAAM,CAAE,CAC/D,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACC,QAAQ,CACjE,KAAM,CAAAC,QAAQ,CAAG3D,SAAS,CAAC2D,QAAQ,CACnC,KAAM,CAAAzD,QAAQ,CAAGF,SAAS,CAACE,QAAQ,CAEnC;AACA,KAAM,CAAA0D,SAAS,IAAArD,MAAA,CAAMJ,SAAS,MAAAI,MAAA,CAAI4C,MAAM,MAAA5C,MAAA,CAAI+C,QAAQ,MAAA/C,MAAA,CAAIoD,QAAQ,MAAApD,MAAA,CAAIL,QAAQ,CAAE,CAC9E,GAAI,CAAA2D,IAAI,CAAG,CAAC,CACZ,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGF,SAAS,CAACG,MAAM,CAAED,CAAC,EAAE,CAAE,CACzC,KAAM,CAAAE,IAAI,CAAGJ,SAAS,CAACK,UAAU,CAACH,CAAC,CAAC,CACpCD,IAAI,CAAI,CAACA,IAAI,EAAI,CAAC,EAAIA,IAAI,CAAIG,IAAI,CAClCH,IAAI,CAAGA,IAAI,CAAGA,IAAI,CAAE;AACtB,CAEA;AACA,KAAM,CAAAK,OAAO,CAAGC,IAAI,CAACC,GAAG,CAACP,IAAI,CAAC,CAACQ,QAAQ,CAAC,EAAE,CAAC,CAAC3C,WAAW,CAAC,CAAC,CAAC4C,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CACrEtB,UAAU,YAAAzC,MAAA,CAAc2D,OAAO,CAAE,CACjCjB,YAAY,CAACsB,OAAO,CAAC,sBAAsB,CAAEvB,UAAU,CAAC,CAExDrD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEoD,UAAU,CAAC,CACrD,CAEA,MAAO,CAAAA,UAAU,CACnB,CAAE,MAAO/B,KAAK,CAAE,CACdtB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAEqB,KAAK,CAAC,CACvD,MAAO,gBAAgB,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAA4B,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,MAAO,IAAI,CAAA2B,OAAO,CAAEC,OAAO,EAAK,CAC9B;AACA;AACA;AAEA;AACA,KAAM,CAAAC,qBAAqB,CAAG,CAC5B,gBAAgB,CAChB,eAAe,CACf,iBAAiB,CACjB,aAAa,CACb,eAAe,CACf,cAAc,CACf,CAED;AACA,KAAM,CAAAvE,SAAS,CAAGH,SAAS,CAACG,SAAS,CACrC,KAAM,CAAAgD,MAAM,IAAA5C,MAAA,CAAMa,MAAM,CAAC+B,MAAM,CAACC,KAAK,MAAA7C,MAAA,CAAIa,MAAM,CAAC+B,MAAM,CAACE,MAAM,CAAE,CAC/D,KAAM,CAAAsB,WAAW,IAAApE,MAAA,CAAMJ,SAAS,MAAAI,MAAA,CAAI4C,MAAM,CAAE,CAE5C,GAAI,CAAAU,IAAI,CAAG,CAAC,CACZ,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGa,WAAW,CAACZ,MAAM,CAAED,CAAC,EAAE,CAAE,CAC3C,KAAM,CAAAE,IAAI,CAAGW,WAAW,CAACV,UAAU,CAACH,CAAC,CAAC,CACtCD,IAAI,CAAI,CAACA,IAAI,EAAI,CAAC,EAAIA,IAAI,CAAIG,IAAI,CAClCH,IAAI,CAAGA,IAAI,CAAGA,IAAI,CACpB,CAEA,KAAM,CAAAe,KAAK,CAAGT,IAAI,CAACC,GAAG,CAACP,IAAI,CAAC,CAAGa,qBAAqB,CAACX,MAAM,CAC3D,KAAM,CAAAc,YAAY,CAAGH,qBAAqB,CAACE,KAAK,CAAC,CAEjDjF,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAEiF,YAAY,CAAC,CAEtF;AACA1D,UAAU,CAAC,IAAMsD,OAAO,CAACI,YAAY,CAAC,CAAE,GAAG,CAAC,CAC9C,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,eAAe,CAAG,KAAO,CAAAxH,OAAO,EAAK,CACzC,GAAI,CACF,KAAM,CAAA0C,SAAS,CAAC+E,SAAS,CAACC,SAAS,CAAC1H,OAAO,CAAC,CAC5Cd,sBAAsB,CAACc,OAAO,CAAC,CAC/BuC,WAAW,CAAC,6BAA6B,CAAC,CAC5C,CAAE,MAAOoB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpDpB,WAAW,CAAC,+BAA+B,CAAC,CAC9C,CACF,CAAC,CAED,KAAM,CAAAoF,UAAU,CAAIC,IAAI,EAAK,CAC3BJ,eAAe,CAACI,IAAI,CAAC5H,OAAO,CAAC,CAC7BZ,2BAA2B,CAACwI,IAAI,CAAC5H,OAAO,CAAC,CAC3C,CAAC,CAED,KAAM,CAAA6H,iBAAiB,CAAIC,MAAM,EAAK,CACpC,KAAM,CAAAC,cAAc,CAAGlI,YAAY,CAACmI,MAAM,CAACJ,IAAI,EAAIA,IAAI,CAAC7H,EAAE,GAAK+H,MAAM,CAAC,CACtEhI,eAAe,CAACiI,cAAc,CAAC,CAC/BxF,WAAW,CAAC,2BAA2B,CAAC,CAC1C,CAAC,CAED;AACA,KAAM,CAAA0F,sBAAsB,CAAGA,CAAA,GAAM,CACnCvI,wBAAwB,CAACT,mBAAmB,CAAC,CAC7CK,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CAAC,CAED,KAAM,CAAA4I,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,KAAM,CAAAC,UAAU,CAAG1I,qBAAqB,CAAC0E,IAAI,CAAC,CAAC,CAC/C,GAAI,CAACgE,UAAU,CAAE,CACf5F,WAAW,CAAC,yBAAyB,CAAC,CACtC,OACF,CAEArD,sBAAsB,CAACiJ,UAAU,CAAC,CAClC,GAAI,CACF,KAAM,CAAAzF,SAAS,CAAC+E,SAAS,CAACC,SAAS,CAACS,UAAU,CAAC,CACjD,CAAE,MAAOxE,KAAK,CAAE,CACdtB,OAAO,CAAC+F,IAAI,CAAC,6BAA6B,CAAEzE,KAAK,CAAC,CACpD,CAEArE,sBAAsB,CAAC,KAAK,CAAC,CAC7BI,wBAAwB,CAAC,EAAE,CAAC,CAC5B6C,WAAW,CAAC,kCAAkC,CAAC,CACjD,CAAC,CAED,KAAM,CAAA8F,oBAAoB,CAAGA,CAAA,GAAM,CACjC/I,sBAAsB,CAAC,KAAK,CAAC,CAC7BI,wBAAwB,CAAC,EAAE,CAAC,CAC9B,CAAC,CAED,KAAM,CAAA4I,2BAA2B,CAAGA,CAAA,GAAM,CACxC1I,6BAA6B,CAACT,wBAAwB,CAAC,CACvDK,2BAA2B,CAAC,IAAI,CAAC,CACnC,CAAC,CAED,KAAM,CAAA+I,uBAAuB,CAAGA,CAAA,GAAM,CACpC,KAAM,CAAAJ,UAAU,CAAGxI,0BAA0B,CAACwE,IAAI,CAAC,CAAC,CACpD,GAAI,CAACgE,UAAU,CAAE,CACf5F,WAAW,CAAC,yBAAyB,CAAC,CACtC,OACF,CAEAnD,2BAA2B,CAAC+I,UAAU,CAAC,CACvC3I,2BAA2B,CAAC,KAAK,CAAC,CAClCI,6BAA6B,CAAC,EAAE,CAAC,CACjC2C,WAAW,CAAC,uCAAuC,CAAC,CACtD,CAAC,CAED,KAAM,CAAAiG,yBAAyB,CAAGA,CAAA,GAAM,CACtChJ,2BAA2B,CAAC,KAAK,CAAC,CAClCI,6BAA6B,CAAC,EAAE,CAAC,CACnC,CAAC,CAED,KAAM,CAAA6I,OAAO,CAAGA,CAAA,GAAM,CACpBlG,WAAW,CAAC,mCAAmC,CAAC,CAChDsB,UAAU,CAAC,IAAM,CACftB,WAAW,CAAC,mBAAmB,CAAC,CAClC,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,KAAM,CAAAmG,iBAAiB,CAAGA,CAAA,GAAM,CAC9BnI,gBAAgB,CAAC,CAACD,aAAa,CAAC,CAChCiC,WAAW,CAACjC,aAAa,CAAG,0BAA0B,CAAG,sBAAsB,CAAC,CAClF,CAAC,CAED,KAAM,CAAAqI,cAAc,CAAGA,CAAA,GAAM,CAC3BpG,WAAW,CAAC,+BAA+B,CAAC,CAC9C,CAAC,CAED,KAAM,CAAAqG,qBAAqB,CAAGA,CAAA,GAAM,CAClCjI,2BAA2B,CAAC,CAACD,wBAAwB,CAAC,CACtD6B,WAAW,CAAC7B,wBAAwB,CAAG,2BAA2B,CAAG,0BAA0B,CAAC,CAClG,CAAC,CAED;AACA,KAAM,CAAAmI,YAAY,CAAIC,QAAQ,EAAK,CACjC,KAAM,CAAAC,cAAc,CAAGvH,aAAa,CAACwG,MAAM,CAACzE,MAAM,EAAIA,MAAM,CAACxD,EAAE,GAAK+I,QAAQ,CAAC,CAC7ErH,gBAAgB,CAACsH,cAAc,CAAC,CAChCxG,WAAW,CAAC,wCAAwC,CAAC,CACvD,CAAC,CAED,KAAM,CAAAyG,aAAa,CAAIF,QAAQ,EAAK,CAClC,KAAM,CAAAC,cAAc,CAAGvH,aAAa,CAACyH,GAAG,CAAC1F,MAAM,EAC7CA,MAAM,CAACxD,EAAE,GAAK+I,QAAQ,CAAApF,aAAA,CAAAA,aAAA,IAAQH,MAAM,MAAE3B,MAAM,CAAE,WAAW,CAAEC,QAAQ,CAAE,UAAU,GAAK0B,MACtF,CAAC,CACD9B,gBAAgB,CAACsH,cAAc,CAAC,CAChCxG,WAAW,CAAC,iCAAiC,CAAC,CAChD,CAAC,CAED,KAAM,CAAA2G,gBAAgB,CAAIJ,QAAQ,EAAK,CACrC,KAAM,CAAAC,cAAc,CAAGvH,aAAa,CAACyH,GAAG,CAAC1F,MAAM,EAC7CA,MAAM,CAACxD,EAAE,GAAK+I,QAAQ,CAAApF,aAAA,CAAAA,aAAA,IAAQH,MAAM,MAAE3B,MAAM,CAAE,cAAc,CAAEC,QAAQ,CAAE,UAAU,GAAK0B,MACzF,CAAC,CACD9B,gBAAgB,CAACsH,cAAc,CAAC,CAChCxG,WAAW,CAAC,wBAAwB,CAAC,CACvC,CAAC,CAED,KAAM,CAAA4G,UAAU,CAAIL,QAAQ,EAAK,CAC/B,KAAM,CAAAM,YAAY,CAAGrH,iBAAiB,CAACsH,IAAI,CAAC9F,MAAM,EAAIA,MAAM,CAACxD,EAAE,GAAK+I,QAAQ,CAAC,CAC7E,GAAIM,YAAY,CAAE,CAChB,KAAM,CAAAE,eAAe,CAAA5F,aAAA,CAAAA,aAAA,IAChB0F,YAAY,MACfxH,MAAM,CAAE,WAAW,CACnBC,QAAQ,CAAE,UAAU,EACrB,CACDJ,gBAAgB,CAAC,CAAC,GAAGD,aAAa,CAAE8H,eAAe,CAAC,CAAC,CACrD/G,WAAW,oCAAAU,MAAA,CAA+BmG,YAAY,CAAC1H,IAAI,CAAE,CAAC,CAChE,CACF,CAAC,CAED,KAAM,CAAA6H,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAIvI,aAAa,CAAE,OAEnBC,gBAAgB,CAAC,IAAI,CAAC,CACtBF,iBAAiB,CAAC,EAAE,CAAC,CAAE;AACvBiB,oBAAoB,CAAC,EAAE,CAAC,CAAE;AAC1BO,WAAW,CAAC,iDAAiD,CAAC,CAE9D,GAAI,CACF;AACA,KAAM,CAAAiH,YAAY,CAAG,EAAE,CAEvB;AACAjH,WAAW,CAAC,qCAAqC,CAAC,CAClD,KAAM,CAAAkH,cAAc,CAAG,KAAM,CAAAC,qBAAqB,CAAC,CAAC,CACpDF,YAAY,CAACG,IAAI,CAAC,GAAGF,cAAc,CAAC,CAEpC;AACA,GAAIA,cAAc,CAAChD,MAAM,CAAG,CAAC,CAAE,CAC7B1F,iBAAiB,CAAC0C,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAGgG,cAAc,CAAC,CAAC,CACvDzH,oBAAoB,CAACyB,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAGgG,cAAc,CAAC,CAAC,CAC1DlH,WAAW,uBAAAU,MAAA,CAAawG,cAAc,CAAChD,MAAM,sBAAoB,CAAC,CACpE,CAEA;AACAlE,WAAW,CAAC,oCAAoC,CAAC,CACjD,KAAM,CAAAqH,cAAc,CAAG,KAAM,CAAAC,qBAAqB,CAAC,CAAC,CACpDL,YAAY,CAACG,IAAI,CAAC,GAAGC,cAAc,CAAC,CAEpC;AACA,GAAIA,cAAc,CAACnD,MAAM,CAAG,CAAC,CAAE,CAC7B1F,iBAAiB,CAAC0C,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAGmG,cAAc,CAAC,CAAC,CACvD5H,oBAAoB,CAACyB,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAGmG,cAAc,CAAC,CAAC,CAC1DrH,WAAW,uBAAAU,MAAA,CAAa2G,cAAc,CAACnD,MAAM,sBAAoB,CAAC,CACpE,CAEA;AACAlE,WAAW,CAAC,2CAA2C,CAAC,CACxD,KAAM,CAAAuH,aAAa,CAAG,KAAM,CAAAC,oBAAoB,CAAC,CAAC,CAClDP,YAAY,CAACG,IAAI,CAAC,GAAGG,aAAa,CAAC,CAEnC;AACA,GAAIA,aAAa,CAACrD,MAAM,CAAG,CAAC,CAAE,CAC5B1F,iBAAiB,CAAC0C,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAGqG,aAAa,CAAC,CAAC,CACtD9H,oBAAoB,CAACyB,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE,GAAGqG,aAAa,CAAC,CAAC,CACzDvH,WAAW,uBAAAU,MAAA,CAAa6G,aAAa,CAACrD,MAAM,6BAA2B,CAAC,CAC1E,CAEA;AACAxF,gBAAgB,CAAC,KAAK,CAAC,CACvB,GAAIuI,YAAY,CAAC/C,MAAM,CAAG,CAAC,CAAE,CAC3BlE,WAAW,qCAAAU,MAAA,CAAgCuG,YAAY,CAAC/C,MAAM,uBAAqB,CAAC,CACtF,CAAC,IAAM,CACLlE,WAAW,CAAC,gEAAgE,CAAC,CAC/E,CAEF,CAAE,MAAOoB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CACxC1C,gBAAgB,CAAC,KAAK,CAAC,CACvBsB,WAAW,CAAC,sDAAsD,CAAC,CACrE,CACF,CAAC,CAED;AACA,KAAM,CAAAmH,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,MAAO,IAAI,CAAAxC,OAAO,CAAEC,OAAO,EAAK,CAC9BtD,UAAU,CAAC,IAAM,CACf,KAAM,CAAA4F,cAAc,CAAG,CACrB,CACE1J,EAAE,CAAE,oBAAoB,CACxB2B,IAAI,CAAE,gBAAgB,CACtBC,IAAI,CAAE,gBAAgB,CACtBC,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,uBAAuB,CACjCC,SAAS,CAAE,eAAe,CAC1BkI,UAAU,CAAE,SACd,CAAC,CACD,CACEjK,EAAE,CAAE,qBAAqB,CACzB2B,IAAI,CAAE,gBAAgB,CACtBC,IAAI,CAAE,YAAY,CAClBC,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,uBAAuB,CACjCC,SAAS,CAAE,eAAe,CAC1BkI,UAAU,CAAE,SACd,CAAC,CACD,CACEjK,EAAE,CAAE,qBAAqB,CACzB2B,IAAI,CAAE,cAAc,CACpBC,IAAI,CAAE,YAAY,CAClBC,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,uBAAuB,CACjCC,SAAS,CAAE,eAAe,CAC1BkI,UAAU,CAAE,SACd,CAAC,CACF,CACD7C,OAAO,CAACsC,cAAc,CAAC,CACzB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAI,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,MAAO,IAAI,CAAA3C,OAAO,CAAEC,OAAO,EAAK,CAC9BtD,UAAU,CAAC,IAAM,CACf,KAAM,CAAA+F,cAAc,CAAG,CACrB,CACE7J,EAAE,CAAE,qBAAqB,CACzB2B,IAAI,CAAE,oBAAoB,CAC1BC,IAAI,CAAE,YAAY,CAClBC,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,uBAAuB,CACjCC,SAAS,CAAE,eAAe,CAC1BkI,UAAU,CAAE,SACd,CAAC,CACD,CACEjK,EAAE,CAAE,qBAAqB,CACzB2B,IAAI,CAAE,YAAY,CAClBC,IAAI,CAAE,YAAY,CAClBC,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,uBAAuB,CACjCC,SAAS,CAAE,eAAe,CAC1BkI,UAAU,CAAE,SACd,CAAC,CACD,CACEjK,EAAE,CAAE,mBAAmB,CACvB2B,IAAI,CAAE,gBAAgB,CACtBC,IAAI,CAAE,YAAY,CAClBC,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,uBAAuB,CACjCC,SAAS,CAAE,eAAe,CAC1BkI,UAAU,CAAE,SACd,CAAC,CACF,CACD7C,OAAO,CAACyC,cAAc,CAAC,CACzB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAG,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,MAAO,IAAI,CAAA7C,OAAO,CAAEC,OAAO,EAAK,CAC9BtD,UAAU,CAAC,IAAM,CACf,KAAM,CAAAiG,aAAa,CAAG,CACpB,CACE/J,EAAE,CAAE,mBAAmB,CACvB2B,IAAI,CAAE,mBAAmB,CACzBC,IAAI,CAAE,WAAW,CACjBC,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,uBAAuB,CACjCC,SAAS,CAAE,eAAe,CAC1BkI,UAAU,CAAE,KACd,CAAC,CACD,CACEjK,EAAE,CAAE,gBAAgB,CACpB2B,IAAI,CAAE,gBAAgB,CACtBC,IAAI,CAAE,cAAc,CACpBC,MAAM,CAAE,aAAa,CACrBC,QAAQ,CAAE,uBAAuB,CACjCC,SAAS,CAAE,eAAe,CAC1BkI,UAAU,CAAE,OACd,CAAC,CACF,CACD7C,OAAO,CAAC2C,aAAa,CAAC,CACxB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAG,UAAU,CAAGA,CAAA,GAAM,CACvB1H,WAAW,CAAC,2EAA2E,CAAC,CAC1F,CAAC,CAED,KAAM,CAAA2H,cAAc,CAAGA,CAAA,GAAM,CAC3B;AACA,KAAM,CAAAC,cAAc,CAAG,CACrB/G,UAAU,CAAEjB,UAAU,CAACT,IAAI,CAC3BsI,UAAU,CAAE7H,UAAU,CAACR,IAAI,CAC3BG,SAAS,CAAEK,UAAU,CAACL,SAAS,CAC/BsI,IAAI,CAAE,IAAI,CACVC,QAAQ,CAAE,aAAa,CACvBpK,SAAS,CAAEqK,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAED,KAAM,CAAAC,MAAM,CAAGC,IAAI,CAACC,SAAS,CAACP,cAAc,CAAC,CAC7C,KAAM,CAAAQ,SAAS,kEAAA1H,MAAA,CAAoE2H,kBAAkB,CAACJ,MAAM,CAAC,CAAE,CAE/G;AACA,KAAM,CAAAK,OAAO,CAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC7CF,OAAO,CAACG,SAAS,CAAG,kBAAkB,CACtCH,OAAO,CAACI,SAAS,+RAAAhI,MAAA,CAOC0H,SAAS,yaAAA1H,MAAA,CAQWd,UAAU,CAACT,IAAI,+CAAAuB,MAAA,CACnBd,UAAU,CAACL,SAAS,8DAIrD,CAEDgJ,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,OAAO,CAAC,CAElC;AACA,KAAM,CAAAO,UAAU,CAAGA,CAAA,GAAM,CACvBN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,OAAO,CAAC,CACpC,CAAC,CAEDA,OAAO,CAACS,aAAa,CAAC,iBAAiB,CAAC,CAACC,OAAO,CAAGH,UAAU,CAC7DP,OAAO,CAACU,OAAO,CAAIlH,CAAC,EAAK,CACvB,GAAIA,CAAC,CAACmH,MAAM,GAAKX,OAAO,CAAEO,UAAU,CAAC,CAAC,CACxC,CAAC,CAED7I,WAAW,CAAC,gEAAgE,CAAC,CAC/E,CAAC,CAED,mBACExD,KAAA,QAAKiM,SAAS,CAAC,eAAe,CAAAS,QAAA,eAE5B1M,KAAA,QAAKiM,SAAS,CAAC,QAAQ,CAAAS,QAAA,eACrB1M,KAAA,QAAKiM,SAAS,CAAC,iBAAiB,CAAAS,QAAA,eAC9B1M,KAAA,QAAKiM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,eAC7B5M,IAAA,QACE6M,GAAG,CAAC,wBAAwB,CAC5BC,GAAG,CAAC,aAAa,CACjBX,SAAS,CAAC,UAAU,CACrB,CAAC,cACFnM,IAAA,QAAKmM,SAAS,mBAAA/H,MAAA,CAAoBzB,aAAa,CAAC8B,IAAI,CAACC,MAAM,EAAIA,MAAM,CAAC3B,MAAM,GAAK,WAAW,CAAC,CAAG,WAAW,CAAG,cAAc,CAAG,CAAM,CAAC,EACnI,CAAC,cACN/C,IAAA,OAAImM,SAAS,CAAC,OAAO,CAAAS,QAAA,CAAC,QAAM,CAAI,CAAC,EAC9B,CAAC,cACN1M,KAAA,QAAKiM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,eAC7B5M,IAAA,WACEmM,SAAS,gBAAA/H,MAAA,CAAiBvC,wBAAwB,CAAG,QAAQ,CAAG,EAAE,CAAG,CACrEkL,OAAO,CAAEhD,qBAAsB,CAC/BiD,KAAK,CAAC,kCAAkC,CAAAJ,QAAA,CACzC,cAED,CAAQ,CAAC,cACT5M,IAAA,WACEmM,SAAS,gBAAA/H,MAAA,CAAiB3C,aAAa,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC1DsL,OAAO,CAAElD,iBAAkB,CAC3BmD,KAAK,CAAC,YAAY,CAAAJ,QAAA,cAElB1M,KAAA,QAAKiM,SAAS,CAAC,UAAU,CAAAS,QAAA,eACvB5M,IAAA,QAAKmM,SAAS,CAAC,UAAU,CAAM,CAAC,cAChCnM,IAAA,QAAKmM,SAAS,CAAC,UAAU,CAAM,CAAC,EAC7B,CAAC,CACA,CAAC,cACTnM,IAAA,WAAQmM,SAAS,CAAC,aAAa,CAACY,OAAO,CAAEjD,cAAe,CAACkD,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,QAE1E,CAAQ,CAAC,cACT5M,IAAA,WAAQmM,SAAS,CAAC,aAAa,CAACY,OAAO,CAAEA,CAAA,GAAMzL,eAAe,CAAC,IAAI,CAAE,CAAC0L,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,cAEvF,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAGLjL,cAAc,eACb3B,IAAA,QAAKmM,SAAS,CAAC,iBAAiB,CAAAS,QAAA,cAC9B5M,IAAA,SAAA4M,QAAA,CAAOjL,cAAc,CAAO,CAAC,CAC1B,CACN,cAGDzB,KAAA,QAAKiM,SAAS,CAAC,cAAc,CAAAS,QAAA,eAE3B1M,KAAA,QAAKiM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,eAC7B5M,IAAA,QAAKmM,SAAS,CAAC,uBAAuB,CAAAS,QAAA,cACpC1M,KAAA,QAAKiM,SAAS,CAAC,gCAAgC,CAAAS,QAAA,eAC7C5M,IAAA,OAAImM,SAAS,CAAC,sBAAsB,CAAAS,QAAA,CAAC,0BAAc,CAAI,CAAC,cACxD5M,IAAA,MAAGmM,SAAS,CAAC,yBAAyB,CAAAS,QAAA,CAAEtJ,UAAU,CAACT,IAAI,CAAI,CAAC,EACzD,CAAC,CACH,CAAC,CAELrC,mBAAmB,cAClBN,KAAA,QAAKiM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,eAC7B5M,IAAA,aACEmM,SAAS,CAAC,iBAAiB,CAC3Bc,KAAK,CAAErM,qBAAsB,CAC7BsM,QAAQ,CAAG1H,CAAC,EAAK3E,wBAAwB,CAAC2E,CAAC,CAACmH,MAAM,CAACM,KAAK,CAAE,CAC1DE,WAAW,CAAC,uCAAuC,CACnDC,SAAS,MACV,CAAC,cACFlN,KAAA,QAAKiM,SAAS,CAAC,cAAc,CAAAS,QAAA,eAC3B5M,IAAA,WAAQmM,SAAS,CAAC,yBAAyB,CAACY,OAAO,CAAE1D,kBAAmB,CAAAuD,QAAA,CAAC,MAEzE,CAAQ,CAAC,cACT5M,IAAA,WAAQmM,SAAS,CAAC,2BAA2B,CAACY,OAAO,CAAEvD,oBAAqB,CAAAoD,QAAA,CAAC,QAE7E,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN5M,IAAA,QAAKmM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,cAC/B1M,KAAA,QACEiM,SAAS,CAAC,uCAAuC,CACjDY,OAAO,CAAEA,CAAA,GAAMpE,eAAe,CAACvI,mBAAmB,CAAE,CAAAwM,QAAA,eAEpD5M,IAAA,MAAGmM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,CAAExM,mBAAmB,CAAI,CAAC,cACvDJ,IAAA,MAAGmM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,CAAC,qCAA8B,CAAG,CAAC,cAChE5M,IAAA,WAAQmM,SAAS,CAAC,oBAAoB,CAACY,OAAO,CAAE3D,sBAAuB,CAAAwD,QAAA,CAAC,QAExE,CAAQ,CAAC,EACN,CAAC,CACH,CACN,EACE,CAAC,cAGN1M,KAAA,QAAKiM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,eAC7B1M,KAAA,QAAKiM,SAAS,CAAC,uBAAuB,CAAAS,QAAA,eACpC5M,IAAA,OAAImM,SAAS,CAAC,sBAAsB,CAAAS,QAAA,CAAC,+BAAmB,CAAI,CAAC,cAC7D5M,IAAA,MAAGmM,SAAS,CAAC,yBAAyB,CAAAS,QAAA,CAAC,uCAA2B,CAAG,CAAC,EACnE,CAAC,CAELlM,wBAAwB,cACvBR,KAAA,QAAKiM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,eAC7B5M,IAAA,aACEmM,SAAS,CAAC,iBAAiB,CAC3Bc,KAAK,CAAEnM,0BAA2B,CAClCoM,QAAQ,CAAG1H,CAAC,EAAKzE,6BAA6B,CAACyE,CAAC,CAACmH,MAAM,CAACM,KAAK,CAAE,CAC/DE,WAAW,CAAC,4CAA4C,CACxDC,SAAS,MACV,CAAC,cACFlN,KAAA,QAAKiM,SAAS,CAAC,cAAc,CAAAS,QAAA,eAC3B5M,IAAA,WAAQmM,SAAS,CAAC,8BAA8B,CAACY,OAAO,CAAErD,uBAAwB,CAAAkD,QAAA,CAAC,MAEnF,CAAQ,CAAC,cACT5M,IAAA,WAAQmM,SAAS,CAAC,gCAAgC,CAACY,OAAO,CAAEpD,yBAA0B,CAAAiD,QAAA,CAAC,QAEvF,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN5M,IAAA,QAAKmM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,cAC/B1M,KAAA,QACEiM,SAAS,CAAC,4CAA4C,CACtDY,OAAO,CAAEA,CAAA,GAAMpE,eAAe,CAACrI,wBAAwB,CAAE,CAAAsM,QAAA,eAEzD5M,IAAA,MAAGmM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,CAAEtM,wBAAwB,CAAI,CAAC,cAC5DN,IAAA,MAAGmM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,CAAC,yCAAkC,CAAG,CAAC,cACpE5M,IAAA,WAAQmM,SAAS,CAAC,oBAAoB,CAACY,OAAO,CAAEtD,2BAA4B,CAAAmD,QAAA,CAAC,QAE7E,CAAQ,CAAC,EACN,CAAC,CACH,CACN,EACE,CAAC,cAGN5M,IAAA,OAAImM,SAAS,CAAC,eAAe,CAAAS,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACpD5M,IAAA,QAAKmM,SAAS,CAAC,cAAc,CAAAS,QAAA,CAC1B5L,YAAY,CAACoJ,GAAG,CAAErB,IAAI,eACrB7I,KAAA,QAEEiM,SAAS,CAAC,cAAc,CACxBY,OAAO,CAAEA,CAAA,GAAMjE,UAAU,CAACC,IAAI,CAAE,CAAA6D,QAAA,eAEhC1M,KAAA,QAAKiM,SAAS,CAAC,qBAAqB,CAAAS,QAAA,eAClC5M,IAAA,SAAMmM,SAAS,CAAC,WAAW,CAAAS,QAAA,CAAE7D,IAAI,CAAC3H,SAAS,CAAO,CAAC,cACnDpB,IAAA,WACEmM,SAAS,CAAC,eAAe,CACzBY,OAAO,CAAGvH,CAAC,EAAK,CACdA,CAAC,CAAC6H,eAAe,CAAC,CAAC,CACnBrE,iBAAiB,CAACD,IAAI,CAAC7H,EAAE,CAAC,CAC5B,CAAE,CAAA0L,QAAA,CACH,QAED,CAAQ,CAAC,EACN,CAAC,cACN5M,IAAA,MAAGmM,SAAS,CAAC,cAAc,CAAAS,QAAA,CAAE7D,IAAI,CAAC5H,OAAO,CAAI,CAAC,GAhBzC4H,IAAI,CAAC7H,EAiBP,CACN,CAAC,CACC,CAAC,EACH,CAAC,cAGNlB,IAAA,WAAQmM,SAAS,CAAC,sBAAsB,CAACY,OAAO,CAAEnD,OAAQ,CAAAgD,QAAA,cACxD5M,IAAA,QAAK6M,GAAG,CAAC,WAAW,CAACC,GAAG,CAAC,MAAM,CAACX,SAAS,CAAC,WAAW,CAAE,CAAC,CAClD,CAAC,CAGR9K,YAAY,eACXrB,IAAA,QAAKmM,SAAS,CAAC,eAAe,CAACY,OAAO,CAAEA,CAAA,GAAMzL,eAAe,CAAC,KAAK,CAAE,CAAAsL,QAAA,cACnE1M,KAAA,QAAKiM,SAAS,CAAC,kBAAkB,CAACY,OAAO,CAAGvH,CAAC,EAAKA,CAAC,CAAC6H,eAAe,CAAC,CAAE,CAAAT,QAAA,eACpE1M,KAAA,QAAKiM,SAAS,CAAC,iBAAiB,CAAAS,QAAA,eAC9B5M,IAAA,OAAImM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC5C5M,IAAA,WAAQmM,SAAS,CAAC,cAAc,CAACY,OAAO,CAAEA,CAAA,GAAMzL,eAAe,CAAC,KAAK,CAAE,CAAAsL,QAAA,CAAC,QAExE,CAAQ,CAAC,EACN,CAAC,cAEN1M,KAAA,QAAKiM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,eAE/B1M,KAAA,QAAKiM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,eAC/B5M,IAAA,OAAImM,SAAS,CAAC,eAAe,CAAAS,QAAA,CAAC,aAAW,CAAI,CAAC,cAC9C1M,KAAA,QAAKiM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,eAC/B5M,IAAA,MAAGmM,SAAS,CAAC,aAAa,CAAAS,QAAA,CAAEtJ,UAAU,CAACT,IAAI,CAAI,CAAC,cAChD7C,IAAA,MAAGmM,SAAS,CAAC,eAAe,CAAAS,QAAA,CAAEtJ,UAAU,CAACR,IAAI,CAAI,CAAC,cAClD5C,KAAA,MAAGiM,SAAS,CAAC,eAAe,CAAAS,QAAA,EAAC,MAAI,CAACtJ,UAAU,CAACL,SAAS,EAAI,CAAC,cAC3D/C,KAAA,QAAKiM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,eAChC5M,IAAA,QAAKmM,SAAS,4BAAA/H,MAAA,CAA6Bd,UAAU,CAACP,MAAM,CAAG,CAAM,CAAC,cACtE7C,KAAA,SAAMiM,SAAS,uBAAA/H,MAAA,CAAwBd,UAAU,CAACP,MAAM,CAAG,CAAA6J,QAAA,EAAC,UAClD,CAACtJ,UAAU,CAACP,MAAM,GAAK,QAAQ,CAAG,WAAW,CAAG,cAAc,EAClE,CAAC,EACJ,CAAC,cACN7C,KAAA,QAAKiM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,eAChC5M,IAAA,QAAKmM,SAAS,4BAAA/H,MAAA,CAA6BhB,oBAAoB,CAAG,QAAQ,CAAG,cAAc,CAAG,CAAM,CAAC,cACrGlD,KAAA,SAAMiM,SAAS,uBAAA/H,MAAA,CAAwBhB,oBAAoB,CAAG,QAAQ,CAAG,cAAc,CAAG,CAAAwJ,QAAA,EAAC,gBAC3E,CAACxJ,oBAAoB,CAAG,0BAA0B,CAAG,uBAAuB,EACtF,CAAC,EACJ,CAAC,CACLE,UAAU,CAACP,MAAM,GAAK,cAAc,eACnC/C,IAAA,MAAGmM,SAAS,CAAC,mCAAmC,CAAAS,QAAA,CAAC,gGAEjD,CAAG,CACJ,EACE,CAAC,EACH,CAAC,cAGN1M,KAAA,QAAKiM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,eAC/B5M,IAAA,OAAImM,SAAS,CAAC,eAAe,CAAAS,QAAA,CAAC,gBAAc,CAAI,CAAC,CAChDjK,aAAa,CAACiF,MAAM,GAAK,CAAC,cACzB1H,KAAA,QAAKiM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,eAChC5M,IAAA,MAAGmM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,CAAC,yBAAuB,CAAG,CAAC,cAC5D5M,IAAA,MAAGmM,SAAS,CAAC,sBAAsB,CAAAS,QAAA,CAAC,iDAA+C,CAAG,CAAC,EACpF,CAAC,CAENjK,aAAa,CAACyH,GAAG,CAAE1F,MAAM,eACvBxE,KAAA,QAAqBiM,SAAS,CAAC,sBAAsB,CAAAS,QAAA,eAEnD5M,IAAA,WACEmM,SAAS,CAAC,yBAAyB,CACnCY,OAAO,CAAEA,CAAA,GAAM/C,YAAY,CAACtF,MAAM,CAACxD,EAAE,CAAE,CAAA0L,QAAA,CACxC,MAED,CAAQ,CAAC,cAET1M,KAAA,QAAKiM,SAAS,CAAC,aAAa,CAAAS,QAAA,eAC1B5M,IAAA,MAAGmM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,CAAElI,MAAM,CAAC7B,IAAI,CAAI,CAAC,cACjD7C,IAAA,MAAGmM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,CAAElI,MAAM,CAAC5B,IAAI,CAAI,CAAC,cACjD5C,KAAA,QAAKiM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,eAChC5M,IAAA,QAAKmM,SAAS,4BAAA/H,MAAA,CAA6BM,MAAM,CAAC3B,MAAM,CAAG,CAAM,CAAC,cAClE7C,KAAA,SAAMiM,SAAS,uBAAA/H,MAAA,CAAwBM,MAAM,CAAC3B,MAAM,CAAG,CAAA6J,QAAA,EACpDlI,MAAM,CAAC3B,MAAM,GAAK,WAAW,CAAG,WAAW,CAAG,cAAc,CAAC,UAAG,CAAC2B,MAAM,CAAC1B,QAAQ,EAC7E,CAAC,EACJ,CAAC,EACH,CAAC,cAGNhD,IAAA,WACEmM,SAAS,sCAAA/H,MAAA,CAAuCM,MAAM,CAAC3B,MAAM,GAAK,WAAW,CAAG,mBAAmB,CAAG,gBAAgB,CAAG,CACzHgK,OAAO,CAAEA,CAAA,GAAMrI,MAAM,CAAC3B,MAAM,GAAK,WAAW,CAAGsH,gBAAgB,CAAC3F,MAAM,CAACxD,EAAE,CAAC,CAAGiJ,aAAa,CAACzF,MAAM,CAACxD,EAAE,CAAE,CAAA0L,QAAA,CAErGlI,MAAM,CAAC3B,MAAM,GAAK,WAAW,CAAG,YAAY,CAAG,SAAS,CACnD,CAAC,GA1BD2B,MAAM,CAACxD,EA2BZ,CACN,CACF,EACE,CAAC,cAGNhB,KAAA,QAAKiM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,eAC/B1M,KAAA,QAAKiM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,eAC7B5M,IAAA,OAAImM,SAAS,CAAC,eAAe,CAAAS,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACnD5M,IAAA,WACEmM,SAAS,oBAAA/H,MAAA,CAAqBjC,aAAa,CAAG,0BAA0B,CAAG,EAAE,CAAG,CAChF4K,OAAO,CAAErC,gBAAiB,CAC1B4C,QAAQ,CAAEnL,aAAc,CAAAyK,QAAA,CAEvBzK,aAAa,CAAG,gBAAgB,CAAG,UAAU,CACxC,CAAC,EACN,CAAC,CACLF,cAAc,CAAC2F,MAAM,GAAK,CAAC,cAC1B1H,KAAA,QAAKiM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,eAChC5M,IAAA,MAAGmM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,CAAC,kBAAgB,CAAG,CAAC,cACrD5M,IAAA,MAAGmM,SAAS,CAAC,sBAAsB,CAAAS,QAAA,CAChCzK,aAAa,CAAG,yBAAyB,CAAG,oCAAoC,CAChF,CAAC,EACD,CAAC,CAENF,cAAc,CAACmI,GAAG,CAAE1F,MAAM,eACxBxE,KAAA,QAAqBiM,SAAS,CAAC,wBAAwB,CAAAS,QAAA,eACrD1M,KAAA,QAAKiM,SAAS,CAAC,aAAa,CAAAS,QAAA,eAC1B1M,KAAA,QAAKiM,SAAS,CAAC,uBAAuB,CAAAS,QAAA,eACpC5M,IAAA,SAAMmM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,CAC/BlI,MAAM,CAACyG,UAAU,GAAK,SAAS,CAAG,IAAI,CACtCzG,MAAM,CAACyG,UAAU,GAAK,SAAS,CAAG,KAAK,CACvCzG,MAAM,CAACyG,UAAU,GAAK,KAAK,CAAG,IAAI,CAClCzG,MAAM,CAACyG,UAAU,GAAK,OAAO,CAAG,IAAI,CAAG,KAAK,CACzC,CAAC,cACPnL,IAAA,MAAGmM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,CAAElI,MAAM,CAAC7B,IAAI,CAAI,CAAC,EAC9C,CAAC,cACN7C,IAAA,MAAGmM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,CAAElI,MAAM,CAAC5B,IAAI,CAAI,CAAC,cACjD9C,IAAA,MAAGmM,SAAS,CAAC,uBAAuB,CAAAS,QAAA,CAAElI,MAAM,CAAC1B,QAAQ,CAAI,CAAC,cAC1D9C,KAAA,MAAGiM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,EAAC,MAAI,CAAClI,MAAM,CAACzB,SAAS,EAAI,CAAC,EACrD,CAAC,cACNjD,IAAA,WACEmM,SAAS,CAAC,aAAa,CACvBY,OAAO,CAAEA,CAAA,GAAMzC,UAAU,CAAC5F,MAAM,CAACxD,EAAE,CAAE,CAAA0L,QAAA,CAEpClI,MAAM,CAACyG,UAAU,GAAK,SAAS,CAAG,iBAAiB,CACnDzG,MAAM,CAACyG,UAAU,GAAK,SAAS,CAAG,kBAAkB,CACpD,gBAAgB,CACX,CAAC,GAtBDzG,MAAM,CAACxD,EAuBZ,CACN,CACF,cAEDlB,IAAA,QAAKmM,SAAS,CAAC,sBAAsB,CAAAS,QAAA,cACnC5M,IAAA,WAAQmM,SAAS,CAAC,oBAAoB,CAACY,OAAO,CAAE1B,cAAe,CAAAuB,QAAA,CAAC,aAEhE,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cAGN1M,KAAA,QAAKiM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,eAC/B5M,IAAA,OAAImM,SAAS,CAAC,eAAe,CAAAS,QAAA,CAAC,qBAAmB,CAAI,CAAC,cACtD1M,KAAA,WACEiM,SAAS,CAAC,cAAc,CACxBY,OAAO,CAAEA,CAAA,GAAMvL,mBAAmB,CAAC,IAAI,CAAE,CAAAoL,QAAA,eAEzC5M,IAAA,SAAMmM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,CAAC,eAAa,CAAM,CAAC,cACxD5M,IAAA,SAAMmM,SAAS,CAAC,oBAAoB,CAAAS,QAAA,CAAC,QAAC,CAAM,CAAC,EACvC,CAAC,cAGT1M,KAAA,WACEiM,SAAS,CAAC,cAAc,CACxBY,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAQ,QAAQ,CAAG,CAACxL,qBAAqB,CACvCC,wBAAwB,CAACuL,QAAQ,CAAC,CAClC7J,WAAW,CAAC6J,QAAQ,CAAG,yBAAyB,CAAG,0BAA0B,CAAC,CAChF,CAAE,CAAAX,QAAA,eAEF5M,IAAA,SAAMmM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,CAAC,iBAAe,CAAM,CAAC,cAC1D5M,IAAA,SAAMmM,SAAS,CAAC,oBAAoB,CAAAS,QAAA,CACjC7K,qBAAqB,CAAG,IAAI,CAAG,KAAK,CACjC,CAAC,EACD,CAAC,cAGT7B,KAAA,WACEiM,SAAS,CAAC,cAAc,CACxBY,OAAO,CAAEA,CAAA,GAAM,CACb1J,uBAAuB,CAAC,CAACD,oBAAoB,CAAC,CAC9CM,WAAW,CACT,CAACN,oBAAoB,CACjB,oEAAoE,CACpE,wCACN,CAAC,CACH,CAAE,CAAAwJ,QAAA,eAEF5M,IAAA,SAAMmM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,CAAC,wBAAsB,CAAM,CAAC,cACjE5M,IAAA,SAAMmM,SAAS,CAAC,oBAAoB,CAAAS,QAAA,CACjCxJ,oBAAoB,CAAG,IAAI,CAAG,KAAK,CAChC,CAAC,EACD,CAAC,cAGTlD,KAAA,WACEiM,SAAS,CAAC,cAAc,CACxBY,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI5K,aAAa,CAAE,CACjBC,gBAAgB,CAAC,KAAK,CAAC,CACvBsB,WAAW,CAAC,8BAA8B,CAAC,CAC7C,CAAC,IAAM,CACLtB,gBAAgB,CAAC,IAAI,CAAC,CACtBsB,WAAW,CAAC,8BAA8B,CAAC,CAC7C,CACF,CAAE,CAAAkJ,QAAA,eAEF5M,IAAA,SAAMmM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,CAAC,mBAAiB,CAAM,CAAC,cAC5D5M,IAAA,SAAMmM,SAAS,CAAC,oBAAoB,CAAAS,QAAA,CACjCzK,aAAa,CAAG,IAAI,CAAG,KAAK,CACzB,CAAC,EACD,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,CAGAZ,gBAAgB,eACfvB,IAAA,QAAKmM,SAAS,CAAC,eAAe,CAACY,OAAO,CAAEA,CAAA,GAAMvL,mBAAmB,CAAC,KAAK,CAAE,CAAAoL,QAAA,cACvE1M,KAAA,QAAKiM,SAAS,CAAC,qBAAqB,CAACY,OAAO,CAAGvH,CAAC,EAAKA,CAAC,CAAC6H,eAAe,CAAC,CAAE,CAAAT,QAAA,eACvE1M,KAAA,QAAKiM,SAAS,CAAC,iBAAiB,CAAAS,QAAA,eAC9B5M,IAAA,OAAImM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,CAAC,eAAa,CAAI,CAAC,cACjD5M,IAAA,WAAQmM,SAAS,CAAC,cAAc,CAACY,OAAO,CAAEA,CAAA,GAAMvL,mBAAmB,CAAC,KAAK,CAAE,CAAAoL,QAAA,CAAC,QAE5E,CAAQ,CAAC,EACN,CAAC,cAEN5M,IAAA,QAAKmM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,cAC/B1M,KAAA,QAAKiM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,eAC/B1M,KAAA,QAAKiM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,eAChC5M,IAAA,UAAOmM,SAAS,CAAC,oBAAoB,CAAAS,QAAA,CAAC,WAAS,CAAO,CAAC,cACvD5M,IAAA,UACE8C,IAAI,CAAC,UAAU,CACfqJ,SAAS,CAAC,uBAAuB,CACjCqB,OAAO,CAAEnL,YAAY,CAACE,QAAS,CAC/B2K,QAAQ,CAAG1H,CAAC,EAAKlD,eAAe,CAAAuC,aAAA,CAAAA,aAAA,IAAKxC,YAAY,MAAEE,QAAQ,CAAEiD,CAAC,CAACmH,MAAM,CAACa,OAAO,EAAC,CAAE,CACjF,CAAC,EACC,CAAC,cAENtN,KAAA,QAAKiM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,eAChC1M,KAAA,UAAOiM,SAAS,CAAC,oBAAoB,CAAAS,QAAA,EAAC,cAAY,CAACvK,YAAY,CAACG,SAAS,CAAC,UAAQ,EAAO,CAAC,cAC1FxC,IAAA,MAAGmM,SAAS,CAAC,0BAA0B,CAAAS,QAAA,CACpCvK,YAAY,CAACG,SAAS,GAAK,CAAC,CAAG,cAAc,IAAA4B,MAAA,CAAM/B,YAAY,CAACG,SAAS,iBAAe,CACxF,CAAC,cACJtC,KAAA,QAAKiM,SAAS,CAAC,qBAAqB,CAAAS,QAAA,eAClC5M,IAAA,WACEmM,SAAS,CAAC,mBAAmB,CAC7BY,OAAO,CAAEA,CAAA,GAAMzK,eAAe,CAAAuC,aAAA,CAAAA,aAAA,IAAKxC,YAAY,MAAEG,SAAS,CAAEwF,IAAI,CAACyF,GAAG,CAAC,CAAC,CAAEpL,YAAY,CAACG,SAAS,CAAG,CAAC,CAAC,EAAC,CAAE,CAAAoK,QAAA,CACvG,GAED,CAAQ,CAAC,cACT1M,KAAA,SAAMiM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,EAAEvK,YAAY,CAACG,SAAS,CAAC,GAAC,EAAM,CAAC,cACnExC,IAAA,WACEmM,SAAS,CAAC,mBAAmB,CAC7BY,OAAO,CAAEA,CAAA,GAAMzK,eAAe,CAAAuC,aAAA,CAAAA,aAAA,IAAKxC,YAAY,MAAEG,SAAS,CAAEwF,IAAI,CAAC0F,GAAG,CAAC,EAAE,CAAErL,YAAY,CAACG,SAAS,CAAG,CAAC,CAAC,EAAC,CAAE,CAAAoK,QAAA,CACxG,GAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN1M,KAAA,QAAKiM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,eAChC5M,IAAA,UAAOmM,SAAS,CAAC,oBAAoB,CAAAS,QAAA,CAAC,iBAAe,CAAO,CAAC,cAC7D5M,IAAA,UACE8C,IAAI,CAAC,UAAU,CACfqJ,SAAS,CAAC,uBAAuB,CACjCqB,OAAO,CAAEnL,YAAY,CAACI,aAAc,CACpCyK,QAAQ,CAAG1H,CAAC,EAAKlD,eAAe,CAAAuC,aAAA,CAAAA,aAAA,IAAKxC,YAAY,MAAEI,aAAa,CAAE+C,CAAC,CAACmH,MAAM,CAACa,OAAO,EAAC,CAAE,CACtF,CAAC,EACC,CAAC,cAENtN,KAAA,QAAKiM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,eAChC5M,IAAA,UAAOmM,SAAS,CAAC,oBAAoB,CAAAS,QAAA,CAAC,oBAAkB,CAAO,CAAC,cAChE5M,IAAA,UACE8C,IAAI,CAAC,UAAU,CACfqJ,SAAS,CAAC,uBAAuB,CACjCqB,OAAO,CAAEnL,YAAY,CAACK,aAAc,CACpCwK,QAAQ,CAAG1H,CAAC,EAAKlD,eAAe,CAAAuC,aAAA,CAAAA,aAAA,IAAKxC,YAAY,MAAEK,aAAa,CAAE8C,CAAC,CAACmH,MAAM,CAACa,OAAO,EAAC,CAAE,CACtF,CAAC,EACC,CAAC,cAGNxN,IAAA,QAAKmM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,cAChC5M,IAAA,WACEmM,SAAS,CAAC,4BAA4B,CACtCY,OAAO,CAAEA,CAAA,GAAMrJ,WAAW,CAAC,6FAA6F,CAAE,CAAAkJ,QAAA,CAC3H,0CAED,CAAQ,CAAC,CACN,CAAC,cAEN5M,IAAA,QAAKmM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,cAChC5M,IAAA,WACEmM,SAAS,CAAC,4BAA4B,CACtCY,OAAO,CAAEA,CAAA,GAAMrJ,WAAW,CAAC,8EAA8E,CAAE,CAAAkJ,QAAA,CAC5G,gDAED,CAAQ,CAAC,CACN,CAAC,cAEN5M,IAAA,QAAKmM,SAAS,CAAC,mBAAmB,CAAAS,QAAA,cAChC5M,IAAA,WACEmM,SAAS,CAAC,4BAA4B,CACtCY,OAAO,CAAEA,CAAA,GAAMrJ,WAAW,CAAC,8EAA8E,CAAE,CAAAkJ,QAAA,CAC5G,+BAED,CAAQ,CAAC,CACN,CAAC,cAGN1M,KAAA,QAAKiM,SAAS,CAAC,kBAAkB,CAAAS,QAAA,eAC/B5M,IAAA,OAAImM,SAAS,CAAC,wBAAwB,CAAAS,QAAA,CAAC,+CAAmC,CAAI,CAAC,cAC/E5M,IAAA,MAAGmM,SAAS,CAAC,sBAAsB,CAAAS,QAAA,CAAC,uFAEpC,CAAG,CAAC,cAEJ1M,KAAA,QAAKiM,SAAS,CAAC,cAAc,CAAAS,QAAA,eAC3B5M,IAAA,SAAMmM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,CAAC,gCAA8B,CAAM,CAAC,cACtE5M,IAAA,WACEmM,SAAS,CAAC,wBAAwB,CAClCY,OAAO,CAAEA,CAAA,GAAMrJ,WAAW,CAAC,gEAAgE,CAAE,CAAAkJ,QAAA,cAE7F5M,IAAA,QAAKmM,SAAS,CAAC,8BAA8B,CAAM,CAAC,CAC9C,CAAC,EACN,CAAC,cAENjM,KAAA,QAAKiM,SAAS,CAAC,cAAc,CAAAS,QAAA,eAC3B5M,IAAA,SAAMmM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,CAAC,yBAAuB,CAAM,CAAC,cAC/D5M,IAAA,WACEmM,SAAS,CAAC,wBAAwB,CAClCY,OAAO,CAAEA,CAAA,GAAMrJ,WAAW,CAAC,+BAA+B,CAAE,CAAAkJ,QAAA,cAE5D5M,IAAA,QAAKmM,SAAS,CAAC,8BAA8B,CAAM,CAAC,CAC9C,CAAC,EACN,CAAC,cAENjM,KAAA,QAAKiM,SAAS,CAAC,cAAc,CAAAS,QAAA,eAC3B5M,IAAA,SAAMmM,SAAS,CAAC,gBAAgB,CAAAS,QAAA,CAAC,sBAAoB,CAAM,CAAC,cAC5D5M,IAAA,WACEmM,SAAS,CAAC,wBAAwB,CAClCY,OAAO,CAAEA,CAAA,GAAMrJ,WAAW,CAAC,iCAAiC,CAAE,CAAAkJ,QAAA,cAE9D5M,IAAA,QAAKmM,SAAS,CAAC,8BAA8B,CAAM,CAAC,CAC9C,CAAC,EACN,CAAC,cAENnM,IAAA,MAAGmM,SAAS,CAAC,eAAe,CAAAS,QAAA,CAAC,iOAE7B,CAAG,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAEA,cAAe,CAAAzM,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}