directories:
  output: dist
  buildResources: assets
files:
  - filter:
      - build/**/*
      - build/**/*
      - '!build/electron.js'
      - electron.js
      - preload.js
extraMetadata:
  main: electron.js
appId: com.clipsy.windows
productName: Clipsy
win:
  target: nsis
  icon: public/clipsy-logo-no-bg.png
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Clipsy
portable:
  artifactName: ${productName}-${version}-portable-${arch}.${ext}
extends: react-cra
electronVersion: 37.1.0
