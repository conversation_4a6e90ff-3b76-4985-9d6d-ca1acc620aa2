{"version": 3, "file": "static/css/main.292ddfd2.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCXA,eACE,wBAAyB,CACzB,UAAc,CAEd,mIAA8J,CAD9J,gBAAiB,CAEjB,iBACF,CAGA,QAKE,4BAA6B,CAF7B,6BAA8B,CAC9B,YAEF,CAEA,yBANE,kBAAmB,CADnB,YAUF,CAEA,gBAEE,gBAAiB,CADjB,iBAEF,CAEA,UACE,WAAY,CAGZ,cAAe,CADf,kBAAmB,CADnB,UAGF,CAEA,gBAOE,wBAAyB,CADzB,iBAAkB,CAJlB,WAAY,CAGZ,UAAW,CAFX,SAAU,CAFV,iBAAkB,CAGlB,SAIF,CAEA,0BACE,wBACF,CAEA,6BACE,wBACF,CAEA,OAEE,gBAAiB,CACjB,eAAgB,CAFhB,QAGF,CAEA,gBACE,YAAa,CACb,OACF,CAEA,aAUE,kBAAmB,CATnB,kBAAmB,CACnB,qBAAsB,CAGtB,iBAAkB,CAFlB,UAAc,CAGd,cAAe,CAGf,YAAa,CAFb,cAAe,CAMf,WAAY,CAFZ,sBAAuB,CACvB,cAAe,CARf,gBAAiB,CAIjB,uBAMF,CAEA,mBACE,kBAAmB,CACnB,iBACF,CAEA,oBACE,kBAAmB,CACnB,oBACF,CAEA,UAKE,kBAAmB,CADnB,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CAHvB,iBAAkB,CAFlB,UAMF,CAEA,UAIE,iBAAkB,CAFlB,UAAW,CAIX,OAAQ,CALR,SAMF,CAEA,oBANE,uBAAwB,CAExB,iBAUF,CANA,UAKE,UAAW,CAHX,WAAY,CADZ,SAKF,CAGA,iBAOE,0BAA4B,CAN5B,kBAAmB,CAInB,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CAFf,kBAAwB,CADxB,iBAKF,CAEA,mBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,cAEE,8BAA+B,CAC/B,eAAgB,CAFhB,oBAGF,CAGA,gBACE,kBACF,CAEA,uBACE,kBACF,CAEA,gCACE,iBACF,CAEA,sBAIE,UAAc,CAFd,gBAAiB,CACjB,eAAgB,CAFhB,cAIF,CAEA,yBAGE,UAAc,CADd,eAAiB,CADjB,QAGF,CAEA,kBACE,iBACF,CAEA,mBACE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAElB,cAAe,CAGf,eAAgB,CAJhB,YAAa,CAGb,iBAAkB,CADlB,uBAGF,CAEA,yBACE,kBAAmB,CACnB,iBACF,CAEA,qBACE,6BACF,CAEA,0BACE,6BACF,CAEA,gBAGE,oBAAqB,CADrB,eAAgB,CADhB,cAAiB,CAGjB,kBACF,CAEA,gBAGE,UAAW,CADX,eAAiB,CADjB,QAGF,CAEA,oBAIE,eAAgB,CAChB,qBAAsB,CAGtB,iBAAkB,CAFlB,UAAc,CAGd,cAAe,CACf,cAAe,CAHf,eAAgB,CANhB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAST,uBACF,CAEA,0BACE,eAAgB,CAChB,iBACF,CAGA,gBACE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAClB,YACF,CAEA,iBAGE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAElB,UAAc,CACd,mBAAoB,CACpB,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CAVnB,gBAAiB,CAIjB,YAAa,CAKb,eAAgB,CAVhB,UAYF,CAEA,uBAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,8BACE,UACF,CAEA,cACE,YAAa,CACb,OACF,CAEA,4BAGE,WAAY,CADZ,iBAAkB,CAElB,cAAe,CACf,cAAe,CACf,eAAgB,CALhB,gBAAiB,CAMjB,uBACF,CAEA,aACE,kBAAmB,CACnB,UACF,CAEA,mBACE,kBACF,CAEA,eACE,eAAgB,CAChB,UACF,CAEA,qBACE,eACF,CAGA,eAIE,UAAc,CAFd,gBAAiB,CACjB,eAAgB,CAFhB,kBAIF,CAEA,cACE,gBAAiB,CACjB,eACF,CAEA,cACE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAGlB,cAAe,CADf,iBAAkB,CADlB,YAAa,CAGb,uBACF,CAEA,oBACE,kBAAmB,CACnB,iBACF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,iBACF,CAEA,WAEE,UAAW,CADX,eAEF,CAEA,eACE,eAAgB,CAChB,qBAAsB,CAGtB,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,cAAe,CAHf,eAAgB,CAIhB,uBACF,CAEA,qBACE,kBAAmB,CACnB,oBACF,CAEA,cAGE,oBAAqB,CACrB,UAAc,CAFd,eAAgB,CADhB,QAIF,CAGA,sBAaE,kBAAmB,CAPnB,kBAAmB,CACnB,WAAY,CACZ,iBAAkB,CANlB,WAAY,CAQZ,+BAAyC,CADzC,cAAe,CAGf,YAAa,CAPb,WAAY,CASZ,sBAAuB,CAbvB,cAAe,CAEf,UAAW,CAQX,uBAAyB,CAPzB,UAAW,CAWX,YACF,CAEA,4BACE,kBAAmB,CAEnB,2BAAyC,CADzC,qBAEF,CAEA,WAGE,8BAA+B,CAD/B,WAAY,CADZ,UAGF,CAGA,eAME,oBAA8B,CAD9B,QAAS,CAET,YAAa,CACb,wBAAyB,CALzB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAON,YACF,CAEA,kBAME,+BAAiC,CALjC,kBAAmB,CAInB,gCAA0C,CAF1C,YAAa,CACb,eAAgB,CAFhB,WAKF,CAEA,wBACE,GACE,0BACF,CACA,GACE,uBACF,CACF,CAEA,iBAGE,kBAAmB,CAEnB,4BAA6B,CAJ7B,YAAa,CACb,6BAA8B,CAE9B,YAEF,CAEA,gBAIE,UAAc,CAFd,gBAAiB,CACjB,eAAgB,CAFhB,QAIF,CAEA,cACE,eAAgB,CAChB,qBAAsB,CAGtB,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,cAAe,CAHf,gBAAiB,CAIjB,uBACF,CAEA,oBACE,eAAgB,CAChB,iBACF,CAEA,kBACE,YACF,CAEA,kBACE,kBACF,CAEA,eAIE,UAAc,CAFd,gBAAiB,CACjB,eAAgB,CAFhB,eAIF,CAGA,kBACE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAClB,YACF,CAEA,aAIE,UAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,cAIF,CAEA,eAGE,UAAc,CADd,eAAiB,CADjB,eAGF,CAEA,mBAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,yBAGE,iBAAkB,CADlB,UAAW,CADX,SAGF,CAEA,mEAEE,wBACF,CAEA,sCACE,wBACF,CAEA,oBACE,eACF,CAEA,yDAEE,aACF,CAEA,iCACE,aACF,CAGA,aACE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAElB,kBAAmB,CADnB,YAEF,CAEA,aACE,QACF,CAEA,kBAIE,UAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,cAIF,CAEA,kBAGE,UAAc,CADd,eAAiB,CADjB,cAGF,CAGA,uGAGE,SACF,CAEA,yHAGE,kBACF,CAEA,yHAGE,eAAgB,CAChB,iBACF,CAEA,2IAGE,eACF,CAGA,sBACE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAElB,kBAAmB,CADnB,YAAa,CAEb,iBACF,CAEA,yBAIE,eAAgB,CAChB,qBAAsB,CAGtB,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,cAAe,CACf,eAAiB,CAJjB,eAAgB,CANhB,iBAAkB,CAElB,SAAU,CADV,OAAQ,CAUR,uBACF,CAEA,+BACE,kBAAmB,CACnB,oBACF,CAEA,mCAME,WAAY,CADZ,iBAAkB,CAHlB,WAAY,CAKZ,cAAe,CACf,cAAe,CACf,eAAgB,CALhB,gBAAiB,CAHjB,iBAAkB,CAElB,UAAW,CAOX,uBACF,CAEA,gBACE,kBAAmB,CACnB,UACF,CAEA,sBACE,kBACF,CAEA,mBACE,kBAAmB,CACnB,UACF,CAEA,yBACE,kBACF,CAGA,mBACE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAClB,YAAa,CACb,iBACF,CAEA,mBAGE,UAAc,CADd,cAAe,CADf,cAGF,CAEA,sBAGE,UAAc,CADd,eAAiB,CADjB,QAGF,CAGA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,iBACE,kBAAmB,CACnB,WAAY,CAGZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,cAAe,CACf,eAAgB,CAJhB,gBAAiB,CAKjB,uBACF,CAEA,uBACE,kBACF,CAEA,0BACE,eAAgB,CAEhB,kBAAmB,CADnB,UAEF,CAGA,wBAQE,kBAAmB,CAPnB,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAGlB,YAAa,CACb,6BAA8B,CAF9B,kBAAmB,CADnB,YAAa,CAKb,uBACF,CAEA,8BACE,eAAgB,CAChB,oBACF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAEb,OAAQ,CACR,iBACF,CAEA,kBACE,cAAe,CACf,cACF,CAEA,gBACE,UAAW,CACX,cAAe,CACf,YACF,CAEA,uBAGE,UAAW,CADX,eAAiB,CADjB,QAGF,CAEA,aACE,kBAAmB,CACnB,WAAY,CAGZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,cAAe,CACf,eAAgB,CAJhB,gBAAiB,CAKjB,uBACF,CAEA,mBACE,kBACF,CAGA,sBACE,YAAa,CACb,QAAS,CACT,eACF,CAEA,oBASE,kBAAmB,CANnB,WAAY,CACZ,iBAAkB,CAMlB,UAAY,CALZ,cAAe,CAJf,QAAO,CAKP,cAAe,CACf,eAAgB,CALhB,YAAa,CAMb,uBAGF,CAEA,0BACE,kBACF,CAGA,qBAKE,oBAAkC,CAElC,6BAA8B,CAD9B,iBAAkB,CALlB,uBAAyB,CACzB,eAAgB,CAChB,cAAe,CACf,WAIF,CAKA,kBASE,kBAAmB,CAEnB,yBAA2B,CAL3B,gBAA8B,CAC9B,YAAa,CAFb,WAAY,CAGZ,sBAAuB,CALvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAMX,aAEF,CAEA,kBAQE,4BAA8B,CAP9B,kBAAmB,CACnB,kBAAmB,CAInB,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAJhB,SAAU,CAEV,SAIF,CAEA,iBAGE,kBAAmB,CAEnB,4BAA6B,CAJ7B,YAAa,CACb,6BAA8B,CAE9B,YAEF,CAEA,oBAEE,UAAc,CACd,gBAAiB,CAFjB,QAGF,CAEA,gBAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAUZ,iBAAkB,CATlB,UAAW,CAEX,cAAe,CAIf,YAAa,CALb,cAAe,CAIf,WAAY,CAGZ,sBAAuB,CALvB,SAAU,CAOV,uBAAyB,CANzB,UAOF,CAEA,sBACE,eAAgB,CAChB,UACF,CAEA,eACE,YAAa,CACb,iBACF,CAEA,eAME,eAAiB,CAHjB,qBAAsB,CADtB,YAAa,CADb,WAMF,CAEA,gCALE,iBAAkB,CAClB,kBAcF,CAVA,iBASE,6BAA8B,CAR9B,UAAc,CACd,cAAe,CACf,eAAgB,CAIhB,YAGF,CAEA,iCANE,kBAAmB,CADnB,eAYF,CALA,gBAGE,iBAAkB,CADlB,YAGF,CAEA,kBAEE,UAAc,CACd,cAAe,CAFf,YAGF,CAEA,uBACE,aACF,CAEA,kBACE,GAAO,SAAY,CACnB,GAAK,SAAY,CACnB,CAEA,qBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,cAUE,kBAAmB,CATnB,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAUlB,aAAc,CAPd,cAAe,CAEf,YAAa,CAMb,mBAAoB,CACpB,iBAAkB,CANlB,6BAA8B,CAJ9B,iBAAkB,CADlB,YAAa,CAQb,eAAgB,CALhB,uBAAyB,CAIzB,UAKF,CAEA,oBACE,eAAgB,CAChB,iBACF,CAEA,mBAEE,UAAc,CADd,cAEF,CAEA,oBAEE,UAAW,CADX,gBAEF,CAEA,oBAEE,UAAc,CADd,eAAiB,CAEjB,eACF,CAGA,qBAOE,+BAAiC,CANjC,kBAAmB,CAInB,kBAAmB,CADnB,eAAgB,CADhB,cAAe,CAGf,eAAgB,CAJhB,WAMF,CAEA,mBACE,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAElB,kBAAmB,CADnB,YAEF,CAEA,oBAIE,UAAc,CAHd,aAAc,CACd,cAAe,CACf,eAAgB,CAEhB,iBACF,CAEA,0BAGE,UAAc,CADd,eAAiB,CAEjB,iBAAkB,CAHlB,eAIF,CAEA,uBAGE,oBAAqB,CADrB,WAAY,CADZ,UAGF,CAEA,qBAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,cACF,CAEA,mBACE,eAAgB,CAChB,qBAAsB,CAGtB,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,cAAe,CACf,eAAiB,CAEjB,cAAe,CANf,gBAAiB,CAKjB,uBAEF,CAEA,yBACE,eAAgB,CAChB,iBACF,CAEA,kBAGE,UAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,cAAe,CACf,iBACF,CAEA,4BACE,kBAAmB,CACnB,WAAY,CAGZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CACf,cAAe,CACf,eAAgB,CAGhB,cAAe,CAPf,iBAAkB,CAKlB,uBAAyB,CACzB,UAEF,CAEA,kCACE,kBACF,CAGA,wBAIE,UAAc,CAFd,gBAAiB,CACjB,eAAgB,CAFhB,eAIF,CAEA,sBAEE,UAAc,CADd,cAAe,CAIf,iBAAkB,CADlB,gBAAiB,CADjB,kBAGF,CAEA,cAGE,kBAAmB,CAEnB,4BAA6B,CAJ7B,YAAa,CACb,6BAA8B,CAE9B,cAEF,CAEA,yBACE,kBACF,CAEA,gBAEE,UAAc,CADd,eAEF,CAEA,iBAGE,eAAgB,CAChB,WAAY,CACZ,kBAAmB,CACnB,cAAe,CAJf,WAAY,CAKZ,iBAAkB,CAClB,uBAAyB,CAPzB,UAQF,CAEA,wBACE,kBACF,CAEA,uBAGE,eAAiB,CACjB,iBAAkB,CAFlB,WAAY,CAKZ,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,uBAAyB,CAPzB,UAQF,CAEA,8BACE,0BACF,CAEA,eAKE,kBAAmB,CAGnB,6BAA8B,CAF9B,iBAAkB,CAJlB,UAAc,CADd,cAAe,CAMf,gBAAiB,CAJjB,eAAgB,CAChB,YAKF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\r\n  margin: 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n    monospace;\r\n}", "/* Dark theme matching Android app */\r\n.app-container {\r\n  background-color: #121212;\r\n  color: #ffffff;\r\n  min-height: 100vh;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\r\n  position: relative;\r\n}\r\n\r\n/* Header */\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 16px;\r\n  border-bottom: 1px solid #333;\r\n}\r\n\r\n.title-container {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo-container {\r\n  position: relative;\r\n  margin-right: 8px;\r\n}\r\n\r\n.app-logo {\r\n  height: 32px;\r\n  width: auto;\r\n  object-fit: contain;\r\n  max-width: 32px;\r\n}\r\n\r\n.connection-dot {\r\n  position: absolute;\r\n  bottom: -2px;\r\n  left: -2px;\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 4px;\r\n  border: 1px solid #121212;\r\n}\r\n\r\n.connection-dot.connected {\r\n  background-color: #4CAF50;\r\n}\r\n\r\n.connection-dot.disconnected {\r\n  background-color: #F44336;\r\n}\r\n\r\n.title {\r\n  margin: 0;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.icon-button {\r\n  background: #2a2a2a;\r\n  border: 1px solid #444;\r\n  color: #ffffff;\r\n  padding: 8px 12px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-width: 36px;\r\n  height: 36px;\r\n}\r\n\r\n.icon-button:hover {\r\n  background: #3a3a3a;\r\n  border-color: #555;\r\n}\r\n\r\n.icon-button.active {\r\n  background: #4CAF50;\r\n  border-color: #4CAF50;\r\n}\r\n\r\n.pin-icon {\r\n  width: 16px;\r\n  height: 16px;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.pin-head {\r\n  width: 8px;\r\n  height: 8px;\r\n  background: currentColor;\r\n  border-radius: 50%;\r\n  position: absolute;\r\n  top: 2px;\r\n}\r\n\r\n.pin-body {\r\n  width: 2px;\r\n  height: 10px;\r\n  background: currentColor;\r\n  position: absolute;\r\n  bottom: 2px;\r\n}\r\n\r\n/* Success Message */\r\n.success-message {\r\n  background: #4CAF50;\r\n  color: white;\r\n  padding: 12px 16px;\r\n  margin: 0 16px 16px 16px;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  animation: slideIn 0.3s ease;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Main Content */\r\n.main-content {\r\n  padding: 0 16px 100px 16px;\r\n  max-height: calc(100vh - 120px);\r\n  overflow-y: auto;\r\n}\r\n\r\n/* Device Sections */\r\n.device-section {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.device-section-header {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.device-section-title-container {\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.device-section-title {\r\n  margin: 0 0 4px 0;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n}\r\n\r\n.device-section-subtitle {\r\n  margin: 0;\r\n  font-size: 0.9rem;\r\n  color: #aaaaaa;\r\n}\r\n\r\n.device-clipboard {\r\n  position: relative;\r\n}\r\n\r\n.clipboard-content {\r\n  background: #1e1e1e;\r\n  border: 1px solid #333;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  position: relative;\r\n  min-height: 80px;\r\n}\r\n\r\n.clipboard-content:hover {\r\n  background: #252525;\r\n  border-color: #444;\r\n}\r\n\r\n.this-device-content {\r\n  border-left: 4px solid #2196F3;\r\n}\r\n\r\n.connected-device-content {\r\n  border-left: 4px solid #4CAF50;\r\n}\r\n\r\n.clipboard-text {\r\n  margin: 0 0 8px 0;\r\n  line-height: 1.5;\r\n  word-wrap: break-word;\r\n  padding-right: 40px;\r\n}\r\n\r\n.clipboard-meta {\r\n  margin: 0;\r\n  font-size: 0.8rem;\r\n  color: #888;\r\n}\r\n\r\n.edit-button-inside {\r\n  position: absolute;\r\n  top: 12px;\r\n  right: 12px;\r\n  background: #333;\r\n  border: 1px solid #555;\r\n  color: #ffffff;\r\n  padding: 6px 8px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.edit-button-inside:hover {\r\n  background: #444;\r\n  border-color: #666;\r\n}\r\n\r\n/* Edit Container */\r\n.edit-container {\r\n  background: #1e1e1e;\r\n  border: 1px solid #333;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n}\r\n\r\n.edit-text-input {\r\n  width: 100%;\r\n  min-height: 100px;\r\n  background: #2a2a2a;\r\n  border: 1px solid #444;\r\n  border-radius: 6px;\r\n  padding: 12px;\r\n  color: #ffffff;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  resize: vertical;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.edit-text-input:focus {\r\n  outline: none;\r\n  border-color: #2196F3;\r\n  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);\r\n}\r\n\r\n.edit-text-input::placeholder {\r\n  color: #888;\r\n}\r\n\r\n.edit-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.save-button, .cancel-button {\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  border: none;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.save-button {\r\n  background: #4CAF50;\r\n  color: white;\r\n}\r\n\r\n.save-button:hover {\r\n  background: #45a049;\r\n}\r\n\r\n.cancel-button {\r\n  background: #666;\r\n  color: white;\r\n}\r\n\r\n.cancel-button:hover {\r\n  background: #777;\r\n}\r\n\r\n/* History */\r\n.history-title {\r\n  margin: 32px 0 16px 0;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n}\r\n\r\n.history-list {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.history-item {\r\n  background: #1e1e1e;\r\n  border: 1px solid #333;\r\n  border-radius: 8px;\r\n  padding: 12px;\r\n  margin-bottom: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.history-item:hover {\r\n  background: #252525;\r\n  border-color: #444;\r\n}\r\n\r\n.history-item-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.timestamp {\r\n  font-size: 0.8rem;\r\n  color: #888;\r\n}\r\n\r\n.delete-button {\r\n  background: #666;\r\n  border: 1px solid #777;\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.delete-button:hover {\r\n  background: #F44336;\r\n  border-color: #F44336;\r\n}\r\n\r\n.item-content {\r\n  margin: 0;\r\n  line-height: 1.4;\r\n  word-wrap: break-word;\r\n  color: #ffffff;\r\n}\r\n\r\n/* Floating Sync Button */\r\n.floating-sync-button {\r\n  position: fixed;\r\n  bottom: 24px;\r\n  right: 24px;\r\n  width: 56px;\r\n  height: 56px;\r\n  background: #2196F3;\r\n  border: none;\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.floating-sync-button:hover {\r\n  background: #1976D2;\r\n  transform: scale(1.05);\r\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.sync-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  filter: brightness(0) invert(1);\r\n}\r\n\r\n/* Settings Modal */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  z-index: 2000;\r\n}\r\n\r\n.settings-sidebar {\r\n  background: #1e1e1e;\r\n  width: 400px;\r\n  height: 100vh;\r\n  overflow-y: auto;\r\n  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.3);\r\n  animation: slideInRight 0.3s ease;\r\n}\r\n\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(100%);\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n.settings-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #333;\r\n}\r\n\r\n.settings-title {\r\n  margin: 0;\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n}\r\n\r\n.close-button {\r\n  background: #666;\r\n  border: 1px solid #777;\r\n  color: white;\r\n  padding: 8px 12px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.close-button:hover {\r\n  background: #777;\r\n  border-color: #888;\r\n}\r\n\r\n.settings-content {\r\n  padding: 20px;\r\n}\r\n\r\n.settings-section {\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.section-title {\r\n  margin: 0 0 16px 0;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n}\r\n\r\n/* Device Info Card */\r\n.device-info-card {\r\n  background: #2a2a2a;\r\n  border: 1px solid #444;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n}\r\n\r\n.device-name {\r\n  margin: 0 0 8px 0;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n}\r\n\r\n.device-detail {\r\n  margin: 0 0 12px 0;\r\n  font-size: 0.9rem;\r\n  color: #aaaaaa;\r\n}\r\n\r\n.device-status-row {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.device-status-indicator {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n}\r\n\r\n.device-status-indicator.connected,\r\n.device-status-indicator.active {\r\n  background-color: #4CAF50;\r\n}\r\n\r\n.device-status-indicator.disconnected {\r\n  background-color: #F44336;\r\n}\r\n\r\n.device-status-text {\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.device-status-text.connected,\r\n.device-status-text.active {\r\n  color: #4CAF50;\r\n}\r\n\r\n.device-status-text.disconnected {\r\n  color: #F44336;\r\n}\r\n\r\n/* Device Cards */\r\n.device-card {\r\n  background: #2a2a2a;\r\n  border: 1px solid #444;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.device-info {\r\n  flex: 1;\r\n}\r\n\r\n.device-card-name {\r\n  margin: 0 0 4px 0;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n}\r\n\r\n.device-card-type {\r\n  margin: 0 0 8px 0;\r\n  font-size: 0.9rem;\r\n  color: #aaaaaa;\r\n}\r\n\r\n/* Scrollbar Styling */\r\n.main-content::-webkit-scrollbar,\r\n.history-list::-webkit-scrollbar,\r\n.settings-sidebar::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.main-content::-webkit-scrollbar-track,\r\n.history-list::-webkit-scrollbar-track,\r\n.settings-sidebar::-webkit-scrollbar-track {\r\n  background: #1e1e1e;\r\n}\r\n\r\n.main-content::-webkit-scrollbar-thumb,\r\n.history-list::-webkit-scrollbar-thumb,\r\n.settings-sidebar::-webkit-scrollbar-thumb {\r\n  background: #444;\r\n  border-radius: 4px;\r\n}\r\n\r\n.main-content::-webkit-scrollbar-thumb:hover,\r\n.history-list::-webkit-scrollbar-thumb:hover,\r\n.settings-sidebar::-webkit-scrollbar-thumb:hover {\r\n  background: #555;\r\n}\r\n\r\n/* Enhanced Device Cards */\r\n.enhanced-device-card {\r\n  background: #2a2a2a;\r\n  border: 1px solid #444;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 12px;\r\n  position: relative;\r\n}\r\n\r\n.remove-button-top-right {\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n  background: #666;\r\n  border: 1px solid #777;\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.remove-button-top-right:hover {\r\n  background: #F44336;\r\n  border-color: #F44336;\r\n}\r\n\r\n.device-action-button-bottom-right {\r\n  position: absolute;\r\n  bottom: 12px;\r\n  right: 12px;\r\n  padding: 6px 12px;\r\n  border-radius: 4px;\r\n  border: none;\r\n  cursor: pointer;\r\n  font-size: 12px;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.connect-button {\r\n  background: #4CAF50;\r\n  color: white;\r\n}\r\n\r\n.connect-button:hover {\r\n  background: #45a049;\r\n}\r\n\r\n.disconnect-button {\r\n  background: #F44336;\r\n  color: white;\r\n}\r\n\r\n.disconnect-button:hover {\r\n  background: #da190b;\r\n}\r\n\r\n/* Empty Device List */\r\n.empty-device-list {\r\n  background: #2a2a2a;\r\n  border: 1px solid #444;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.empty-device-text {\r\n  margin: 0 0 8px 0;\r\n  font-size: 1rem;\r\n  color: #ffffff;\r\n}\r\n\r\n.empty-device-subtext {\r\n  margin: 0;\r\n  font-size: 0.9rem;\r\n  color: #aaaaaa;\r\n}\r\n\r\n/* Section Header */\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.discover-button {\r\n  background: #8B5CF6;\r\n  border: none;\r\n  color: white;\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.discover-button:hover {\r\n  background: #7C3AED;\r\n}\r\n\r\n.discover-button-disabled {\r\n  background: #666;\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Discovered Device Cards */\r\n.discovered-device-card {\r\n  background: #2a2a2a;\r\n  border: 1px solid #444;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 12px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.discovered-device-card:hover {\r\n  background: #333;\r\n  border-color: #8B5CF6;\r\n}\r\n\r\n.device-name-with-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.device-type-icon {\r\n  font-size: 18px;\r\n  min-width: 24px;\r\n}\r\n\r\n.device-card-ip {\r\n  color: #888;\r\n  font-size: 12px;\r\n  margin: 2px 0;\r\n}\r\n\r\n.device-card-last-seen {\r\n  margin: 0;\r\n  font-size: 0.8rem;\r\n  color: #888;\r\n}\r\n\r\n.pair-button {\r\n  background: #2196F3;\r\n  border: none;\r\n  color: white;\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.pair-button:hover {\r\n  background: #1976D2;\r\n}\r\n\r\n/* QR Buttons Container */\r\n.qr-buttons-container {\r\n  display: flex;\r\n  gap: 12px;\r\n  margin-top: 16px;\r\n}\r\n\r\n.qr-generate-button {\r\n  flex: 1;\r\n  padding: 12px;\r\n  border: none;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n  background: #9C27B0;\r\n  color: white;\r\n}\r\n\r\n.qr-generate-button:hover {\r\n  background: #7B1FA2;\r\n}\r\n\r\n/* Disconnected Notice */\r\n.disconnected-notice {\r\n  color: #F44336 !important;\r\n  font-weight: 600;\r\n  margin-top: 8px;\r\n  padding: 8px;\r\n  background: rgba(244, 67, 54, 0.1);\r\n  border-radius: 4px;\r\n  border-left: 3px solid #F44336;\r\n}\r\n\r\n\r\n\r\n/* QR Code Modal */\r\n.qr-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 10000;\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n.qr-modal-content {\r\n  background: #1e1e1e;\r\n  border-radius: 12px;\r\n  padding: 0;\r\n  max-width: 400px;\r\n  width: 90%;\r\n  max-height: 80vh;\r\n  overflow-y: auto;\r\n  animation: slideInUp 0.3s ease;\r\n}\r\n\r\n.qr-modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #333;\r\n}\r\n\r\n.qr-modal-header h3 {\r\n  margin: 0;\r\n  color: #ffffff;\r\n  font-size: 1.2rem;\r\n}\r\n\r\n.qr-modal-close {\r\n  background: none;\r\n  border: none;\r\n  color: #888;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  padding: 0;\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 50%;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.qr-modal-close:hover {\r\n  background: #333;\r\n  color: #ffffff;\r\n}\r\n\r\n.qr-modal-body {\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.qr-code-image {\r\n  width: 250px;\r\n  height: 250px;\r\n  border: 2px solid #444;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  background: white;\r\n}\r\n\r\n.qr-instructions {\r\n  color: #aaaaaa;\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  margin-bottom: 20px;\r\n  text-align: left;\r\n  background: #2a2a2a;\r\n  padding: 16px;\r\n  border-radius: 8px;\r\n  border-left: 3px solid #8B5CF6;\r\n}\r\n\r\n.qr-device-info {\r\n  background: #2a2a2a;\r\n  padding: 12px;\r\n  border-radius: 6px;\r\n  text-align: left;\r\n}\r\n\r\n.qr-device-info p {\r\n  margin: 4px 0;\r\n  color: #ffffff;\r\n  font-size: 14px;\r\n}\r\n\r\n.qr-device-info strong {\r\n  color: #8B5CF6;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; }\r\n  to { opacity: 1; }\r\n}\r\n\r\n@keyframes slideInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Setting Items */\r\n.setting-item {\r\n  background: #2a2a2a;\r\n  border: 1px solid #444;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n  text-align: left;\r\n  color: inherit;\r\n  font-family: inherit;\r\n  font-size: inherit;\r\n}\r\n\r\n.setting-item:hover {\r\n  background: #333;\r\n  border-color: #555;\r\n}\r\n\r\n.setting-item-text {\r\n  font-size: 1rem;\r\n  color: #ffffff;\r\n}\r\n\r\n.setting-item-arrow {\r\n  font-size: 1.2rem;\r\n  color: #888;\r\n}\r\n\r\n.setting-item-value {\r\n  font-size: 0.9rem;\r\n  color: #ffffff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Sync Settings Modal */\r\n.sync-settings-modal {\r\n  background: #1e1e1e;\r\n  width: 500px;\r\n  max-width: 90vw;\r\n  max-height: 80vh;\r\n  border-radius: 12px;\r\n  overflow-y: auto;\r\n  animation: slideInRight 0.3s ease;\r\n}\r\n\r\n.sync-setting-item {\r\n  background: #2a2a2a;\r\n  border: 1px solid #444;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.sync-setting-label {\r\n  display: block;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.sync-setting-description {\r\n  margin: 0 0 12px 0;\r\n  font-size: 0.9rem;\r\n  color: #aaaaaa;\r\n  font-style: italic;\r\n}\r\n\r\n.sync-setting-checkbox {\r\n  width: 20px;\r\n  height: 20px;\r\n  accent-color: #4CAF50;\r\n}\r\n\r\n.sync-delay-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.sync-delay-button {\r\n  background: #444;\r\n  border: 1px solid #666;\r\n  color: white;\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  transition: all 0.2s ease;\r\n  min-width: 40px;\r\n}\r\n\r\n.sync-delay-button:hover {\r\n  background: #555;\r\n  border-color: #777;\r\n}\r\n\r\n.sync-delay-value {\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  min-width: 40px;\r\n  text-align: center;\r\n}\r\n\r\n.cross-platform-sync-button {\r\n  background: #8B5CF6;\r\n  border: none;\r\n  color: white;\r\n  padding: 12px 16px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  transition: all 0.2s ease;\r\n  width: 100%;\r\n  margin-top: 8px;\r\n}\r\n\r\n.cross-platform-sync-button:hover {\r\n  background: #7C3AED;\r\n}\r\n\r\n/* Settings Section Title */\r\n.settings-section-title {\r\n  margin: 0 0 12px 0;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n}\r\n\r\n.settings-description {\r\n  font-size: 14px;\r\n  color: #aaaaaa;\r\n  margin-bottom: 16px;\r\n  line-height: 20px;\r\n  font-style: italic;\r\n}\r\n\r\n.settings-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #333;\r\n}\r\n\r\n.settings-row:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.settings-label {\r\n  font-size: 0.9rem;\r\n  color: #ffffff;\r\n}\r\n\r\n.settings-toggle {\r\n  width: 50px;\r\n  height: 24px;\r\n  background: #666;\r\n  border: none;\r\n  border-radius: 12px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.settings-toggle.active {\r\n  background: #4CAF50;\r\n}\r\n\r\n.settings-toggle-thumb {\r\n  width: 20px;\r\n  height: 20px;\r\n  background: white;\r\n  border-radius: 50%;\r\n  position: absolute;\r\n  top: 2px;\r\n  left: 2px;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.settings-toggle-thumb.active {\r\n  transform: translateX(26px);\r\n}\r\n\r\n.settings-note {\r\n  font-size: 12px;\r\n  color: #808080;\r\n  margin-top: 16px;\r\n  padding: 12px;\r\n  background: #2a2a2a;\r\n  border-radius: 8px;\r\n  line-height: 18px;\r\n  border-left: 3px solid #8B5CF6;\r\n}"], "names": [], "sourceRoot": ""}