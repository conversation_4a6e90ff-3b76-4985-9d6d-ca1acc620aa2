/* Dark theme matching Android app */
.app-container {
  background-color: #121212;
  color: #ffffff;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  position: relative;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #333;
}

.title-container {
  display: flex;
  align-items: center;
}

.logo-container {
  position: relative;
  margin-right: 8px;
}

.app-logo {
  height: 32px;
  width: auto;
  object-fit: contain;
  max-width: 32px;
}

.connection-dot {
  position: absolute;
  bottom: -2px;
  left: -2px;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  border: 1px solid #121212;
}

.connection-dot.connected {
  background-color: #4CAF50;
}

.connection-dot.disconnected {
  background-color: #F44336;
}

.title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.icon-button {
  background: #2a2a2a;
  border: 1px solid #444;
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
}

.icon-button:hover {
  background: #3a3a3a;
  border-color: #555;
}

.icon-button.active {
  background: #4CAF50;
  border-color: #4CAF50;
}

.pin-icon {
  width: 16px;
  height: 16px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pin-head {
  width: 8px;
  height: 8px;
  background: currentColor;
  border-radius: 50%;
  position: absolute;
  top: 2px;
}

.pin-body {
  width: 2px;
  height: 10px;
  background: currentColor;
  position: absolute;
  bottom: 2px;
}

/* Success Message */
.success-message {
  background: #4CAF50;
  color: white;
  padding: 12px 16px;
  margin: 0 16px 16px 16px;
  border-radius: 6px;
  font-size: 14px;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Main Content */
.main-content {
  padding: 0 16px 100px 16px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

/* Device Sections */
.device-section {
  margin-bottom: 24px;
}

.device-section-header {
  margin-bottom: 12px;
}

.device-section-title-container {
  margin-bottom: 8px;
}

.device-section-title {
  margin: 0 0 4px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

.device-section-subtitle {
  margin: 0;
  font-size: 0.9rem;
  color: #aaaaaa;
}

.device-clipboard {
  position: relative;
}

.clipboard-content {
  background: #1e1e1e;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-height: 80px;
}

.clipboard-content:hover {
  background: #252525;
  border-color: #444;
}

.this-device-content {
  border-left: 4px solid #2196F3;
}

.connected-device-content {
  border-left: 4px solid #4CAF50;
}

.clipboard-text {
  margin: 0 0 8px 0;
  line-height: 1.5;
  word-wrap: break-word;
  padding-right: 40px;
}

.clipboard-meta {
  margin: 0;
  font-size: 0.8rem;
  color: #888;
}

.edit-button-inside {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #333;
  border: 1px solid #555;
  color: #ffffff;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.edit-button-inside:hover {
  background: #444;
  border-color: #666;
}

/* Edit Container */
.edit-container {
  background: #1e1e1e;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
}

.edit-text-input {
  width: 100%;
  min-height: 100px;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 12px;
  color: #ffffff;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  margin-bottom: 12px;
}

.edit-text-input:focus {
  outline: none;
  border-color: #2196F3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.edit-text-input::placeholder {
  color: #888;
}

.edit-actions {
  display: flex;
  gap: 8px;
}

.save-button, .cancel-button {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.save-button {
  background: #4CAF50;
  color: white;
}

.save-button:hover {
  background: #45a049;
}

.cancel-button {
  background: #666;
  color: white;
}

.cancel-button:hover {
  background: #777;
}

/* History */
.history-title {
  margin: 32px 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  background: #1e1e1e;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  background: #252525;
  border-color: #444;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timestamp {
  font-size: 0.8rem;
  color: #888;
}

.delete-button {
  background: #666;
  border: 1px solid #777;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.delete-button:hover {
  background: #F44336;
  border-color: #F44336;
}

.item-content {
  margin: 0;
  line-height: 1.4;
  word-wrap: break-word;
  color: #ffffff;
}

/* Floating Sync Button */
.floating-sync-button {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  background: #2196F3;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.floating-sync-button:hover {
  background: #1976D2;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.sync-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}

/* Settings Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: flex-end;
  z-index: 2000;
}

.settings-sidebar {
  background: #1e1e1e;
  width: 400px;
  height: 100vh;
  overflow-y: auto;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.3);
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #333;
}

.settings-title {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffffff;
}

.close-button {
  background: #666;
  border: 1px solid #777;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #777;
  border-color: #888;
}

.settings-content {
  padding: 20px;
}

.settings-section {
  margin-bottom: 32px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

/* Device Info Card */
.device-info-card {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 16px;
}

.device-name {
  margin: 0 0 8px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

.device-detail {
  margin: 0 0 12px 0;
  font-size: 0.9rem;
  color: #aaaaaa;
}

.device-status-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.device-status-indicator.connected,
.device-status-indicator.active {
  background-color: #4CAF50;
}

.device-status-indicator.disconnected {
  background-color: #F44336;
}

.device-status-text {
  font-size: 0.9rem;
}

.device-status-text.connected,
.device-status-text.active {
  color: #4CAF50;
}

.device-status-text.disconnected {
  color: #F44336;
}

/* Device Cards */
.device-card {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.device-info {
  flex: 1;
}

.device-card-name {
  margin: 0 0 4px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

.device-card-type {
  margin: 0 0 8px 0;
  font-size: 0.9rem;
  color: #aaaaaa;
}

/* Scrollbar Styling */
.main-content::-webkit-scrollbar,
.history-list::-webkit-scrollbar,
.settings-sidebar::-webkit-scrollbar {
  width: 8px;
}

.main-content::-webkit-scrollbar-track,
.history-list::-webkit-scrollbar-track,
.settings-sidebar::-webkit-scrollbar-track {
  background: #1e1e1e;
}

.main-content::-webkit-scrollbar-thumb,
.history-list::-webkit-scrollbar-thumb,
.settings-sidebar::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover,
.history-list::-webkit-scrollbar-thumb:hover,
.settings-sidebar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Enhanced Device Cards */
.enhanced-device-card {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  position: relative;
}

.remove-button-top-right {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #666;
  border: 1px solid #777;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.2s ease;
}

.remove-button-top-right:hover {
  background: #F44336;
  border-color: #F44336;
}

.device-action-button-bottom-right {
  position: absolute;
  bottom: 12px;
  right: 12px;
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.connect-button {
  background: #4CAF50;
  color: white;
}

.connect-button:hover {
  background: #45a049;
}

.disconnect-button {
  background: #F44336;
  color: white;
}

.disconnect-button:hover {
  background: #da190b;
}

/* Empty Device List */
.empty-device-list {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.empty-device-text {
  margin: 0 0 8px 0;
  font-size: 1rem;
  color: #ffffff;
}

.empty-device-subtext {
  margin: 0;
  font-size: 0.9rem;
  color: #aaaaaa;
}

/* Section Header */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.discover-button {
  background: #8B5CF6;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.discover-button:hover {
  background: #7C3AED;
}

.discover-button-disabled {
  background: #666;
  opacity: 0.7;
  cursor: not-allowed;
}

/* Discovered Device Cards */
.discovered-device-card {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-card-last-seen {
  margin: 0;
  font-size: 0.8rem;
  color: #888;
}

.pair-button {
  background: #2196F3;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.pair-button:hover {
  background: #1976D2;
}

/* QR Buttons Container */
.qr-buttons-container {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.qr-generate-button {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  background: #9C27B0;
  color: white;
}

.qr-generate-button:hover {
  background: #7B1FA2;
}

/* Disconnected Notice */
.disconnected-notice {
  color: #F44336 !important;
  font-weight: 600;
  margin-top: 8px;
  padding: 8px;
  background: rgba(244, 67, 54, 0.1);
  border-radius: 4px;
  border-left: 3px solid #F44336;
}

/* Connection Test Button */
.connection-test-button {
  background: #2196F3;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  margin-top: 12px;
  transition: all 0.2s ease;
}

.connection-test-button:hover {
  background: #1976D2;
}

/* Setting Items */
.setting-item {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  text-align: left;
  color: inherit;
  font-family: inherit;
  font-size: inherit;
}

.setting-item:hover {
  background: #333;
  border-color: #555;
}

.setting-item-text {
  font-size: 1rem;
  color: #ffffff;
}

.setting-item-arrow {
  font-size: 1.2rem;
  color: #888;
}

.setting-item-value {
  font-size: 0.9rem;
  color: #ffffff;
  font-weight: bold;
}

/* Sync Settings Modal */
.sync-settings-modal {
  background: #1e1e1e;
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  border-radius: 12px;
  overflow-y: auto;
  animation: slideInRight 0.3s ease;
}

.sync-setting-item {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.sync-setting-label {
  display: block;
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
}

.sync-setting-description {
  margin: 0 0 12px 0;
  font-size: 0.9rem;
  color: #aaaaaa;
  font-style: italic;
}

.sync-setting-checkbox {
  width: 20px;
  height: 20px;
  accent-color: #4CAF50;
}

.sync-delay-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.sync-delay-button {
  background: #444;
  border: 1px solid #666;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.2s ease;
  min-width: 40px;
}

.sync-delay-button:hover {
  background: #555;
  border-color: #777;
}

.sync-delay-value {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  min-width: 40px;
  text-align: center;
}

.cross-platform-sync-button {
  background: #8B5CF6;
  border: none;
  color: white;
  padding: 12px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  width: 100%;
  margin-top: 8px;
}

.cross-platform-sync-button:hover {
  background: #7C3AED;
}

/* Settings Section Title */
.settings-section-title {
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

.settings-description {
  font-size: 14px;
  color: #aaaaaa;
  margin-bottom: 16px;
  line-height: 20px;
  font-style: italic;
}

.settings-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #333;
}

.settings-row:last-child {
  border-bottom: none;
}

.settings-label {
  font-size: 0.9rem;
  color: #ffffff;
}

.settings-toggle {
  width: 50px;
  height: 24px;
  background: #666;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.settings-toggle.active {
  background: #4CAF50;
}

.settings-toggle-thumb {
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: all 0.2s ease;
}

.settings-toggle-thumb.active {
  transform: translateX(26px);
}

.settings-note {
  font-size: 12px;
  color: #808080;
  margin-top: 16px;
  padding: 12px;
  background: #2a2a2a;
  border-radius: 8px;
  line-height: 18px;
  border-left: 3px solid #8B5CF6;
}