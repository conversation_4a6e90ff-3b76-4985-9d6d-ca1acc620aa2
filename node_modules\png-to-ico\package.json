{"name": "png-to-ico", "version": "2.1.8", "description": "convert png to windows ico format", "main": "index.js", "engines": {"node": ">=8"}, "bin": {"png-to-ico": "bin/cli.js"}, "files": ["bin/*", "lib/*", "*.md", "index.d.ts"], "typings": "index.d.ts", "scripts": {"test": "jest test/*.test.js", "lint": "eslint ."}, "repository": {"type": "git", "url": "git+https://github.com/steambap/png-to-ico.git"}, "keywords": ["png", "ico", "favicon", "favicon-generator", "fast", "convert"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/steambap/png-to-ico/issues"}, "homepage": "https://github.com/steambap/png-to-ico#readme", "dependencies": {"@types/node": "^17.0.36", "minimist": "^1.2.6", "pngjs": "^6.0.0"}, "devDependencies": {"eslint": "^8.16.0", "jest": "^28.1.1"}}