const fs = require('fs');
const path = require('path');

// Simple PNG to ICO conversion using Node.js
// This creates a basic ICO file with the PNG data embedded

function createIcoFromPng(pngPath, icoPath) {
  try {
    const pngData = fs.readFileSync(pngPath);
    
    // ICO file header (6 bytes)
    const icoHeader = Buffer.alloc(6);
    icoHeader.writeUInt16LE(0, 0);      // Reserved (must be 0)
    icoHeader.writeUInt16LE(1, 2);      // Image type (1 = ICO)
    icoHeader.writeUInt16LE(1, 4);      // Number of images
    
    // ICO directory entry (16 bytes)
    const dirEntry = Buffer.alloc(16);
    dirEntry.writeUInt8(0, 0);          // Width (0 = 256)
    dirEntry.writeUInt8(0, 1);          // Height (0 = 256)
    dirEntry.writeUInt8(0, 2);          // Color palette (0 = no palette)
    dirEntry.writeUInt8(0, 3);          // Reserved
    dirEntry.writeUInt16LE(1, 4);       // Color planes
    dirEntry.writeUInt16LE(32, 6);      // Bits per pixel
    dirEntry.writeUInt32LE(pngData.length, 8);  // Image data size
    dirEntry.writeUInt32LE(22, 12);     // Offset to image data
    
    // Combine header, directory entry, and PNG data
    const icoData = Buffer.concat([icoHeader, dirEntry, pngData]);
    
    fs.writeFileSync(icoPath, icoData);
    console.log(`✅ Created ${icoPath}`);
    
  } catch (error) {
    console.error(`❌ Error converting ${pngPath}:`, error.message);
  }
}

// Convert both logo files
console.log('Converting PNG files to ICO format...');

createIcoFromPng('public/clipsy-logo-no-bg.png', 'public/clipsy-logo-no-bg.ico');
createIcoFromPng('public/clipsy-logo-bg.png', 'public/clipsy-logo-bg.ico');

console.log('✅ Icon conversion completed!');
