@echo off
title Clipsy Installer v1.0.0

echo.
echo ===============================================
echo           Clipsy Installation v1.0.0
echo ===============================================
echo.
echo Installing Clipsy automatically...
echo.

REM Create installation directory
set "INSTALL_DIR=%LOCALAPPDATA%\Clipsy"
echo Creating installation directory: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files from the packaged version
echo Copying application files...
if exist "dist-packager\Clipsy-win32-x64" (
    xcopy /E /I /Y "dist-packager\Clipsy-win32-x64\*" "%INSTALL_DIR%\" >nul
    if errorlevel 1 (
        echo ERROR: Failed to copy files!
        exit /b 1
    )
    echo Files copied successfully.
) else (
    echo ERROR: Source files not found!
    echo Please make sure dist-packager\Clipsy-win32-x64 exists.
    exit /b 1
)

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Clipsy.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Clipsy.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\Clipsy.exe'; $Shortcut.Save()}" >nul 2>&1
echo Desktop shortcut created.

REM Create start menu shortcut
echo Creating start menu shortcut...
set "START_MENU_DIR=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Clipsy"
if not exist "%START_MENU_DIR%" mkdir "%START_MENU_DIR%"
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\Clipsy.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Clipsy.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\Clipsy.exe'; $Shortcut.Save()}" >nul 2>&1
echo Start menu shortcut created.

echo.
echo ===============================================
echo     Installation completed successfully!
echo ===============================================
echo.
echo Clipsy has been installed to: %INSTALL_DIR%
echo Desktop shortcut: %USERPROFILE%\Desktop\Clipsy.lnk
echo Start menu shortcut created.
echo.
echo You can now launch Clipsy from the desktop or start menu.
echo.
echo Installation complete!
