import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  // State management matching Android app
  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to <PERSON>lipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');
  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');
  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);
  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);
  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');
  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');

  const [historyItems, setHistoryItems] = useState([
    { id: '1', content: 'This is an older clipboard item. It\'s shorter.', timestamp: '2 minutes ago' },
    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },
    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },
    { id: '4', content: 'Some code snippet: function hello() { console.log("Hello World!"); }', timestamp: '5 hours ago' }
  ]);

  // UI State
  const [showSettings, setShowSettings] = useState(false);
  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [connectionStatus, setConnectionStatus] = useState('connected');
  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);

  // Paired devices state
  const [pairedDevices] = useState([
    {
      id: 'android-1',
      name: 'Android Phone - Personal',
      type: 'Android 14',
      status: 'connected',
      lastSeen: '2 min ago',
      ipAddress: '*************'
    },
    {
      id: 'linux-1',
      name: 'Ubuntu Server - Home',
      type: 'Ubuntu 22.04',
      status: 'disconnected',
      lastSeen: '1 hour ago',
      ipAddress: '*************'
    }
  ]);

  // Functions
  const showMessage = (text) => {
    setSuccessMessage(text);
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  const copyToClipboard = async (content) => {
    try {
      await navigator.clipboard.writeText(content);
      setThisDeviceClipboard(content);
      showMessage('✅ Text copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      showMessage('❌ Failed to copy to clipboard');
    }
  };

  const selectItem = (item) => {
    copyToClipboard(item.content);
    setConnectedDeviceClipboard(item.content);
  };

  const deleteHistoryItem = (itemId) => {
    const updatedHistory = historyItems.filter(item => item.id !== itemId);
    setHistoryItems(updatedHistory);
    showMessage('🗑️ History item deleted!');
  };

  // Device edit functions
  const startEditingThisDevice = () => {
    setEditingThisDeviceText(thisDeviceClipboard);
    setIsEditingThisDevice(true);
  };

  const saveThisDeviceEdit = async () => {
    const newContent = editingThisDeviceText.trim();
    if (!newContent) {
      showMessage('Content cannot be empty');
      return;
    }

    setThisDeviceClipboard(newContent);
    try {
      await navigator.clipboard.writeText(newContent);
    } catch (error) {
      console.warn('Failed to update clipboard:', error);
    }

    setIsEditingThisDevice(false);
    setEditingThisDeviceText('');
    showMessage('✅ This Device clipboard updated!');
  };

  const cancelThisDeviceEdit = () => {
    setIsEditingThisDevice(false);
    setEditingThisDeviceText('');
  };

  const startEditingConnectedDevice = () => {
    setEditingConnectedDeviceText(connectedDeviceClipboard);
    setIsEditingConnectedDevice(true);
  };

  const saveConnectedDeviceEdit = () => {
    const newContent = editingConnectedDeviceText.trim();
    if (!newContent) {
      showMessage('Content cannot be empty');
      return;
    }

    setConnectedDeviceClipboard(newContent);
    setIsEditingConnectedDevice(false);
    setEditingConnectedDeviceText('');
    showMessage('✅ Connected Device clipboard updated!');
  };

  const cancelConnectedDeviceEdit = () => {
    setIsEditingConnectedDevice(false);
    setEditingConnectedDeviceText('');
  };

  const syncNow = () => {
    showMessage('🔄 Syncing with paired devices...');
    setTimeout(() => {
      showMessage('✅ Sync completed!');
    }, 1000);
  };

  const toggleAlwaysOnTop = () => {
    setIsAlwaysOnTop(!isAlwaysOnTop);
    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');
  };

  const minimizeToTray = () => {
    showMessage('➖ Minimizing to background...');
  };

  const toggleFloatingOverlay = () => {
    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);
    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');
  };

  return (
    <div className="app-container">
      {/* Header */}
      <div className="header">
        <div className="title-container">
          <div className="logo-container">
            <img
              src="/clipsy-logo-no-bg.png"
              alt="Clipsy Logo"
              className="app-logo"
            />
            <div className={`connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`}></div>
          </div>
          <h1 className="title">Clipsy</h1>
        </div>
        <div className="header-actions">
          <button
            className={`icon-button ${isFloatingOverlayVisible ? 'active' : ''}`}
            onClick={toggleFloatingOverlay}
            title="Toggle floating clipboard widget"
          >
            📋
          </button>
          <button
            className={`icon-button ${isAlwaysOnTop ? 'active' : ''}`}
            onClick={toggleAlwaysOnTop}
            title="Pin to top"
          >
            <div className="pin-icon">
              <div className="pin-head"></div>
              <div className="pin-body"></div>
            </div>
          </button>
          <button className="icon-button" onClick={minimizeToTray} title="Minimize">
            ➖
          </button>
          <button className="icon-button" onClick={() => setShowSettings(true)} title="Settings">
            ⚙️
          </button>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="success-message">
          <span>{successMessage}</span>
        </div>
      )}

      {/* Main Content */}
      <div className="main-content">
        {/* This Device Section */}
        <div className="device-section">
          <div className="device-section-header">
            <div className="device-section-title-container">
              <h2 className="device-section-title">💻 This Device</h2>
              <p className="device-section-subtitle">Windows PC - Office</p>
            </div>
          </div>

          {isEditingThisDevice ? (
            <div className="edit-container">
              <textarea
                className="edit-text-input"
                value={editingThisDeviceText}
                onChange={(e) => setEditingThisDeviceText(e.target.value)}
                placeholder="Edit this device clipboard content..."
                autoFocus
              />
              <div className="edit-actions">
                <button className="save-button this-device" onClick={saveThisDeviceEdit}>
                  Save
                </button>
                <button className="cancel-button this-device" onClick={cancelThisDeviceEdit}>
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="device-clipboard">
              <div
                className="clipboard-content this-device-content"
                onClick={() => copyToClipboard(thisDeviceClipboard)}
              >
                <p className="clipboard-text">{thisDeviceClipboard}</p>
                <p className="clipboard-meta">Click to copy • Real-time sync</p>
                <button className="edit-button-inside" onClick={startEditingThisDevice}>
                  ✎
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Connected Device Section */}
        <div className="device-section">
          <div className="device-section-header">
            <h2 className="device-section-title">🔗 Connected Device</h2>
            <p className="device-section-subtitle">Android Phone - Personal 🟢</p>
          </div>

          {isEditingConnectedDevice ? (
            <div className="edit-container">
              <textarea
                className="edit-text-input"
                value={editingConnectedDeviceText}
                onChange={(e) => setEditingConnectedDeviceText(e.target.value)}
                placeholder="Edit connected device clipboard content..."
                autoFocus
              />
              <div className="edit-actions">
                <button className="save-button connected-device" onClick={saveConnectedDeviceEdit}>
                  Save
                </button>
                <button className="cancel-button connected-device" onClick={cancelConnectedDeviceEdit}>
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="device-clipboard">
              <div
                className="clipboard-content connected-device-content"
                onClick={() => copyToClipboard(connectedDeviceClipboard)}
              >
                <p className="clipboard-text">{connectedDeviceClipboard}</p>
                <p className="clipboard-meta">Click to copy • Bidirectional sync</p>
                <button className="edit-button-inside" onClick={startEditingConnectedDevice}>
                  ✎
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Clipboard History */}
        <h2 className="history-title">Clipboard History</h2>
        <div className="history-list">
          {historyItems.map((item) => (
            <div
              key={item.id}
              className="history-item"
              onClick={() => selectItem(item)}
            >
              <div className="history-item-header">
                <span className="timestamp">{item.timestamp}</span>
                <button
                  className="delete-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteHistoryItem(item.id);
                  }}
                >
                  ✕
                </button>
              </div>
              <p className="item-content">{item.content}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Floating Sync Button */}
      <button className="floating-sync-button" onClick={syncNow}>
        <img src="/sync.png" alt="Sync" className="sync-icon" />
      </button>

      {/* Settings Modal */}
      {showSettings && (
        <div className="modal-overlay" onClick={() => setShowSettings(false)}>
          <div className="settings-sidebar" onClick={(e) => e.stopPropagation()}>
            <div className="settings-header">
              <h2 className="settings-title">Settings</h2>
              <button className="close-button" onClick={() => setShowSettings(false)}>
                ✕
              </button>
            </div>

            <div className="settings-content">
              {/* Device Info */}
              <div className="settings-section">
                <h3 className="section-title">Device Info</h3>
                <div className="device-info-card">
                  <p className="device-name">Windows PC - Office</p>
                  <p className="device-detail">IP: *************</p>
                  <div className="device-status-row">
                    <div className={`device-status-indicator ${connectionStatus}`}></div>
                    <span className={`device-status-text ${connectionStatus}`}>
                      Status: {connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Paired Devices */}
              <div className="settings-section">
                <h3 className="section-title">Paired Devices</h3>
                {pairedDevices.map((device) => (
                  <div key={device.id} className="device-card">
                    <div className="device-info">
                      <p className="device-card-name">{device.name}</p>
                      <p className="device-card-type">{device.type}</p>
                      <div className="device-status-row">
                        <div className={`device-status-indicator ${device.status}`}></div>
                        <span className={`device-status-text ${device.status}`}>
                          {device.status} • {device.lastSeen}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;