import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  // State management matching Android app
  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to <PERSON>lipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');
  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');
  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);
  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);
  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');
  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');

  const [historyItems, setHistoryItems] = useState([
    { id: '1', content: 'This is an older clipboard item. It\'s shorter.', timestamp: '2 minutes ago' },
    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },
    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },
    { id: '4', content: 'Some code snippet: function hello() { console.log("Hello World!"); }', timestamp: '5 hours ago' }
  ]);

  // UI State
  const [showSettings, setShowSettings] = useState(false);
  const [showSyncSettings, setShowSyncSettings] = useState(false);
  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);

  // Background Sync State
  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);
  const [networkDevices, setNetworkDevices] = useState([]);
  const [isDiscovering, setIsDiscovering] = useState(false);

  // Sync Settings - matching Android app
  const [syncSettings, setSyncSettings] = useState({
    autoSync: true,
    syncDelay: 2,
    syncOnConnect: true,
    bidirectional: true
  });

  // Device Management - matching Android app
  const [pairedDevices, setPairedDevices] = useState([
    {
      id: 'android-1',
      name: 'Android Phone - Personal',
      type: 'Android 14',
      status: 'connected',
      lastSeen: '2 min ago',
      ipAddress: '*************'
    },
    {
      id: 'linux-1',
      name: 'Ubuntu Server - Home',
      type: 'Ubuntu 22.04',
      status: 'disconnected',
      lastSeen: '1 hour ago',
      ipAddress: '*************'
    }
  ]);

  const [discoveredDevices] = useState([
    {
      id: 'johns-laptop',
      name: 'John\'s Laptop',
      type: 'Windows 10',
      status: 'discovering',
      lastSeen: 'Available for pairing',
      ipAddress: '*************'
    },
    {
      id: 'sarahs-desktop',
      name: 'Sarah\'s Desktop',
      type: 'Ubuntu 22.04',
      status: 'discovering',
      lastSeen: 'Available for pairing',
      ipAddress: '*************'
    }
  ]);

  // Device info state - get actual Windows device details
  const [deviceInfo, setDeviceInfo] = useState({
    name: 'Windows PC - Loading...',
    type: 'Windows',
    status: 'active',
    lastSeen: 'now',
    ipAddress: '*************'
  });

  // Get actual device name and OS details
  React.useEffect(() => {
    const getDeviceInfo = async () => {
      try {
        // Get device name from various sources
        let deviceName = 'Windows PC';
        let osVersion = 'Windows';

        // Try to get computer name from environment or navigator
        if (navigator.userAgentData) {
          const platform = navigator.userAgentData.platform;
          osVersion = platform || 'Windows';
        } else if (navigator.userAgent) {
          // Parse user agent for Windows version
          const windowsMatch = navigator.userAgent.match(/Windows NT (\d+\.\d+)/);
          if (windowsMatch) {
            const version = windowsMatch[1];
            switch (version) {
              case '10.0': osVersion = 'Windows 10/11'; break;
              case '6.3': osVersion = 'Windows 8.1'; break;
              case '6.2': osVersion = 'Windows 8'; break;
              case '6.1': osVersion = 'Windows 7'; break;
              default: osVersion = `Windows NT ${version}`;
            }
          }
        }

        // Try to get hostname if available
        try {
          if (window.location.hostname && window.location.hostname !== 'localhost') {
            deviceName = `${window.location.hostname} - Windows PC`;
          } else {
            // Use a more descriptive name based on OS
            deviceName = `${osVersion} PC - Main`;
          }
        } catch (e) {
          deviceName = `${osVersion} PC - Main`;
        }

        // Check if any devices are connected
        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');
        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';

        setDeviceInfo(prev => ({
          ...prev,
          name: deviceName,
          type: osVersion,
          status: deviceStatus,
          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'
        }));
      } catch (error) {
        console.log('Could not get device info:', error);
        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');
        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';

        setDeviceInfo(prev => ({
          ...prev,
          name: 'Windows PC - Main',
          type: 'Windows',
          status: deviceStatus,
          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'
        }));
      }
    };

    getDeviceInfo();
  }, [pairedDevices]); // Changed dependency from connectionStatus to pairedDevices

  // Functions
  const showMessage = (text) => {
    setSuccessMessage(text);
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  const copyToClipboard = async (content) => {
    try {
      await navigator.clipboard.writeText(content);
      setThisDeviceClipboard(content);
      showMessage('✅ Text copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      showMessage('❌ Failed to copy to clipboard');
    }
  };

  const selectItem = (item) => {
    copyToClipboard(item.content);
    setConnectedDeviceClipboard(item.content);
  };

  const deleteHistoryItem = (itemId) => {
    const updatedHistory = historyItems.filter(item => item.id !== itemId);
    setHistoryItems(updatedHistory);
    showMessage('🗑️ History item deleted!');
  };

  // Device edit functions
  const startEditingThisDevice = () => {
    setEditingThisDeviceText(thisDeviceClipboard);
    setIsEditingThisDevice(true);
  };

  const saveThisDeviceEdit = async () => {
    const newContent = editingThisDeviceText.trim();
    if (!newContent) {
      showMessage('Content cannot be empty');
      return;
    }

    setThisDeviceClipboard(newContent);
    try {
      await navigator.clipboard.writeText(newContent);
    } catch (error) {
      console.warn('Failed to update clipboard:', error);
    }

    setIsEditingThisDevice(false);
    setEditingThisDeviceText('');
    showMessage('✅ This Device clipboard updated!');
  };

  const cancelThisDeviceEdit = () => {
    setIsEditingThisDevice(false);
    setEditingThisDeviceText('');
  };

  const startEditingConnectedDevice = () => {
    setEditingConnectedDeviceText(connectedDeviceClipboard);
    setIsEditingConnectedDevice(true);
  };

  const saveConnectedDeviceEdit = () => {
    const newContent = editingConnectedDeviceText.trim();
    if (!newContent) {
      showMessage('Content cannot be empty');
      return;
    }

    setConnectedDeviceClipboard(newContent);
    setIsEditingConnectedDevice(false);
    setEditingConnectedDeviceText('');
    showMessage('✅ Connected Device clipboard updated!');
  };

  const cancelConnectedDeviceEdit = () => {
    setIsEditingConnectedDevice(false);
    setEditingConnectedDeviceText('');
  };

  const syncNow = () => {
    showMessage('🔄 Syncing with paired devices...');
    setTimeout(() => {
      showMessage('✅ Sync completed!');
    }, 1000);
  };

  const toggleAlwaysOnTop = () => {
    setIsAlwaysOnTop(!isAlwaysOnTop);
    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');
  };

  const minimizeToTray = () => {
    showMessage('➖ Minimizing to background...');
  };

  const toggleFloatingOverlay = () => {
    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);
    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');
  };

  // Device Management Functions
  const removeDevice = (deviceId) => {
    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);
    setPairedDevices(updatedDevices);
    showMessage('🗑️ Device removed from paired devices');
  };

  const connectDevice = (deviceId) => {
    const updatedDevices = pairedDevices.map(device =>
      device.id === deviceId ? { ...device, status: 'connected', lastSeen: 'Just now' } : device
    );
    setPairedDevices(updatedDevices);
    showMessage('✅ Device connected successfully');
  };

  const disconnectDevice = (deviceId) => {
    const updatedDevices = pairedDevices.map(device =>
      device.id === deviceId ? { ...device, status: 'disconnected', lastSeen: 'Just now' } : device
    );
    setPairedDevices(updatedDevices);
    showMessage('🔌 Device disconnected');
  };

  const pairDevice = (deviceId) => {
    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);
    if (deviceToPair) {
      const newPairedDevice = {
        ...deviceToPair,
        status: 'connected',
        lastSeen: 'Just now'
      };
      setPairedDevices([...pairedDevices, newPairedDevice]);
      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);
    }
  };

  const refreshDiscovery = () => {
    if (isDiscovering) return;

    setIsDiscovering(true);
    showMessage('🔍 Scanning for devices...');

    // Simulate discovery process
    setTimeout(() => {
      setNetworkDevices(discoveredDevices);
      setIsDiscovering(false);
      showMessage(`📱 Found ${discoveredDevices.length} available devices`);
    }, 2000);
  };

  const scanQRCode = () => {
    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');
  };

  const generateQRCode = () => {
    // Generate connection info for QR code
    const connectionInfo = {
      deviceName: deviceInfo.name,
      deviceType: deviceInfo.type,
      ipAddress: deviceInfo.ipAddress,
      port: 3001,
      protocol: 'clipsy-sync',
      timestamp: Date.now()
    };

    const qrData = JSON.stringify(connectionInfo);
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrData)}`;

    // Create and show QR code modal
    const qrModal = document.createElement('div');
    qrModal.className = 'qr-modal-overlay';
    qrModal.innerHTML = `
      <div class="qr-modal-content">
        <div class="qr-modal-header">
          <h3>📱 Scan to Connect Android Device</h3>
          <button class="qr-modal-close">✕</button>
        </div>
        <div class="qr-modal-body">
          <img src="${qrCodeUrl}" alt="QR Code" class="qr-code-image" />
          <p class="qr-instructions">
            1. Open Clipsy app on your Android device<br/>
            2. Go to Settings → Device Discovery<br/>
            3. Tap "Scan QR" and scan this code<br/>
            4. Your devices will be paired automatically
          </p>
          <div class="qr-device-info">
            <p><strong>Device:</strong> ${deviceInfo.name}</p>
            <p><strong>IP:</strong> ${deviceInfo.ipAddress}</p>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(qrModal);

    // Close modal functionality
    const closeModal = () => {
      document.body.removeChild(qrModal);
    };

    qrModal.querySelector('.qr-modal-close').onclick = closeModal;
    qrModal.onclick = (e) => {
      if (e.target === qrModal) closeModal();
    };

    showMessage('📱 QR Code generated! Scan with Android Clipsy app to connect.');
  };

  return (
    <div className="app-container">
      {/* Header */}
      <div className="header">
        <div className="title-container">
          <div className="logo-container">
            <img
              src="/clipsy-logo-no-bg.png"
              alt="Clipsy Logo"
              className="app-logo"
            />
            <div className={`connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`}></div>
          </div>
          <h1 className="title">Clipsy</h1>
        </div>
        <div className="header-actions">
          <button
            className={`icon-button ${isFloatingOverlayVisible ? 'active' : ''}`}
            onClick={toggleFloatingOverlay}
            title="Toggle floating clipboard widget"
          >
            📋
          </button>
          <button
            className={`icon-button ${isAlwaysOnTop ? 'active' : ''}`}
            onClick={toggleAlwaysOnTop}
            title="Pin to top"
          >
            <div className="pin-icon">
              <div className="pin-head"></div>
              <div className="pin-body"></div>
            </div>
          </button>
          <button className="icon-button" onClick={minimizeToTray} title="Minimize">
            ➖
          </button>
          <button className="icon-button" onClick={() => setShowSettings(true)} title="Settings">
            ⚙️
          </button>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="success-message">
          <span>{successMessage}</span>
        </div>
      )}

      {/* Main Content */}
      <div className="main-content">
        {/* This Device Section */}
        <div className="device-section">
          <div className="device-section-header">
            <div className="device-section-title-container">
              <h2 className="device-section-title">💻 This Device</h2>
              <p className="device-section-subtitle">{deviceInfo.name}</p>
            </div>
          </div>

          {isEditingThisDevice ? (
            <div className="edit-container">
              <textarea
                className="edit-text-input"
                value={editingThisDeviceText}
                onChange={(e) => setEditingThisDeviceText(e.target.value)}
                placeholder="Edit this device clipboard content..."
                autoFocus
              />
              <div className="edit-actions">
                <button className="save-button this-device" onClick={saveThisDeviceEdit}>
                  Save
                </button>
                <button className="cancel-button this-device" onClick={cancelThisDeviceEdit}>
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="device-clipboard">
              <div
                className="clipboard-content this-device-content"
                onClick={() => copyToClipboard(thisDeviceClipboard)}
              >
                <p className="clipboard-text">{thisDeviceClipboard}</p>
                <p className="clipboard-meta">Click to copy • Real-time sync</p>
                <button className="edit-button-inside" onClick={startEditingThisDevice}>
                  ✎
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Connected Device Section */}
        <div className="device-section">
          <div className="device-section-header">
            <h2 className="device-section-title">🔗 Connected Device</h2>
            <p className="device-section-subtitle">Android Phone - Personal 🟢</p>
          </div>

          {isEditingConnectedDevice ? (
            <div className="edit-container">
              <textarea
                className="edit-text-input"
                value={editingConnectedDeviceText}
                onChange={(e) => setEditingConnectedDeviceText(e.target.value)}
                placeholder="Edit connected device clipboard content..."
                autoFocus
              />
              <div className="edit-actions">
                <button className="save-button connected-device" onClick={saveConnectedDeviceEdit}>
                  Save
                </button>
                <button className="cancel-button connected-device" onClick={cancelConnectedDeviceEdit}>
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="device-clipboard">
              <div
                className="clipboard-content connected-device-content"
                onClick={() => copyToClipboard(connectedDeviceClipboard)}
              >
                <p className="clipboard-text">{connectedDeviceClipboard}</p>
                <p className="clipboard-meta">Click to copy • Bidirectional sync</p>
                <button className="edit-button-inside" onClick={startEditingConnectedDevice}>
                  ✎
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Clipboard History */}
        <h2 className="history-title">Clipboard History</h2>
        <div className="history-list">
          {historyItems.map((item) => (
            <div
              key={item.id}
              className="history-item"
              onClick={() => selectItem(item)}
            >
              <div className="history-item-header">
                <span className="timestamp">{item.timestamp}</span>
                <button
                  className="delete-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteHistoryItem(item.id);
                  }}
                >
                  ✕
                </button>
              </div>
              <p className="item-content">{item.content}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Floating Sync Button */}
      <button className="floating-sync-button" onClick={syncNow}>
        <img src="/sync.png" alt="Sync" className="sync-icon" />
      </button>

      {/* Settings Modal */}
      {showSettings && (
        <div className="modal-overlay" onClick={() => setShowSettings(false)}>
          <div className="settings-sidebar" onClick={(e) => e.stopPropagation()}>
            <div className="settings-header">
              <h2 className="settings-title">Settings</h2>
              <button className="close-button" onClick={() => setShowSettings(false)}>
                ✕
              </button>
            </div>

            <div className="settings-content">
              {/* Device Info */}
              <div className="settings-section">
                <h3 className="section-title">Device Info</h3>
                <div className="device-info-card">
                  <p className="device-name">{deviceInfo.name}</p>
                  <p className="device-detail">{deviceInfo.type}</p>
                  <p className="device-detail">IP: {deviceInfo.ipAddress}</p>
                  <div className="device-status-row">
                    <div className={`device-status-indicator ${deviceInfo.status}`}></div>
                    <span className={`device-status-text ${deviceInfo.status}`}>
                      Status: {deviceInfo.status === 'active' ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                  {deviceInfo.status === 'disconnected' && (
                    <p className="device-detail disconnected-notice">
                      ⚠️ No devices connected - Use QR code or device discovery to connect Android devices
                    </p>
                  )}
                </div>
              </div>

              {/* Paired Devices */}
              <div className="settings-section">
                <h3 className="section-title">Paired Devices</h3>
                {pairedDevices.length === 0 ? (
                  <div className="empty-device-list">
                    <p className="empty-device-text">No paired devices found</p>
                    <p className="empty-device-subtext">Use QR code or device discovery to pair devices</p>
                  </div>
                ) : (
                  pairedDevices.map((device) => (
                    <div key={device.id} className="enhanced-device-card">
                      {/* Remove Button - White X at top right corner */}
                      <button
                        className="remove-button-top-right"
                        onClick={() => removeDevice(device.id)}
                      >
                        ×
                      </button>

                      <div className="device-info">
                        <p className="device-card-name">{device.name}</p>
                        <p className="device-card-type">{device.type}</p>
                        <div className="device-status-row">
                          <div className={`device-status-indicator ${device.status}`}></div>
                          <span className={`device-status-text ${device.status}`}>
                            {device.status === 'connected' ? 'Connected' : 'Disconnected'} • {device.lastSeen}
                          </span>
                        </div>
                      </div>

                      {/* Connect/Disconnect Button - Bottom Right Corner */}
                      <button
                        className={`device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`}
                        onClick={() => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id)}
                      >
                        {device.status === 'connected' ? 'Disconnect' : 'Connect'}
                      </button>
                    </div>
                  ))
                )}
              </div>

              {/* Device Discovery */}
              <div className="settings-section">
                <div className="section-header">
                  <h3 className="section-title">Device Discovery</h3>
                  <button
                    className={`discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`}
                    onClick={refreshDiscovery}
                    disabled={isDiscovering}
                  >
                    {isDiscovering ? '🔍 Scanning...' : 'Discover'}
                  </button>
                </div>
                {networkDevices.length === 0 ? (
                  <div className="empty-device-list">
                    <p className="empty-device-text">No devices found</p>
                    <p className="empty-device-subtext">
                      {isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'}
                    </p>
                  </div>
                ) : (
                  networkDevices.map((device) => (
                    <div key={device.id} className="discovered-device-card">
                      <div className="device-info">
                        <p className="device-card-name">{device.name}</p>
                        <p className="device-card-type">{device.type}</p>
                        <p className="device-card-last-seen">{device.lastSeen}</p>
                      </div>
                      <button
                        className="pair-button"
                        onClick={() => pairDevice(device.id)}
                      >
                        Pair
                      </button>
                    </div>
                  ))
                )}

                <div className="qr-buttons-container">
                  <button className="qr-generate-button" onClick={generateQRCode}>
                    Generate QR
                  </button>
                </div>
              </div>

              {/* Additional Settings */}
              <div className="settings-section">
                <h3 className="section-title">Additional Settings</h3>
                <button
                  className="setting-item"
                  onClick={() => setShowSyncSettings(true)}
                >
                  <span className="setting-item-text">Sync Settings</span>
                  <span className="setting-item-arrow">›</span>
                </button>

                {/* Background Sync Setting */}
                <button
                  className="setting-item"
                  onClick={() => {
                    const newValue = !backgroundSyncEnabled;
                    setBackgroundSyncEnabled(newValue);
                    showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');
                  }}
                >
                  <span className="setting-item-text">Background Sync</span>
                  <span className="setting-item-value">
                    {backgroundSyncEnabled ? 'on' : 'off'}
                  </span>
                </button>

                {/* Network Discovery Setting */}
                <button
                  className="setting-item"
                  onClick={() => {
                    if (isDiscovering) {
                      setIsDiscovering(false);
                      showMessage('Network discovery stopped');
                    } else {
                      setIsDiscovering(true);
                      showMessage('Network discovery started');
                    }
                  }}
                >
                  <span className="setting-item-text">Network Discovery</span>
                  <span className="setting-item-value">
                    {isDiscovering ? 'on' : 'off'}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sync Settings Modal */}
      {showSyncSettings && (
        <div className="modal-overlay" onClick={() => setShowSyncSettings(false)}>
          <div className="sync-settings-modal" onClick={(e) => e.stopPropagation()}>
            <div className="settings-header">
              <h2 className="settings-title">Sync Settings</h2>
              <button className="close-button" onClick={() => setShowSyncSettings(false)}>
                ✕
              </button>
            </div>

            <div className="settings-content">
              <div className="settings-section">
                <div className="sync-setting-item">
                  <label className="sync-setting-label">Auto Sync</label>
                  <input
                    type="checkbox"
                    className="sync-setting-checkbox"
                    checked={syncSettings.autoSync}
                    onChange={(e) => setSyncSettings({...syncSettings, autoSync: e.target.checked})}
                  />
                </div>

                <div className="sync-setting-item">
                  <label className="sync-setting-label">Sync Delay: {syncSettings.syncDelay} seconds</label>
                  <p className="sync-setting-description">
                    {syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`}
                  </p>
                  <div className="sync-delay-controls">
                    <button
                      className="sync-delay-button"
                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.max(0, syncSettings.syncDelay - 1)})}
                    >
                      -
                    </button>
                    <span className="sync-delay-value">{syncSettings.syncDelay}s</span>
                    <button
                      className="sync-delay-button"
                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.min(30, syncSettings.syncDelay + 1)})}
                    >
                      +
                    </button>
                  </div>
                </div>

                <div className="sync-setting-item">
                  <label className="sync-setting-label">Sync on Connect</label>
                  <input
                    type="checkbox"
                    className="sync-setting-checkbox"
                    checked={syncSettings.syncOnConnect}
                    onChange={(e) => setSyncSettings({...syncSettings, syncOnConnect: e.target.checked})}
                  />
                </div>

                <div className="sync-setting-item">
                  <label className="sync-setting-label">Bidirectional Sync</label>
                  <input
                    type="checkbox"
                    className="sync-setting-checkbox"
                    checked={syncSettings.bidirectional}
                    onChange={(e) => setSyncSettings({...syncSettings, bidirectional: e.target.checked})}
                  />
                </div>

                {/* Cross-Platform Sync Controls */}
                <div className="sync-setting-item">
                  <button
                    className="cross-platform-sync-button"
                    onClick={() => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.')}
                  >
                    📱 Windows ↔ Android Sync
                  </button>
                </div>

                <div className="sync-setting-item">
                  <button
                    className="cross-platform-sync-button"
                    onClick={() => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.')}
                  >
                    🖥️ Windows ↔ Windows Sync
                  </button>
                </div>

                <div className="sync-setting-item">
                  <button
                    className="cross-platform-sync-button"
                    onClick={() => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.')}
                  >
                    🚀 Sync All Devices
                  </button>
                </div>

                {/* Floating Overlay Button Settings */}
                <div className="settings-section">
                  <h3 className="settings-section-title">📋 Floating Overlay Button Settings</h3>
                  <p className="settings-description">
                    Configure the floating overlay button for quick access to connected device clipboards
                  </p>

                  <div className="settings-row">
                    <span className="settings-label">Enable Floating Overlay Button</span>
                    <button
                      className="settings-toggle active"
                      onClick={() => showMessage('📋 Floating overlay button is always enabled for accessibility')}
                    >
                      <div className="settings-toggle-thumb active"></div>
                    </button>
                  </div>

                  <div className="settings-row">
                    <span className="settings-label">Show Device Count Badge</span>
                    <button
                      className="settings-toggle active"
                      onClick={() => showMessage('🔢 Device count badge enabled')}
                    >
                      <div className="settings-toggle-thumb active"></div>
                    </button>
                  </div>

                  <div className="settings-row">
                    <span className="settings-label">Auto-hide After Copy</span>
                    <button
                      className="settings-toggle active"
                      onClick={() => showMessage('⏱️ Auto-hide after copy enabled')}
                    >
                      <div className="settings-toggle-thumb active"></div>
                    </button>
                  </div>

                  <p className="settings-note">
                    💡 The floating overlay button (📋) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;