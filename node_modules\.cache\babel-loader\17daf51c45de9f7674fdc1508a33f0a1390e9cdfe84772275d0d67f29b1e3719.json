{"ast": null, "code": "var _jsxFileName = \"D:\\\\new git\\\\Clipsy-Windows\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  // State management matching Android app\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to Clipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\n  const [historyItems, setHistoryItems] = useState([{\n    id: '1',\n    content: 'This is an older clipboard item. It\\'s shorter.',\n    timestamp: '2 minutes ago'\n  }, {\n    id: '2',\n    content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.',\n    timestamp: '10 minutes ago'\n  }, {\n    id: '3',\n    content: 'Yet another historical entry.',\n    timestamp: '1 hour ago'\n  }, {\n    id: '4',\n    content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }',\n    timestamp: '5 hours ago'\n  }]);\n\n  // UI State\n  const [showSettings, setShowSettings] = useState(false);\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [connectionStatus, setConnectionStatus] = useState('connected');\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\n\n  // Background Sync State\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\n  const [networkDevices, setNetworkDevices] = useState([]);\n  const [isDiscovering, setIsDiscovering] = useState(false);\n\n  // Sync Settings - matching Android app\n  const [syncSettings, setSyncSettings] = useState({\n    autoSync: true,\n    syncDelay: 2,\n    syncOnConnect: true,\n    bidirectional: true\n  });\n\n  // Paired devices state\n  const [pairedDevices] = useState([{\n    id: 'android-1',\n    name: 'Android Phone - Personal',\n    type: 'Android 14',\n    status: 'connected',\n    lastSeen: '2 min ago',\n    ipAddress: '*************'\n  }, {\n    id: 'linux-1',\n    name: 'Ubuntu Server - Home',\n    type: 'Ubuntu 22.04',\n    status: 'disconnected',\n    lastSeen: '1 hour ago',\n    ipAddress: '*************'\n  }]);\n\n  // Functions\n  const showMessage = text => {\n    setSuccessMessage(text);\n    setTimeout(() => setSuccessMessage(''), 3000);\n  };\n  const copyToClipboard = async content => {\n    try {\n      await navigator.clipboard.writeText(content);\n      setThisDeviceClipboard(content);\n      showMessage('✅ Text copied to clipboard!');\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error);\n      showMessage('❌ Failed to copy to clipboard');\n    }\n  };\n  const selectItem = item => {\n    copyToClipboard(item.content);\n    setConnectedDeviceClipboard(item.content);\n  };\n  const deleteHistoryItem = itemId => {\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\n    setHistoryItems(updatedHistory);\n    showMessage('🗑️ History item deleted!');\n  };\n\n  // Device edit functions\n  const startEditingThisDevice = () => {\n    setEditingThisDeviceText(thisDeviceClipboard);\n    setIsEditingThisDevice(true);\n  };\n  const saveThisDeviceEdit = async () => {\n    const newContent = editingThisDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setThisDeviceClipboard(newContent);\n    try {\n      await navigator.clipboard.writeText(newContent);\n    } catch (error) {\n      console.warn('Failed to update clipboard:', error);\n    }\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n    showMessage('✅ This Device clipboard updated!');\n  };\n  const cancelThisDeviceEdit = () => {\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n  };\n  const startEditingConnectedDevice = () => {\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\n    setIsEditingConnectedDevice(true);\n  };\n  const saveConnectedDeviceEdit = () => {\n    const newContent = editingConnectedDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setConnectedDeviceClipboard(newContent);\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n    showMessage('✅ Connected Device clipboard updated!');\n  };\n  const cancelConnectedDeviceEdit = () => {\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n  };\n  const syncNow = () => {\n    showMessage('🔄 Syncing with paired devices...');\n    setTimeout(() => {\n      showMessage('✅ Sync completed!');\n    }, 1000);\n  };\n  const toggleAlwaysOnTop = () => {\n    setIsAlwaysOnTop(!isAlwaysOnTop);\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\n  };\n  const minimizeToTray = () => {\n    showMessage('➖ Minimizing to background...');\n  };\n  const toggleFloatingOverlay = () => {\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"title-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/clipsy-logo-no-bg.png\",\n            alt: \"Clipsy Logo\",\n            className: \"app-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: \"Clipsy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isFloatingOverlayVisible ? 'active' : ''}`,\n          onClick: toggleFloatingOverlay,\n          title: \"Toggle floating clipboard widget\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isAlwaysOnTop ? 'active' : ''}`,\n          onClick: toggleAlwaysOnTop,\n          title: \"Pin to top\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pin-icon\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-head\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-body\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: minimizeToTray,\n          title: \"Minimize\",\n          children: \"\\u2796\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: () => setShowSettings(true),\n          title: \"Settings\",\n          children: \"\\u2699\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"device-section-title-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"device-section-title\",\n              children: \"\\uD83D\\uDCBB This Device\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"device-section-subtitle\",\n              children: \"Windows PC - Office\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), isEditingThisDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingThisDeviceText,\n            onChange: e => setEditingThisDeviceText(e.target.value),\n            placeholder: \"Edit this device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button this-device\",\n              onClick: saveThisDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button this-device\",\n              onClick: cancelThisDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content this-device-content\",\n            onClick: () => copyToClipboard(thisDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: thisDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Real-time sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingThisDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"device-section-title\",\n            children: \"\\uD83D\\uDD17 Connected Device\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"device-section-subtitle\",\n            children: \"Android Phone - Personal \\uD83D\\uDFE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), isEditingConnectedDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingConnectedDeviceText,\n            onChange: e => setEditingConnectedDeviceText(e.target.value),\n            placeholder: \"Edit connected device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button connected-device\",\n              onClick: saveConnectedDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button connected-device\",\n              onClick: cancelConnectedDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content connected-device-content\",\n            onClick: () => copyToClipboard(connectedDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: connectedDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Bidirectional sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingConnectedDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"history-title\",\n        children: \"Clipboard History\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-list\",\n        children: historyItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-item\",\n          onClick: () => selectItem(item),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-item-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"timestamp\",\n              children: item.timestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"delete-button\",\n              onClick: e => {\n                e.stopPropagation();\n                deleteHistoryItem(item.id);\n              },\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"item-content\",\n            children: item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"floating-sync-button\",\n      onClick: syncNow,\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/sync.png\",\n        alt: \"Sync\",\n        className: \"sync-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowSettings(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-sidebar\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-title\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: () => setShowSettings(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Device Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"device-info-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-name\",\n                children: \"Windows PC - Office\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail\",\n                children: \"IP: *************\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-status-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `device-status-indicator ${connectionStatus}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `device-status-text ${connectionStatus}`,\n                  children: [\"Status: \", connectionStatus === 'connected' ? 'Connected' : 'Disconnected']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Paired Devices\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this), pairedDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"device-card\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"device-status-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `device-status-indicator ${device.status}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `device-status-text ${device.status}`,\n                    children: [device.status, \" \\u2022 \", device.lastSeen]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this)\n            }, device.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"pLAK3d+u0dYoalHyf3ELYVN1f+I=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "App", "_s", "thisDeviceClipboard", "setThisDeviceClipboard", "connectedDeviceClipboard", "setConnectedDeviceClipboard", "isEditingThisDevice", "setIsEditingThisDevice", "isEditingConnectedDevice", "setIsEditingConnectedDevice", "editingThisDeviceText", "setEditingThisDeviceText", "editingConnectedDeviceText", "setEditingConnectedDeviceText", "historyItems", "setHistoryItems", "id", "content", "timestamp", "showSettings", "setShowSettings", "showSyncSettings", "setShowSyncSettings", "isAlwaysOnTop", "setIsAlwaysOnTop", "successMessage", "setSuccessMessage", "connectionStatus", "setConnectionStatus", "isFloatingOverlayVisible", "setIsFloatingOverlayVisible", "backgroundSyncEnabled", "setBackgroundSyncEnabled", "networkDevices", "setNetworkDevices", "isDiscovering", "setIsDiscovering", "syncSettings", "setSyncSettings", "autoSync", "syncD<PERSON>y", "syncOnConnect", "bidirectional", "pairedDevices", "name", "type", "status", "lastSeen", "ip<PERSON><PERSON><PERSON>", "showMessage", "text", "setTimeout", "copyToClipboard", "navigator", "clipboard", "writeText", "error", "console", "selectItem", "item", "deleteHistoryItem", "itemId", "updatedHistory", "filter", "startEditingThisDevice", "saveThisDeviceEdit", "newContent", "trim", "warn", "cancelThisDeviceEdit", "startEditingConnectedDevice", "saveConnectedDeviceEdit", "cancelConnectedDeviceEdit", "syncNow", "toggleAlwaysOnTop", "minimizeToTray", "toggleFloatingOverlay", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "some", "device", "onClick", "title", "value", "onChange", "e", "target", "placeholder", "autoFocus", "map", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["D:/new git/Clipsy-Windows/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  // State management matching Android app\r\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to <PERSON>lipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\r\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\r\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\r\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\r\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\r\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\r\n\r\n  const [historyItems, setHistoryItems] = useState([\r\n    { id: '1', content: 'This is an older clipboard item. It\\'s shorter.', timestamp: '2 minutes ago' },\r\n    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },\r\n    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },\r\n    { id: '4', content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }', timestamp: '5 hours ago' }\r\n  ]);\r\n\r\n  // UI State\r\n  const [showSettings, setShowSettings] = useState(false);\r\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\r\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [connectionStatus, setConnectionStatus] = useState('connected');\r\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\r\n\r\n  // Background Sync State\r\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\r\n  const [networkDevices, setNetworkDevices] = useState([]);\r\n  const [isDiscovering, setIsDiscovering] = useState(false);\r\n\r\n  // Sync Settings - matching Android app\r\n  const [syncSettings, setSyncSettings] = useState({\r\n    autoSync: true,\r\n    syncDelay: 2,\r\n    syncOnConnect: true,\r\n    bidirectional: true\r\n  });\r\n\r\n  // Paired devices state\r\n  const [pairedDevices] = useState([\r\n    {\r\n      id: 'android-1',\r\n      name: 'Android Phone - Personal',\r\n      type: 'Android 14',\r\n      status: 'connected',\r\n      lastSeen: '2 min ago',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'linux-1',\r\n      name: 'Ubuntu Server - Home',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'disconnected',\r\n      lastSeen: '1 hour ago',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  // Functions\r\n  const showMessage = (text) => {\r\n    setSuccessMessage(text);\r\n    setTimeout(() => setSuccessMessage(''), 3000);\r\n  };\r\n\r\n  const copyToClipboard = async (content) => {\r\n    try {\r\n      await navigator.clipboard.writeText(content);\r\n      setThisDeviceClipboard(content);\r\n      showMessage('✅ Text copied to clipboard!');\r\n    } catch (error) {\r\n      console.error('Failed to copy to clipboard:', error);\r\n      showMessage('❌ Failed to copy to clipboard');\r\n    }\r\n  };\r\n\r\n  const selectItem = (item) => {\r\n    copyToClipboard(item.content);\r\n    setConnectedDeviceClipboard(item.content);\r\n  };\r\n\r\n  const deleteHistoryItem = (itemId) => {\r\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\r\n    setHistoryItems(updatedHistory);\r\n    showMessage('🗑️ History item deleted!');\r\n  };\r\n\r\n  // Device edit functions\r\n  const startEditingThisDevice = () => {\r\n    setEditingThisDeviceText(thisDeviceClipboard);\r\n    setIsEditingThisDevice(true);\r\n  };\r\n\r\n  const saveThisDeviceEdit = async () => {\r\n    const newContent = editingThisDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setThisDeviceClipboard(newContent);\r\n    try {\r\n      await navigator.clipboard.writeText(newContent);\r\n    } catch (error) {\r\n      console.warn('Failed to update clipboard:', error);\r\n    }\r\n\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n    showMessage('✅ This Device clipboard updated!');\r\n  };\r\n\r\n  const cancelThisDeviceEdit = () => {\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n  };\r\n\r\n  const startEditingConnectedDevice = () => {\r\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\r\n    setIsEditingConnectedDevice(true);\r\n  };\r\n\r\n  const saveConnectedDeviceEdit = () => {\r\n    const newContent = editingConnectedDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setConnectedDeviceClipboard(newContent);\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n    showMessage('✅ Connected Device clipboard updated!');\r\n  };\r\n\r\n  const cancelConnectedDeviceEdit = () => {\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n  };\r\n\r\n  const syncNow = () => {\r\n    showMessage('🔄 Syncing with paired devices...');\r\n    setTimeout(() => {\r\n      showMessage('✅ Sync completed!');\r\n    }, 1000);\r\n  };\r\n\r\n  const toggleAlwaysOnTop = () => {\r\n    setIsAlwaysOnTop(!isAlwaysOnTop);\r\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\r\n  };\r\n\r\n  const minimizeToTray = () => {\r\n    showMessage('➖ Minimizing to background...');\r\n  };\r\n\r\n  const toggleFloatingOverlay = () => {\r\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\r\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\r\n  };\r\n\r\n  return (\r\n    <div className=\"app-container\">\r\n      {/* Header */}\r\n      <div className=\"header\">\r\n        <div className=\"title-container\">\r\n          <div className=\"logo-container\">\r\n            <img\r\n              src=\"/clipsy-logo-no-bg.png\"\r\n              alt=\"Clipsy Logo\"\r\n              className=\"app-logo\"\r\n            />\r\n            <div className={`connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`}></div>\r\n          </div>\r\n          <h1 className=\"title\">Clipsy</h1>\r\n        </div>\r\n        <div className=\"header-actions\">\r\n          <button\r\n            className={`icon-button ${isFloatingOverlayVisible ? 'active' : ''}`}\r\n            onClick={toggleFloatingOverlay}\r\n            title=\"Toggle floating clipboard widget\"\r\n          >\r\n            📋\r\n          </button>\r\n          <button\r\n            className={`icon-button ${isAlwaysOnTop ? 'active' : ''}`}\r\n            onClick={toggleAlwaysOnTop}\r\n            title=\"Pin to top\"\r\n          >\r\n            <div className=\"pin-icon\">\r\n              <div className=\"pin-head\"></div>\r\n              <div className=\"pin-body\"></div>\r\n            </div>\r\n          </button>\r\n          <button className=\"icon-button\" onClick={minimizeToTray} title=\"Minimize\">\r\n            ➖\r\n          </button>\r\n          <button className=\"icon-button\" onClick={() => setShowSettings(true)} title=\"Settings\">\r\n            ⚙️\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"success-message\">\r\n          <span>{successMessage}</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      <div className=\"main-content\">\r\n        {/* This Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <div className=\"device-section-title-container\">\r\n              <h2 className=\"device-section-title\">💻 This Device</h2>\r\n              <p className=\"device-section-subtitle\">Windows PC - Office</p>\r\n            </div>\r\n          </div>\r\n\r\n          {isEditingThisDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingThisDeviceText}\r\n                onChange={(e) => setEditingThisDeviceText(e.target.value)}\r\n                placeholder=\"Edit this device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button this-device\" onClick={saveThisDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button this-device\" onClick={cancelThisDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content this-device-content\"\r\n                onClick={() => copyToClipboard(thisDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{thisDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Real-time sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingThisDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Connected Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <h2 className=\"device-section-title\">🔗 Connected Device</h2>\r\n            <p className=\"device-section-subtitle\">Android Phone - Personal 🟢</p>\r\n          </div>\r\n\r\n          {isEditingConnectedDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingConnectedDeviceText}\r\n                onChange={(e) => setEditingConnectedDeviceText(e.target.value)}\r\n                placeholder=\"Edit connected device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button connected-device\" onClick={saveConnectedDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button connected-device\" onClick={cancelConnectedDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content connected-device-content\"\r\n                onClick={() => copyToClipboard(connectedDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{connectedDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Bidirectional sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingConnectedDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Clipboard History */}\r\n        <h2 className=\"history-title\">Clipboard History</h2>\r\n        <div className=\"history-list\">\r\n          {historyItems.map((item) => (\r\n            <div\r\n              key={item.id}\r\n              className=\"history-item\"\r\n              onClick={() => selectItem(item)}\r\n            >\r\n              <div className=\"history-item-header\">\r\n                <span className=\"timestamp\">{item.timestamp}</span>\r\n                <button\r\n                  className=\"delete-button\"\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    deleteHistoryItem(item.id);\r\n                  }}\r\n                >\r\n                  ✕\r\n                </button>\r\n              </div>\r\n              <p className=\"item-content\">{item.content}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Floating Sync Button */}\r\n      <button className=\"floating-sync-button\" onClick={syncNow}>\r\n        <img src=\"/sync.png\" alt=\"Sync\" className=\"sync-icon\" />\r\n      </button>\r\n\r\n      {/* Settings Modal */}\r\n      {showSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSettings(false)}>\r\n          <div className=\"settings-sidebar\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              {/* Device Info */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Device Info</h3>\r\n                <div className=\"device-info-card\">\r\n                  <p className=\"device-name\">Windows PC - Office</p>\r\n                  <p className=\"device-detail\">IP: *************</p>\r\n                  <div className=\"device-status-row\">\r\n                    <div className={`device-status-indicator ${connectionStatus}`}></div>\r\n                    <span className={`device-status-text ${connectionStatus}`}>\r\n                      Status: {connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Paired Devices */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Paired Devices</h3>\r\n                {pairedDevices.map((device) => (\r\n                  <div key={device.id} className=\"device-card\">\r\n                    <div className=\"device-info\">\r\n                      <p className=\"device-card-name\">{device.name}</p>\r\n                      <p className=\"device-card-type\">{device.type}</p>\r\n                      <div className=\"device-status-row\">\r\n                        <div className={`device-status-indicator ${device.status}`}></div>\r\n                        <span className={`device-status-text ${device.status}`}>\r\n                          {device.status} • {device.lastSeen}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGP,QAAQ,CAAC,sIAAsI,CAAC;EACtM,MAAM,CAACQ,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGT,QAAQ,CAAC,0IAA0I,CAAC;EACpN,MAAM,CAACU,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACY,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACc,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACgB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEhF,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAC/C;IAAEoB,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,iDAAiD;IAAEC,SAAS,EAAE;EAAgB,CAAC,EACnG;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,gLAAgL;IAAEC,SAAS,EAAE;EAAiB,CAAC,EACnO;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,+BAA+B;IAAEC,SAAS,EAAE;EAAa,CAAC,EAC9E;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,sEAAsE;IAAEC,SAAS,EAAE;EAAc,CAAC,CACvH,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,WAAW,CAAC;EACrE,MAAM,CAACiC,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;;EAE/E;EACA,MAAM,CAACmC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC;IAC/C2C,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,CAAC,GAAG/C,QAAQ,CAAC,CAC/B;IACEoB,EAAE,EAAE,WAAW;IACf4B,IAAI,EAAE,0BAA0B;IAChCC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE;EACb,CAAC,EACD;IACEhC,EAAE,EAAE,SAAS;IACb4B,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,cAAc;IACtBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAIC,IAAI,IAAK;IAC5BxB,iBAAiB,CAACwB,IAAI,CAAC;IACvBC,UAAU,CAAC,MAAMzB,iBAAiB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;EAED,MAAM0B,eAAe,GAAG,MAAOnC,OAAO,IAAK;IACzC,IAAI;MACF,MAAMoC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACtC,OAAO,CAAC;MAC5Cd,sBAAsB,CAACc,OAAO,CAAC;MAC/BgC,WAAW,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDP,WAAW,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMS,UAAU,GAAIC,IAAI,IAAK;IAC3BP,eAAe,CAACO,IAAI,CAAC1C,OAAO,CAAC;IAC7BZ,2BAA2B,CAACsD,IAAI,CAAC1C,OAAO,CAAC;EAC3C,CAAC;EAED,MAAM2C,iBAAiB,GAAIC,MAAM,IAAK;IACpC,MAAMC,cAAc,GAAGhD,YAAY,CAACiD,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAAC3C,EAAE,KAAK6C,MAAM,CAAC;IACtE9C,eAAe,CAAC+C,cAAc,CAAC;IAC/Bb,WAAW,CAAC,2BAA2B,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMe,sBAAsB,GAAGA,CAAA,KAAM;IACnCrD,wBAAwB,CAACT,mBAAmB,CAAC;IAC7CK,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM0D,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,UAAU,GAAGxD,qBAAqB,CAACyD,IAAI,CAAC,CAAC;IAC/C,IAAI,CAACD,UAAU,EAAE;MACfjB,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEA9C,sBAAsB,CAAC+D,UAAU,CAAC;IAClC,IAAI;MACF,MAAMb,SAAS,CAACC,SAAS,CAACC,SAAS,CAACW,UAAU,CAAC;IACjD,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACW,IAAI,CAAC,6BAA6B,EAAEZ,KAAK,CAAC;IACpD;IAEAjD,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;IAC5BsC,WAAW,CAAC,kCAAkC,CAAC;EACjD,CAAC;EAED,MAAMoB,oBAAoB,GAAGA,CAAA,KAAM;IACjC9D,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;EAC9B,CAAC;EAED,MAAM2D,2BAA2B,GAAGA,CAAA,KAAM;IACxCzD,6BAA6B,CAACT,wBAAwB,CAAC;IACvDK,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAM8D,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAML,UAAU,GAAGtD,0BAA0B,CAACuD,IAAI,CAAC,CAAC;IACpD,IAAI,CAACD,UAAU,EAAE;MACfjB,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEA5C,2BAA2B,CAAC6D,UAAU,CAAC;IACvCzD,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;IACjCoC,WAAW,CAAC,uCAAuC,CAAC;EACtD,CAAC;EAED,MAAMuB,yBAAyB,GAAGA,CAAA,KAAM;IACtC/D,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;EACnC,CAAC;EAED,MAAM4D,OAAO,GAAGA,CAAA,KAAM;IACpBxB,WAAW,CAAC,mCAAmC,CAAC;IAChDE,UAAU,CAAC,MAAM;MACfF,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMyB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BlD,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChC0B,WAAW,CAAC1B,aAAa,GAAG,0BAA0B,GAAG,sBAAsB,CAAC;EAClF,CAAC;EAED,MAAMoD,cAAc,GAAGA,CAAA,KAAM;IAC3B1B,WAAW,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAM2B,qBAAqB,GAAGA,CAAA,KAAM;IAClC9C,2BAA2B,CAAC,CAACD,wBAAwB,CAAC;IACtDoB,WAAW,CAACpB,wBAAwB,GAAG,2BAA2B,GAAG,0BAA0B,CAAC;EAClG,CAAC;EAED,oBACE9B,OAAA;IAAK8E,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5B/E,OAAA;MAAK8E,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACrB/E,OAAA;QAAK8E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B/E,OAAA;UAAK8E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/E,OAAA;YACEgF,GAAG,EAAC,wBAAwB;YAC5BC,GAAG,EAAC,aAAa;YACjBH,SAAS,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACFrF,OAAA;YAAK8E,SAAS,EAAE,kBAAkBlC,aAAa,CAAC0C,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACxC,MAAM,KAAK,WAAW,CAAC,GAAG,WAAW,GAAG,cAAc;UAAG;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnI,CAAC,eACNrF,OAAA;UAAI8E,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACNrF,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/E,OAAA;UACE8E,SAAS,EAAE,eAAehD,wBAAwB,GAAG,QAAQ,GAAG,EAAE,EAAG;UACrE0D,OAAO,EAAEX,qBAAsB;UAC/BY,KAAK,EAAC,kCAAkC;UAAAV,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrF,OAAA;UACE8E,SAAS,EAAE,eAAetD,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC1DgE,OAAO,EAAEb,iBAAkB;UAC3Bc,KAAK,EAAC,YAAY;UAAAV,QAAA,eAElB/E,OAAA;YAAK8E,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB/E,OAAA;cAAK8E,SAAS,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChCrF,OAAA;cAAK8E,SAAS,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTrF,OAAA;UAAQ8E,SAAS,EAAC,aAAa;UAACU,OAAO,EAAEZ,cAAe;UAACa,KAAK,EAAC,UAAU;UAAAV,QAAA,EAAC;QAE1E;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrF,OAAA;UAAQ8E,SAAS,EAAC,aAAa;UAACU,OAAO,EAAEA,CAAA,KAAMnE,eAAe,CAAC,IAAI,CAAE;UAACoE,KAAK,EAAC,UAAU;UAAAV,QAAA,EAAC;QAEvF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3D,cAAc,iBACb1B,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9B/E,OAAA;QAAA+E,QAAA,EAAOrD;MAAc;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDrF,OAAA;MAAK8E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3B/E,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/E,OAAA;UAAK8E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC/E,OAAA;YAAK8E,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7C/E,OAAA;cAAI8E,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDrF,OAAA;cAAG8E,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL9E,mBAAmB,gBAClBP,OAAA;UAAK8E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/E,OAAA;YACE8E,SAAS,EAAC,iBAAiB;YAC3BY,KAAK,EAAE/E,qBAAsB;YAC7BgF,QAAQ,EAAGC,CAAC,IAAKhF,wBAAwB,CAACgF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC1DI,WAAW,EAAC,uCAAuC;YACnDC,SAAS;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACFrF,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/E,OAAA;cAAQ8E,SAAS,EAAC,yBAAyB;cAACU,OAAO,EAAEtB,kBAAmB;cAAAa,QAAA,EAAC;YAEzE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrF,OAAA;cAAQ8E,SAAS,EAAC,2BAA2B;cAACU,OAAO,EAAElB,oBAAqB;cAAAS,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENrF,OAAA;UAAK8E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B/E,OAAA;YACE8E,SAAS,EAAC,uCAAuC;YACjDU,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAClD,mBAAmB,CAAE;YAAA4E,QAAA,gBAEpD/E,OAAA;cAAG8E,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAE5E;YAAmB;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDrF,OAAA;cAAG8E,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA8B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChErF,OAAA;cAAQ8E,SAAS,EAAC,oBAAoB;cAACU,OAAO,EAAEvB,sBAAuB;cAAAc,QAAA,EAAC;YAExE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNrF,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B/E,OAAA;UAAK8E,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpC/E,OAAA;YAAI8E,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DrF,OAAA;YAAG8E,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,EAEL5E,wBAAwB,gBACvBT,OAAA;UAAK8E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/E,OAAA;YACE8E,SAAS,EAAC,iBAAiB;YAC3BY,KAAK,EAAE7E,0BAA2B;YAClC8E,QAAQ,EAAGC,CAAC,IAAK9E,6BAA6B,CAAC8E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC/DI,WAAW,EAAC,4CAA4C;YACxDC,SAAS;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACFrF,OAAA;YAAK8E,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B/E,OAAA;cAAQ8E,SAAS,EAAC,8BAA8B;cAACU,OAAO,EAAEhB,uBAAwB;cAAAO,QAAA,EAAC;YAEnF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTrF,OAAA;cAAQ8E,SAAS,EAAC,gCAAgC;cAACU,OAAO,EAAEf,yBAA0B;cAAAM,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENrF,OAAA;UAAK8E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B/E,OAAA;YACE8E,SAAS,EAAC,4CAA4C;YACtDU,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAChD,wBAAwB,CAAE;YAAA0E,QAAA,gBAEzD/E,OAAA;cAAG8E,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAE1E;YAAwB;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DrF,OAAA;cAAG8E,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAkC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpErF,OAAA;cAAQ8E,SAAS,EAAC,oBAAoB;cAACU,OAAO,EAAEjB,2BAA4B;cAAAQ,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNrF,OAAA;QAAI8E,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDrF,OAAA;QAAK8E,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BhE,YAAY,CAACiF,GAAG,CAAEpC,IAAI,iBACrB5D,OAAA;UAEE8E,SAAS,EAAC,cAAc;UACxBU,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAACC,IAAI,CAAE;UAAAmB,QAAA,gBAEhC/E,OAAA;YAAK8E,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC/E,OAAA;cAAM8E,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEnB,IAAI,CAACzC;YAAS;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDrF,OAAA;cACE8E,SAAS,EAAC,eAAe;cACzBU,OAAO,EAAGI,CAAC,IAAK;gBACdA,CAAC,CAACK,eAAe,CAAC,CAAC;gBACnBpC,iBAAiB,CAACD,IAAI,CAAC3C,EAAE,CAAC;cAC5B,CAAE;cAAA8D,QAAA,EACH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNrF,OAAA;YAAG8E,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEnB,IAAI,CAAC1C;UAAO;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GAhBzCzB,IAAI,CAAC3C,EAAE;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBT,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrF,OAAA;MAAQ8E,SAAS,EAAC,sBAAsB;MAACU,OAAO,EAAEd,OAAQ;MAAAK,QAAA,eACxD/E,OAAA;QAAKgF,GAAG,EAAC,WAAW;QAACC,GAAG,EAAC,MAAM;QAACH,SAAS,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,EAGRjE,YAAY,iBACXpB,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAACU,OAAO,EAAEA,CAAA,KAAMnE,eAAe,CAAC,KAAK,CAAE;MAAA0D,QAAA,eACnE/E,OAAA;QAAK8E,SAAS,EAAC,kBAAkB;QAACU,OAAO,EAAGI,CAAC,IAAKA,CAAC,CAACK,eAAe,CAAC,CAAE;QAAAlB,QAAA,gBACpE/E,OAAA;UAAK8E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B/E,OAAA;YAAI8E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CrF,OAAA;YAAQ8E,SAAS,EAAC,cAAc;YAACU,OAAO,EAAEA,CAAA,KAAMnE,eAAe,CAAC,KAAK,CAAE;YAAA0D,QAAA,EAAC;UAExE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENrF,OAAA;UAAK8E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAE/B/E,OAAA;YAAK8E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B/E,OAAA;cAAI8E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CrF,OAAA;cAAK8E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B/E,OAAA;gBAAG8E,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClDrF,OAAA;gBAAG8E,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClDrF,OAAA;gBAAK8E,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC/E,OAAA;kBAAK8E,SAAS,EAAE,2BAA2BlD,gBAAgB;gBAAG;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrErF,OAAA;kBAAM8E,SAAS,EAAE,sBAAsBlD,gBAAgB,EAAG;kBAAAmD,QAAA,GAAC,UACjD,EAACnD,gBAAgB,KAAK,WAAW,GAAG,WAAW,GAAG,cAAc;gBAAA;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrF,OAAA;YAAK8E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B/E,OAAA;cAAI8E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChDzC,aAAa,CAACoD,GAAG,CAAET,MAAM,iBACxBvF,OAAA;cAAqB8E,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1C/E,OAAA;gBAAK8E,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B/E,OAAA;kBAAG8E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEQ,MAAM,CAAC1C;gBAAI;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDrF,OAAA;kBAAG8E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEQ,MAAM,CAACzC;gBAAI;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDrF,OAAA;kBAAK8E,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC/E,OAAA;oBAAK8E,SAAS,EAAE,2BAA2BS,MAAM,CAACxC,MAAM;kBAAG;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClErF,OAAA;oBAAM8E,SAAS,EAAE,sBAAsBS,MAAM,CAACxC,MAAM,EAAG;oBAAAgC,QAAA,GACpDQ,MAAM,CAACxC,MAAM,EAAC,UAAG,EAACwC,MAAM,CAACvC,QAAQ;kBAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAVEE,MAAM,CAACtE,EAAE;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWd,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACnF,EAAA,CAzXQD,GAAG;AAAAiG,EAAA,GAAHjG,GAAG;AA2XZ,eAAeA,GAAG;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}