{"name": "electron-installer-common", "version": "0.10.4", "description": "Common functionality for creating distributable Electron apps", "author": "<PERSON>", "license": "Apache-2.0", "main": "src/index.js", "types": "src/index.d.ts", "scripts": {"ava": "ava", "codecov": "nyc report --reporter=text-lcov | codecov --disable=gcov --pipe --env=CI_OS,NODE_VERSION", "coverage": "nyc ava", "lint": "npm run lint:js && npm run lint:ts", "lint:js": "eslint .", "lint:ts": "eslint --config .eslintrc.typescript.js --ext .ts .", "test": "npm run lint && tsd && ava", "tsd": "tsd"}, "repository": "electron-userland/electron-installer-common", "keywords": ["electron", "installer"], "files": ["NEWS.md", "src"], "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.8.0", "@typescript-eslint/parser": "^4.8.0", "ava": "^3.0.0", "codecov": "^3.5.0", "eslint": "^7.12.1", "eslint-config-standard": "^16.0.0", "eslint-plugin-ava": "^12.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^5.1.0", "nyc": "^15.0.0", "sinon": "^11.1.0", "tsd": "^0.16.0", "typescript": "^4.0.2"}, "dependencies": {"@malept/cross-spawn-promise": "^1.0.0", "@electron/asar": "^3.2.5", "debug": "^4.1.1", "fs-extra": "^9.0.0", "glob": "^7.1.4", "lodash": "^4.17.15", "parse-author": "^2.0.0", "semver": "^7.1.1", "tmp-promise": "^3.0.2"}, "engines": {"node": ">= 10.0.0"}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:ava/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:node/recommended", "plugin:promise/recommended", "standard"]}, "funding": {"url": "https://github.com/electron-userland/electron-installer-common?sponsor=1"}, "tsd": {"directory": "test"}, "optionalDependencies": {"@types/fs-extra": "^9.0.1"}}