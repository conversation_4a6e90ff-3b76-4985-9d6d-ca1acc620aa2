const electronInstaller = require('electron-winstaller');
const path = require('path');

async function buildInstaller() {
  try {
    console.log('Creating Windows installer...');
    
    const options = {
      appDirectory: path.join(__dirname, 'dist', 'win-unpacked'),
      outputDirectory: path.join(__dirname, 'dist', 'installer'),
      authors: 'Clipsy Team',
      exe: 'Clipsy.exe',
      description: 'Cross-platform clipboard synchronization tool',
      version: '1.0.0',
      title: 'Clipsy',
      // iconUrl: path.join(__dirname, 'public', 'clipsy-logo-no-bg.png'),
      // setupIcon: path.join(__dirname, 'public', 'clipsy-logo-no-bg.png'),
      noMsi: true, // Only create .exe installer, not MSI
      skipUpdateIcon: true,
      certificateFile: undefined, // No code signing
      certificatePassword: undefined
    };

    await electronInstaller.createWindowsInstaller(options);
    console.log('✅ Windows installer created successfully!');
    console.log('📦 Installer location: dist/installer/');
    
  } catch (error) {
    console.error('❌ Error creating installer:', error);
    process.exit(1);
  }
}

buildInstaller();
