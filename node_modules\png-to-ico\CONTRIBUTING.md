First off, thank you for considering contributing to png-to-icon. It's people like you that make png-to-icon such a great module.

Following these guidelines helps to communicate that you respect the time of the developers managing and developing this open source project. In return, they should reciprocate that respect in addressing your issue, assessing changes, and helping you finalize your pull requests.

png-to-icon is an open source project and we love to receive contributions from our community — you! There are many ways to contribute, from writing blog posts, improving the documentation, submitting bug reports and feature requests or writing code which can be incorporated into png-to-icon itself.

Ground Rules:
- Ensure all tests pass.
- Make sure you only implement ONE feature or bugfix in a pull request.
- Do not add dependencies on other modules. Even better, do not add dev dependencies on other modules.
