import React, { useState, useEffect } from 'react';
import './App.css';

/*
 * Windows Computer Name Detection
 *
 * In a real Windows application (Electron, PWA, or native app), the computer name
 * would be retrieved from Windows Device specifications using:
 *
 * 1. Windows API: GetComputerNameEx() function
 * 2. WMI Query: SELECT Name FROM Win32_ComputerSystem
 * 3. Registry: HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\ComputerName\ComputerName
 * 4. PowerShell: $env:COMPUTERNAME or Get-ComputerInfo
 * 5. Settings Path: Settings > System > About > Device specifications > Device name
 *
 * This web version simulates the process and generates consistent computer names
 * based on browser fingerprinting for demonstration purposes.
 */

function App() {
  // State management matching Android app
  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to Clipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');
  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');
  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);
  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);
  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');
  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');

  const [historyItems, setHistoryItems] = useState([
    { id: '1', content: 'This is an older clipboard item. It\'s shorter.', timestamp: '2 minutes ago' },
    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },
    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },
    { id: '4', content: 'Some code snippet: function hello() { console.log("Hello World!"); }', timestamp: '5 hours ago' }
  ]);

  // UI State
  const [showSettings, setShowSettings] = useState(false);
  const [showSyncSettings, setShowSyncSettings] = useState(false);
  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);

  // Background Sync State
  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);
  const [networkDevices, setNetworkDevices] = useState([]);
  const [isDiscovering, setIsDiscovering] = useState(false);

  // Sync Settings - matching Android app
  const [syncSettings, setSyncSettings] = useState({
    autoSync: true,
    syncDelay: 2,
    syncOnConnect: true,
    bidirectional: true
  });

  // Device Management - matching Android app
  const [pairedDevices, setPairedDevices] = useState([
    {
      id: 'android-1',
      name: 'Android Phone - Personal',
      type: 'Android 14',
      status: 'connected',
      lastSeen: '2 min ago',
      ipAddress: '*************'
    },
    {
      id: 'linux-1',
      name: 'Ubuntu Server - Home',
      type: 'Ubuntu 22.04',
      status: 'disconnected',
      lastSeen: '1 hour ago',
      ipAddress: '*************'
    }
  ]);

  const [discoveredDevices, setDiscoveredDevices] = useState([]);
  const [isDeviceDiscoverable, setIsDeviceDiscoverable] = useState(true);

  // Device info state - get actual Windows device details from Device specifications
  const [deviceInfo, setDeviceInfo] = useState({
    name: 'Loading computer name from Device specifications...',
    type: 'Windows',
    status: 'active',
    lastSeen: 'now',
    ipAddress: '*************'
  });

  // Device discoverability service
  React.useEffect(() => {
    if (isDeviceDiscoverable) {
      // Start discovery service
      console.log('🔍 Device is now discoverable by other devices');
      showMessage('🔍 This device is discoverable by other Android and Windows devices');
    } else {
      console.log('🔒 Device discovery disabled');
    }
  }, [isDeviceDiscoverable]);

  // Get actual Windows computer name and OS details
  React.useEffect(() => {
    const getDeviceInfo = async () => {
      try {
        let osVersion = 'Windows';

        // Get OS version from user agent
        if (navigator.userAgentData) {
          const platform = navigator.userAgentData.platform;
          osVersion = platform || 'Windows';
        } else if (navigator.userAgent) {
          const windowsMatch = navigator.userAgent.match(/Windows NT (\d+\.\d+)/);
          if (windowsMatch) {
            const version = windowsMatch[1];
            switch (version) {
              case '10.0': osVersion = 'Windows 10/11'; break;
              case '6.3': osVersion = 'Windows 8.1'; break;
              case '6.2': osVersion = 'Windows 8'; break;
              case '6.1': osVersion = 'Windows 7'; break;
              default: osVersion = `Windows NT ${version}`;
            }
          }
        }

        // Get the actual computer name
        const computerName = await getWindowsComputerName();
        const deviceName = `${computerName} - ${osVersion}`;

        // Check if any devices are connected
        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');
        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';

        setDeviceInfo(prev => ({
          ...prev,
          name: deviceName,
          type: osVersion,
          status: deviceStatus,
          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'
        }));

        console.log('Device info updated:', { computerName, deviceName, osVersion });
      } catch (error) {
        console.log('Could not get device info:', error);
        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');
        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';

        setDeviceInfo(prev => ({
          ...prev,
          name: 'Windows PC - Main',
          type: 'Windows',
          status: deviceStatus,
          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'
        }));
      }
    };

    getDeviceInfo();
  }, [pairedDevices]);

  // Functions
  const showMessage = (text) => {
    setSuccessMessage(text);
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  // Get Windows computer name from Device specifications
  const getWindowsComputerName = async () => {
    try {
      // Method 1: Try to get from Windows Device specifications via WMI (if available)
      if (window.chrome && window.chrome.runtime) {
        try {
          // Try Chrome extension API if available
          const computerName = await window.chrome.runtime.sendMessage({
            action: 'getComputerName'
          });
          if (computerName) return computerName.trim().toUpperCase();
        } catch (e) {
          console.log('Chrome extension method failed:', e);
        }
      }

      // Method 2: Try PowerShell via Electron or native app
      if (window.electronAPI || window.nativeAPI) {
        try {
          const api = window.electronAPI || window.nativeAPI;
          const computerName = await api.getComputerName();
          if (computerName) return computerName.trim().toUpperCase();
        } catch (e) {
          console.log('Native API method failed:', e);
        }
      }

      // Method 3: Try to get from environment variables (Node.js/Electron environment)
      if (typeof process !== 'undefined' && process.env) {
        if (process.env.COMPUTERNAME) return process.env.COMPUTERNAME.toUpperCase();
        if (process.env.HOSTNAME) return process.env.HOSTNAME.toUpperCase();
      }

      // Method 4: Try Windows Device specifications API endpoint
      try {
        const response = await fetch('/api/device-specs', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });
        if (response.ok) {
          const deviceSpecs = await response.json();
          if (deviceSpecs.computerName) return deviceSpecs.computerName.toUpperCase();
          if (deviceSpecs.deviceName) return deviceSpecs.deviceName.toUpperCase();
        }
      } catch (e) {
        console.log('Device specs API method failed:', e);
      }

      // Method 5: Try WMI via WebAssembly (if available)
      try {
        if (window.WebAssembly) {
          // This would require a WASM module to access Windows WMI
          // For now, we'll simulate the call
          const wmiResult = await simulateWMICall();
          if (wmiResult) return wmiResult.toUpperCase();
        }
      } catch (e) {
        console.log('WMI method failed:', e);
      }

      // Method 6: Use network hostname if available and valid
      if (window.location.hostname &&
          window.location.hostname !== 'localhost' &&
          window.location.hostname !== '127.0.0.1' &&
          !window.location.hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
        return window.location.hostname.toUpperCase();
      }

      // Method 7: Generate or retrieve persistent computer name
      let storedName = localStorage.getItem('clipsy-computer-name');
      if (!storedName) {
        // Create a more Windows-like computer name
        const userAgent = navigator.userAgent;
        const screen = `${window.screen.width}x${window.screen.height}`;
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const language = navigator.language;
        const platform = navigator.platform;

        // Create a deterministic hash for this browser/system
        const hashInput = `${userAgent}-${screen}-${timezone}-${language}-${platform}`;
        let hash = 0;
        for (let i = 0; i < hashInput.length; i++) {
          const char = hashInput.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32-bit integer
        }

        // Generate Windows-style computer name
        const hashHex = Math.abs(hash).toString(16).toUpperCase().slice(0, 6);
        storedName = `DESKTOP-${hashHex}`;
        localStorage.setItem('clipsy-computer-name', storedName);

        console.log('Generated computer name:', storedName);
      }

      return storedName;
    } catch (error) {
      console.log('All computer name methods failed:', error);
      return 'DESKTOP-CLIPSY';
    }
  };

  // Simulate WMI call for computer name (would access Windows Device specifications)
  const simulateWMICall = async () => {
    return new Promise((resolve) => {
      // In a real Windows application, this would access:
      // Settings > System > About > Device specifications > Device name
      // Using Windows Management Instrumentation (WMI) or Windows API

      // Simulate getting actual Windows computer names from Device specifications
      const possibleComputerNames = [
        'DESKTOP-ABC123',
        'LAPTOP-DEF456',
        'WORKSTATION-789',
        'PC-USER-001',
        'GAMING-RIG-01',
        'OFFICE-PC-02'
      ];

      // Use a deterministic selection based on browser fingerprint
      const userAgent = navigator.userAgent;
      const screen = `${window.screen.width}x${window.screen.height}`;
      const fingerprint = `${userAgent}-${screen}`;

      let hash = 0;
      for (let i = 0; i < fingerprint.length; i++) {
        const char = fingerprint.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }

      const index = Math.abs(hash) % possibleComputerNames.length;
      const selectedName = possibleComputerNames[index];

      console.log('Simulated WMI call - Device specifications computer name:', selectedName);

      // Simulate API delay
      setTimeout(() => resolve(selectedName), 200);
    });
  };

  const copyToClipboard = async (content) => {
    try {
      await navigator.clipboard.writeText(content);
      setThisDeviceClipboard(content);
      showMessage('✅ Text copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      showMessage('❌ Failed to copy to clipboard');
    }
  };

  const selectItem = (item) => {
    copyToClipboard(item.content);
    setConnectedDeviceClipboard(item.content);
  };

  const deleteHistoryItem = (itemId) => {
    const updatedHistory = historyItems.filter(item => item.id !== itemId);
    setHistoryItems(updatedHistory);
    showMessage('🗑️ History item deleted!');
  };

  // Device edit functions
  const startEditingThisDevice = () => {
    setEditingThisDeviceText(thisDeviceClipboard);
    setIsEditingThisDevice(true);
  };

  const saveThisDeviceEdit = async () => {
    const newContent = editingThisDeviceText.trim();
    if (!newContent) {
      showMessage('Content cannot be empty');
      return;
    }

    setThisDeviceClipboard(newContent);
    try {
      await navigator.clipboard.writeText(newContent);
    } catch (error) {
      console.warn('Failed to update clipboard:', error);
    }

    setIsEditingThisDevice(false);
    setEditingThisDeviceText('');
    showMessage('✅ This Device clipboard updated!');
  };

  const cancelThisDeviceEdit = () => {
    setIsEditingThisDevice(false);
    setEditingThisDeviceText('');
  };

  const startEditingConnectedDevice = () => {
    setEditingConnectedDeviceText(connectedDeviceClipboard);
    setIsEditingConnectedDevice(true);
  };

  const saveConnectedDeviceEdit = () => {
    const newContent = editingConnectedDeviceText.trim();
    if (!newContent) {
      showMessage('Content cannot be empty');
      return;
    }

    setConnectedDeviceClipboard(newContent);
    setIsEditingConnectedDevice(false);
    setEditingConnectedDeviceText('');
    showMessage('✅ Connected Device clipboard updated!');
  };

  const cancelConnectedDeviceEdit = () => {
    setIsEditingConnectedDevice(false);
    setEditingConnectedDeviceText('');
  };

  const syncNow = () => {
    showMessage('🔄 Syncing with paired devices...');
    setTimeout(() => {
      showMessage('✅ Sync completed!');
    }, 1000);
  };

  const toggleAlwaysOnTop = () => {
    setIsAlwaysOnTop(!isAlwaysOnTop);
    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');
  };

  const minimizeToTray = () => {
    showMessage('➖ Minimizing to background...');
  };

  const toggleFloatingOverlay = () => {
    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);
    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');
  };

  // Device Management Functions
  const removeDevice = (deviceId) => {
    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);
    setPairedDevices(updatedDevices);
    showMessage('🗑️ Device removed from paired devices');
  };

  const connectDevice = (deviceId) => {
    const updatedDevices = pairedDevices.map(device =>
      device.id === deviceId ? { ...device, status: 'connected', lastSeen: 'Just now' } : device
    );
    setPairedDevices(updatedDevices);
    showMessage('✅ Device connected successfully');
  };

  const disconnectDevice = (deviceId) => {
    const updatedDevices = pairedDevices.map(device =>
      device.id === deviceId ? { ...device, status: 'disconnected', lastSeen: 'Just now' } : device
    );
    setPairedDevices(updatedDevices);
    showMessage('🔌 Device disconnected');
  };

  const pairDevice = (deviceId) => {
    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);
    if (deviceToPair) {
      const newPairedDevice = {
        ...deviceToPair,
        status: 'connected',
        lastSeen: 'Just now'
      };
      setPairedDevices([...pairedDevices, newPairedDevice]);
      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);
    }
  };

  const refreshDiscovery = async () => {
    if (isDiscovering) return;

    setIsDiscovering(true);
    setNetworkDevices([]); // Clear previous results
    setDiscoveredDevices([]); // Clear discovered devices
    showMessage('🔍 Searching for Android and Windows devices...');

    try {
      // Start device discovery process
      const foundDevices = [];

      // Phase 1: Network scanning for Windows devices
      showMessage('🖥️ Scanning for Windows devices...');
      const windowsDevices = await scanForWindowsDevices();
      foundDevices.push(...windowsDevices);

      // Update UI with Windows devices found
      if (windowsDevices.length > 0) {
        setNetworkDevices(prev => [...prev, ...windowsDevices]);
        setDiscoveredDevices(prev => [...prev, ...windowsDevices]);
        showMessage(`💻 Found ${windowsDevices.length} Windows device(s)`);
      }

      // Phase 2: Broadcast discovery for Android devices
      showMessage('📱 Scanning for Android devices...');
      const androidDevices = await scanForAndroidDevices();
      foundDevices.push(...androidDevices);

      // Update UI with Android devices found
      if (androidDevices.length > 0) {
        setNetworkDevices(prev => [...prev, ...androidDevices]);
        setDiscoveredDevices(prev => [...prev, ...androidDevices]);
        showMessage(`📱 Found ${androidDevices.length} Android device(s)`);
      }

      // Phase 3: mDNS/Bonjour discovery for all Clipsy devices
      showMessage('🔍 Scanning for Clipsy-enabled devices...');
      const clipsyDevices = await scanForClipsyDevices();
      foundDevices.push(...clipsyDevices);

      // Update UI with Clipsy devices found
      if (clipsyDevices.length > 0) {
        setNetworkDevices(prev => [...prev, ...clipsyDevices]);
        setDiscoveredDevices(prev => [...prev, ...clipsyDevices]);
        showMessage(`🔗 Found ${clipsyDevices.length} Clipsy-enabled device(s)`);
      }

      // Final results
      setIsDiscovering(false);
      if (foundDevices.length > 0) {
        showMessage(`✅ Discovery complete! Found ${foundDevices.length} available devices.`);
      } else {
        showMessage('❌ No devices found. Make sure devices are on the same network.');
      }

    } catch (error) {
      console.error('Discovery error:', error);
      setIsDiscovering(false);
      showMessage('❌ Discovery failed. Please check network connection.');
    }
  };

  // Device scanning functions
  const scanForWindowsDevices = async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const windowsDevices = [
          {
            id: 'windows-laptop-001',
            name: 'LAPTOP-WORK123',
            type: 'Windows 11 Pro',
            status: 'discovering',
            lastSeen: 'Available for pairing',
            ipAddress: '*************',
            deviceType: 'windows'
          },
          {
            id: 'windows-desktop-001',
            name: 'DESKTOP-GAMING',
            type: 'Windows 10',
            status: 'discovering',
            lastSeen: 'Available for pairing',
            ipAddress: '*************',
            deviceType: 'windows'
          },
          {
            id: 'windows-surface-001',
            name: 'SURFACE-PRO9',
            type: 'Windows 11',
            status: 'discovering',
            lastSeen: 'Available for pairing',
            ipAddress: '*************',
            deviceType: 'windows'
          }
        ];
        resolve(windowsDevices);
      }, 1500);
    });
  };

  const scanForAndroidDevices = async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const androidDevices = [
          {
            id: 'android-samsung-001',
            name: 'Samsung Galaxy S23',
            type: 'Android 14',
            status: 'discovering',
            lastSeen: 'Available for pairing',
            ipAddress: '*************',
            deviceType: 'android'
          },
          {
            id: 'android-oneplus-001',
            name: 'OnePlus 11',
            type: 'Android 14',
            status: 'discovering',
            lastSeen: 'Available for pairing',
            ipAddress: '*************',
            deviceType: 'android'
          },
          {
            id: 'android-pixel-001',
            name: 'Google Pixel 8',
            type: 'Android 14',
            status: 'discovering',
            lastSeen: 'Available for pairing',
            ipAddress: '*************',
            deviceType: 'android'
          }
        ];
        resolve(androidDevices);
      }, 2000);
    });
  };

  const scanForClipsyDevices = async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const clipsyDevices = [
          {
            id: 'clipsy-tablet-001',
            name: 'iPad Pro (Clipsy)',
            type: 'iPadOS 17',
            status: 'discovering',
            lastSeen: 'Available for pairing',
            ipAddress: '*************',
            deviceType: 'ios'
          },
          {
            id: 'clipsy-mac-001',
            name: 'MacBook Pro M3',
            type: 'macOS Sonoma',
            status: 'discovering',
            lastSeen: 'Available for pairing',
            ipAddress: '*************',
            deviceType: 'macos'
          }
        ];
        resolve(clipsyDevices);
      }, 1000);
    });
  };

  const scanQRCode = () => {
    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');
  };

  const generateQRCode = () => {
    // Generate connection info for QR code
    const connectionInfo = {
      deviceName: deviceInfo.name,
      deviceType: deviceInfo.type,
      ipAddress: deviceInfo.ipAddress,
      port: 3001,
      protocol: 'clipsy-sync',
      timestamp: Date.now()
    };

    const qrData = JSON.stringify(connectionInfo);
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrData)}`;

    // Create and show QR code modal
    const qrModal = document.createElement('div');
    qrModal.className = 'qr-modal-overlay';
    qrModal.innerHTML = `
      <div class="qr-modal-content">
        <div class="qr-modal-header">
          <h3>📱 Scan to Connect Android Device</h3>
          <button class="qr-modal-close">✕</button>
        </div>
        <div class="qr-modal-body">
          <img src="${qrCodeUrl}" alt="QR Code" class="qr-code-image" />
          <p class="qr-instructions">
            1. Open Clipsy app on your Android device<br/>
            2. Go to Settings → Device Discovery<br/>
            3. Tap "Scan QR" and scan this code<br/>
            4. Your devices will be paired automatically
          </p>
          <div class="qr-device-info">
            <p><strong>Device:</strong> ${deviceInfo.name}</p>
            <p><strong>IP:</strong> ${deviceInfo.ipAddress}</p>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(qrModal);

    // Close modal functionality
    const closeModal = () => {
      document.body.removeChild(qrModal);
    };

    qrModal.querySelector('.qr-modal-close').onclick = closeModal;
    qrModal.onclick = (e) => {
      if (e.target === qrModal) closeModal();
    };

    showMessage('📱 QR Code generated! Scan with Android Clipsy app to connect.');
  };

  return (
    <div className="app-container">
      {/* Header */}
      <div className="header">
        <div className="title-container">
          <div className="logo-container">
            <img
              src="/clipsy-logo-no-bg.png"
              alt="Clipsy Logo"
              className="app-logo"
            />
            <div className={`connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`}></div>
          </div>
          <h1 className="title">Clipsy</h1>
        </div>
        <div className="header-actions">
          <button
            className={`icon-button ${isFloatingOverlayVisible ? 'active' : ''}`}
            onClick={toggleFloatingOverlay}
            title="Toggle floating clipboard widget"
          >
            📋
          </button>
          <button
            className={`icon-button ${isAlwaysOnTop ? 'active' : ''}`}
            onClick={toggleAlwaysOnTop}
            title="Pin to top"
          >
            <div className="pin-icon">
              <div className="pin-head"></div>
              <div className="pin-body"></div>
            </div>
          </button>
          <button className="icon-button" onClick={minimizeToTray} title="Minimize">
            ➖
          </button>
          <button className="icon-button" onClick={() => setShowSettings(true)} title="Settings">
            ⚙️
          </button>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="success-message">
          <span>{successMessage}</span>
        </div>
      )}

      {/* Main Content */}
      <div className="main-content">
        {/* This Device Section */}
        <div className="device-section">
          <div className="device-section-header">
            <div className="device-section-title-container">
              <h2 className="device-section-title">💻 This Device</h2>
              <p className="device-section-subtitle">{deviceInfo.name}</p>
            </div>
          </div>

          {isEditingThisDevice ? (
            <div className="edit-container">
              <textarea
                className="edit-text-input"
                value={editingThisDeviceText}
                onChange={(e) => setEditingThisDeviceText(e.target.value)}
                placeholder="Edit this device clipboard content..."
                autoFocus
              />
              <div className="edit-actions">
                <button className="save-button this-device" onClick={saveThisDeviceEdit}>
                  Save
                </button>
                <button className="cancel-button this-device" onClick={cancelThisDeviceEdit}>
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="device-clipboard">
              <div
                className="clipboard-content this-device-content"
                onClick={() => copyToClipboard(thisDeviceClipboard)}
              >
                <p className="clipboard-text">{thisDeviceClipboard}</p>
                <p className="clipboard-meta">Click to copy • Real-time sync</p>
                <button className="edit-button-inside" onClick={startEditingThisDevice}>
                  ✎
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Connected Device Section */}
        <div className="device-section">
          <div className="device-section-header">
            <h2 className="device-section-title">🔗 Connected Device</h2>
            <p className="device-section-subtitle">Android Phone - Personal 🟢</p>
          </div>

          {isEditingConnectedDevice ? (
            <div className="edit-container">
              <textarea
                className="edit-text-input"
                value={editingConnectedDeviceText}
                onChange={(e) => setEditingConnectedDeviceText(e.target.value)}
                placeholder="Edit connected device clipboard content..."
                autoFocus
              />
              <div className="edit-actions">
                <button className="save-button connected-device" onClick={saveConnectedDeviceEdit}>
                  Save
                </button>
                <button className="cancel-button connected-device" onClick={cancelConnectedDeviceEdit}>
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="device-clipboard">
              <div
                className="clipboard-content connected-device-content"
                onClick={() => copyToClipboard(connectedDeviceClipboard)}
              >
                <p className="clipboard-text">{connectedDeviceClipboard}</p>
                <p className="clipboard-meta">Click to copy • Bidirectional sync</p>
                <button className="edit-button-inside" onClick={startEditingConnectedDevice}>
                  ✎
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Clipboard History */}
        <h2 className="history-title">Clipboard History</h2>
        <div className="history-list">
          {historyItems.map((item) => (
            <div
              key={item.id}
              className="history-item"
              onClick={() => selectItem(item)}
            >
              <div className="history-item-header">
                <span className="timestamp">{item.timestamp}</span>
                <button
                  className="delete-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteHistoryItem(item.id);
                  }}
                >
                  ✕
                </button>
              </div>
              <p className="item-content">{item.content}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Floating Sync Button */}
      <button className="floating-sync-button" onClick={syncNow}>
        <img src="/sync.png" alt="Sync" className="sync-icon" />
      </button>

      {/* Settings Modal */}
      {showSettings && (
        <div className="modal-overlay" onClick={() => setShowSettings(false)}>
          <div className="settings-sidebar" onClick={(e) => e.stopPropagation()}>
            <div className="settings-header">
              <h2 className="settings-title">Settings</h2>
              <button className="close-button" onClick={() => setShowSettings(false)}>
                ✕
              </button>
            </div>

            <div className="settings-content">
              {/* Device Info */}
              <div className="settings-section">
                <h3 className="section-title">Device Info</h3>
                <div className="device-info-card">
                  <p className="device-name">{deviceInfo.name}</p>
                  <p className="device-detail">{deviceInfo.type}</p>
                  <p className="device-detail">IP: {deviceInfo.ipAddress}</p>
                  <div className="device-status-row">
                    <div className={`device-status-indicator ${deviceInfo.status}`}></div>
                    <span className={`device-status-text ${deviceInfo.status}`}>
                      Status: {deviceInfo.status === 'active' ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                  <div className="device-status-row">
                    <div className={`device-status-indicator ${isDeviceDiscoverable ? 'active' : 'disconnected'}`}></div>
                    <span className={`device-status-text ${isDeviceDiscoverable ? 'active' : 'disconnected'}`}>
                      Discoverable: {isDeviceDiscoverable ? 'Visible to other devices' : 'Hidden from discovery'}
                    </span>
                  </div>
                  {deviceInfo.status === 'disconnected' && (
                    <p className="device-detail disconnected-notice">
                      ⚠️ No devices connected - Use QR code or device discovery to connect Android devices
                    </p>
                  )}
                </div>
              </div>

              {/* Paired Devices */}
              <div className="settings-section">
                <h3 className="section-title">Paired Devices</h3>
                {pairedDevices.length === 0 ? (
                  <div className="empty-device-list">
                    <p className="empty-device-text">No paired devices found</p>
                    <p className="empty-device-subtext">Use QR code or device discovery to pair devices</p>
                  </div>
                ) : (
                  pairedDevices.map((device) => (
                    <div key={device.id} className="enhanced-device-card">
                      {/* Remove Button - White X at top right corner */}
                      <button
                        className="remove-button-top-right"
                        onClick={() => removeDevice(device.id)}
                      >
                        ×
                      </button>

                      <div className="device-info">
                        <p className="device-card-name">{device.name}</p>
                        <p className="device-card-type">{device.type}</p>
                        <div className="device-status-row">
                          <div className={`device-status-indicator ${device.status}`}></div>
                          <span className={`device-status-text ${device.status}`}>
                            {device.status === 'connected' ? 'Connected' : 'Disconnected'} • {device.lastSeen}
                          </span>
                        </div>
                      </div>

                      {/* Connect/Disconnect Button - Bottom Right Corner */}
                      <button
                        className={`device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`}
                        onClick={() => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id)}
                      >
                        {device.status === 'connected' ? 'Disconnect' : 'Connect'}
                      </button>
                    </div>
                  ))
                )}
              </div>

              {/* Device Discovery */}
              <div className="settings-section">
                <div className="section-header">
                  <h3 className="section-title">Device Discovery</h3>
                  <button
                    className={`discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`}
                    onClick={refreshDiscovery}
                    disabled={isDiscovering}
                  >
                    {isDiscovering ? '🔍 Scanning...' : 'Discover'}
                  </button>
                </div>
                {networkDevices.length === 0 ? (
                  <div className="empty-device-list">
                    <p className="empty-device-text">No devices found</p>
                    <p className="empty-device-subtext">
                      {isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'}
                    </p>
                  </div>
                ) : (
                  networkDevices.map((device) => (
                    <div key={device.id} className="discovered-device-card">
                      <div className="device-info">
                        <div className="device-name-with-icon">
                          <span className="device-type-icon">
                            {device.deviceType === 'android' ? '📱' :
                             device.deviceType === 'windows' ? '🖥️' :
                             device.deviceType === 'ios' ? '📱' :
                             device.deviceType === 'macos' ? '💻' : '🖥️'}
                          </span>
                          <p className="device-card-name">{device.name}</p>
                        </div>
                        <p className="device-card-type">{device.type}</p>
                        <p className="device-card-last-seen">{device.lastSeen}</p>
                        <p className="device-card-ip">IP: {device.ipAddress}</p>
                      </div>
                      <button
                        className="pair-button"
                        onClick={() => pairDevice(device.id)}
                      >
                        {device.deviceType === 'android' ? '📱 Pair Android' :
                         device.deviceType === 'windows' ? '🖥️ Pair Windows' :
                         '🔗 Pair Device'}
                      </button>
                    </div>
                  ))
                )}

                <div className="qr-buttons-container">
                  <button className="qr-generate-button" onClick={generateQRCode}>
                    Generate QR
                  </button>
                </div>
              </div>

              {/* Additional Settings */}
              <div className="settings-section">
                <h3 className="section-title">Additional Settings</h3>
                <button
                  className="setting-item"
                  onClick={() => setShowSyncSettings(true)}
                >
                  <span className="setting-item-text">Sync Settings</span>
                  <span className="setting-item-arrow">›</span>
                </button>

                {/* Background Sync Setting */}
                <button
                  className="setting-item"
                  onClick={() => {
                    const newValue = !backgroundSyncEnabled;
                    setBackgroundSyncEnabled(newValue);
                    showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');
                  }}
                >
                  <span className="setting-item-text">Background Sync</span>
                  <span className="setting-item-value">
                    {backgroundSyncEnabled ? 'on' : 'off'}
                  </span>
                </button>

                {/* Device Discoverability Setting */}
                <button
                  className="setting-item"
                  onClick={() => {
                    setIsDeviceDiscoverable(!isDeviceDiscoverable);
                    showMessage(
                      !isDeviceDiscoverable
                        ? '🔍 Device is now discoverable by other Android and Windows devices'
                        : '🔒 Device is now hidden from discovery'
                    );
                  }}
                >
                  <span className="setting-item-text">Device Discoverability</span>
                  <span className="setting-item-value">
                    {isDeviceDiscoverable ? 'on' : 'off'}
                  </span>
                </button>

                {/* Network Discovery Setting */}
                <button
                  className="setting-item"
                  onClick={() => {
                    if (isDiscovering) {
                      setIsDiscovering(false);
                      showMessage('🔍 Network discovery stopped');
                    } else {
                      setIsDiscovering(true);
                      showMessage('🔍 Network discovery started');
                    }
                  }}
                >
                  <span className="setting-item-text">Network Discovery</span>
                  <span className="setting-item-value">
                    {isDiscovering ? 'on' : 'off'}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sync Settings Modal */}
      {showSyncSettings && (
        <div className="modal-overlay" onClick={() => setShowSyncSettings(false)}>
          <div className="sync-settings-modal" onClick={(e) => e.stopPropagation()}>
            <div className="settings-header">
              <h2 className="settings-title">Sync Settings</h2>
              <button className="close-button" onClick={() => setShowSyncSettings(false)}>
                ✕
              </button>
            </div>

            <div className="settings-content">
              <div className="settings-section">
                <div className="sync-setting-item">
                  <label className="sync-setting-label">Auto Sync</label>
                  <input
                    type="checkbox"
                    className="sync-setting-checkbox"
                    checked={syncSettings.autoSync}
                    onChange={(e) => setSyncSettings({...syncSettings, autoSync: e.target.checked})}
                  />
                </div>

                <div className="sync-setting-item">
                  <label className="sync-setting-label">Sync Delay: {syncSettings.syncDelay} seconds</label>
                  <p className="sync-setting-description">
                    {syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`}
                  </p>
                  <div className="sync-delay-controls">
                    <button
                      className="sync-delay-button"
                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.max(0, syncSettings.syncDelay - 1)})}
                    >
                      -
                    </button>
                    <span className="sync-delay-value">{syncSettings.syncDelay}s</span>
                    <button
                      className="sync-delay-button"
                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.min(30, syncSettings.syncDelay + 1)})}
                    >
                      +
                    </button>
                  </div>
                </div>

                <div className="sync-setting-item">
                  <label className="sync-setting-label">Sync on Connect</label>
                  <input
                    type="checkbox"
                    className="sync-setting-checkbox"
                    checked={syncSettings.syncOnConnect}
                    onChange={(e) => setSyncSettings({...syncSettings, syncOnConnect: e.target.checked})}
                  />
                </div>

                <div className="sync-setting-item">
                  <label className="sync-setting-label">Bidirectional Sync</label>
                  <input
                    type="checkbox"
                    className="sync-setting-checkbox"
                    checked={syncSettings.bidirectional}
                    onChange={(e) => setSyncSettings({...syncSettings, bidirectional: e.target.checked})}
                  />
                </div>

                {/* Cross-Platform Sync Controls */}
                <div className="sync-setting-item">
                  <button
                    className="cross-platform-sync-button"
                    onClick={() => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.')}
                  >
                    📱 Windows ↔ Android Sync
                  </button>
                </div>

                <div className="sync-setting-item">
                  <button
                    className="cross-platform-sync-button"
                    onClick={() => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.')}
                  >
                    🖥️ Windows ↔ Windows Sync
                  </button>
                </div>

                <div className="sync-setting-item">
                  <button
                    className="cross-platform-sync-button"
                    onClick={() => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.')}
                  >
                    🚀 Sync All Devices
                  </button>
                </div>

                {/* Floating Overlay Button Settings */}
                <div className="settings-section">
                  <h3 className="settings-section-title">📋 Floating Overlay Button Settings</h3>
                  <p className="settings-description">
                    Configure the floating overlay button for quick access to connected device clipboards
                  </p>

                  <div className="settings-row">
                    <span className="settings-label">Enable Floating Overlay Button</span>
                    <button
                      className="settings-toggle active"
                      onClick={() => showMessage('📋 Floating overlay button is always enabled for accessibility')}
                    >
                      <div className="settings-toggle-thumb active"></div>
                    </button>
                  </div>

                  <div className="settings-row">
                    <span className="settings-label">Show Device Count Badge</span>
                    <button
                      className="settings-toggle active"
                      onClick={() => showMessage('🔢 Device count badge enabled')}
                    >
                      <div className="settings-toggle-thumb active"></div>
                    </button>
                  </div>

                  <div className="settings-row">
                    <span className="settings-label">Auto-hide After Copy</span>
                    <button
                      className="settings-toggle active"
                      onClick={() => showMessage('⏱️ Auto-hide after copy enabled')}
                    >
                      <div className="settings-toggle-thumb active"></div>
                    </button>
                  </div>

                  <p className="settings-note">
                    💡 The floating overlay button (📋) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;