directories:
  output: dist
  buildResources: assets
files:
  - filter:
      - build/**/*
      - build/**/*
      - electron.js
      - preload.js
extraMetadata:
  main: build/electron.js
appId: com.clipsy.windows
productName: Clipsy
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: public/clipsy-logo-no-bg.png
  artifactName: ${productName}-${version}-${arch}.${ext}
  forceCodeSigning: false
  verifyUpdateCodeSignature: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Clipsy
portable:
  artifactName: ${productName}-${version}-portable-${arch}.${ext}
extends: react-cra
electronVersion: 37.1.0
