directories:
  output: dist
  buildResources: assets
files:
  - filter:
      - build/**/*
      - build/**/*
      - electron.js
      - preload.js
      - node_modules/**/*
extraMetadata:
  main: build/electron.js
appId: com.clipsy.windows
productName: Clipsy
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
    - target: portable
      arch:
        - x64
        - ia32
  icon: public/clipsy-logo-no-bg.png
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-${arch}.${ext}
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Clipsy
portable:
  artifactName: ${productName}-${version}-portable-${arch}.${ext}
extraResources:
  - from: public/
    to: public/
    filter:
      - '**/*'
extends: react-cra
electronVersion: 37.1.0
