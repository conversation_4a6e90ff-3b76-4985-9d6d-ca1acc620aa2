{"version": 3, "file": "static/css/main.a480311c.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCZA,KACE,iBACF,CAEA,YACE,wBAAyB,CAEzB,UAAY,CADZ,YAEF,CAEA,eAEE,cAAe,CADf,eAEF,CAEA,cAEE,gBAAiB,CADjB,QAAS,CAET,UACF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\r\n  margin: 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n    monospace;\r\n}", ".App {\r\n  text-align: center;\r\n}\r\n\r\n.App-header {\r\n  background-color: #282c34;\r\n  padding: 20px;\r\n  color: white;\r\n}\r\n\r\n.App-header h1 {\r\n  margin: 0 0 10px 0;\r\n  font-size: 2rem;\r\n}\r\n\r\n.App-header p {\r\n  margin: 0;\r\n  font-size: 1.1rem;\r\n  opacity: 0.8;\r\n}"], "names": [], "sourceRoot": ""}