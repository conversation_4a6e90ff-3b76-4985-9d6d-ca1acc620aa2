﻿[28-06-25 16.32.19] info: Program: Starting Squirrel Updater: --releasify C:\Users\<USER>\AppData\Local\Temp\si-2025528-10620-1aw01ef.12g7\clipsy-windows.1.0.0.nupkg --releaseDir D:\new git\Clipsy-Windows\dist\installer --loadingGif D:\new git\Clipsy-Windows\node_modules\electron-winstaller\resources\install-spinner.gif --setupIcon D:\new git\Clipsy-Windows\public\clipsy-logo-no-bg.png --no-msi
[28-06-25 16.32.19] info: Program: Bootstrapper EXE found at:D:\new git\Clipsy-Windows\node_modules\electron-winstaller\vendor\Setup.exe
[28-06-25 16.32.19] info: Program: Creating release package: D:\new git\Clipsy-Windows\dist\installer\clipsy-windows.1.0.0.nupkg
[28-06-25 16.32.19] info: ReleasePackage: Creating release package: D:\new git\Clipsy-Windows\dist\installer\clipsy-windows.1.0.0.nupkg => D:\new git\Clipsy-Windows\dist\installer\clipsy-windows-1.0.0-full.nupkg
[28-06-25 16.32.22] info: ReleasePackage: Extracting dependent packages: []
[28-06-25 16.32.22] info: ReleasePackage: Removing unnecessary data
[28-06-25 16.32.22] info: ReleasePackage: No release notes found in C:\Users\<USER>\AppData\Local\SquirrelTemp\tempa\clipsy-windows.nuspec
[28-06-25 16.32.47] info: Program: Building embedded zip file for Setup.exe
[28-06-25 16.32.54] fatal: Finished with unhandled exception: System.AggregateException: One or more errors occurred. ---> System.Exception: Failed to modify resources, command invoked was: 'D:\new git\Clipsy-Windows\node_modules\electron-winstaller\vendor\rcedit.exe "D:\new git\Clipsy-Windows\dist\installer\Setup.exe" --set-version-string "CompanyName" "Clipsy Team" --set-version-string "LegalCopyright" "Copyright © 2025 Clipsy Team" --set-version-string "FileDescription" "Cross-platform clipboard synchronization tool" --set-version-string "ProductName" "Cross-platform clipboard synchronization tool" --set-file-version 1.0.0 --set-product-version 1.0.0 --set-icon "D:\new git\Clipsy-Windows\public\clipsy-logo-no-bg.png"'

Output was:
Fatal error: Unable to set icon
   at Squirrel.Update.Program.<setPEVersionInfoAndIcon>d__20.MoveNext()
   --- End of inner exception stack trace ---
   at System.Threading.Tasks.Task.ThrowIfExceptional(Boolean includeTaskCanceledExceptions)
   at System.Threading.Tasks.Task.Wait(Int32 millisecondsTimeout, CancellationToken cancellationToken)
   at Squirrel.Update.Program.<>c__DisplayClass10_0.<Releasify>b__6()
   at Squirrel.Utility.<>c__DisplayClass8_0.<Retry>b__0()
   at Squirrel.Utility.Retry[T](Func`1 block, Int32 retries)
   at Squirrel.Update.Program.Releasify(String package, String targetDir, String packagesDir, String bootstrapperExe, String backgroundGif, String signingOpts, String baseUrl, String setupIcon, Boolean generateMsi, Boolean packageAs64Bit, String frameworkVersion, Boolean generateDeltas)
   at Squirrel.Update.Program.executeCommandLine(String[] args)
   at Squirrel.Update.Program.main(String[] args)
---> (Inner Exception #0) System.Exception: Failed to modify resources, command invoked was: 'D:\new git\Clipsy-Windows\node_modules\electron-winstaller\vendor\rcedit.exe "D:\new git\Clipsy-Windows\dist\installer\Setup.exe" --set-version-string "CompanyName" "Clipsy Team" --set-version-string "LegalCopyright" "Copyright © 2025 Clipsy Team" --set-version-string "FileDescription" "Cross-platform clipboard synchronization tool" --set-version-string "ProductName" "Cross-platform clipboard synchronization tool" --set-file-version 1.0.0 --set-product-version 1.0.0 --set-icon "D:\new git\Clipsy-Windows\public\clipsy-logo-no-bg.png"'

Output was:
Fatal error: Unable to set icon
   at Squirrel.Update.Program.<setPEVersionInfoAndIcon>d__20.MoveNext()<---

[28-06-25 16.33.45] info: Program: Starting Squirrel Updater: --releasify C:\Users\<USER>\AppData\Local\Temp\si-2025528-17460-1pzm87v.bt22\clipsy-windows.1.0.0.nupkg --releaseDir D:\new git\Clipsy-Windows\dist\installer --loadingGif D:\new git\Clipsy-Windows\node_modules\electron-winstaller\resources\install-spinner.gif --no-msi
[28-06-25 16.33.45] info: Program: Bootstrapper EXE found at:D:\new git\Clipsy-Windows\node_modules\electron-winstaller\vendor\Setup.exe
[28-06-25 16.33.45] info: Program: Creating release package: D:\new git\Clipsy-Windows\dist\installer\clipsy-windows.1.0.0.nupkg
[28-06-25 16.33.46] info: ReleasePackage: Creating release package: D:\new git\Clipsy-Windows\dist\installer\clipsy-windows.1.0.0.nupkg => D:\new git\Clipsy-Windows\dist\installer\clipsy-windows-1.0.0-full.nupkg
[28-06-25 16.33.48] info: ReleasePackage: Extracting dependent packages: []
[28-06-25 16.33.48] info: ReleasePackage: Removing unnecessary data
[28-06-25 16.33.48] info: ReleasePackage: No release notes found in C:\Users\<USER>\AppData\Local\SquirrelTemp\tempa\clipsy-windows.nuspec
[28-06-25 16.34.14] info: Program: Building embedded zip file for Setup.exe
[28-06-25 16.34.27] info: Program: Finished Squirrel Updater
