{"ast": null, "code": "var _jsxFileName = \"D:\\\\new git\\\\Clipsy-Windows\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  // State management matching Android app\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to Clipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\n  const [historyItems, setHistoryItems] = useState([{\n    id: '1',\n    content: 'This is an older clipboard item. It\\'s shorter.',\n    timestamp: '2 minutes ago'\n  }, {\n    id: '2',\n    content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.',\n    timestamp: '10 minutes ago'\n  }, {\n    id: '3',\n    content: 'Yet another historical entry.',\n    timestamp: '1 hour ago'\n  }, {\n    id: '4',\n    content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }',\n    timestamp: '5 hours ago'\n  }]);\n\n  // UI State\n  const [showSettings, setShowSettings] = useState(false);\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\n\n  // Background Sync State\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\n  const [networkDevices, setNetworkDevices] = useState([]);\n  const [isDiscovering, setIsDiscovering] = useState(false);\n\n  // Sync Settings - matching Android app\n  const [syncSettings, setSyncSettings] = useState({\n    autoSync: true,\n    syncDelay: 2,\n    syncOnConnect: true,\n    bidirectional: true\n  });\n\n  // Device Management - matching Android app\n  const [pairedDevices, setPairedDevices] = useState([{\n    id: 'android-1',\n    name: 'Android Phone - Personal',\n    type: 'Android 14',\n    status: 'connected',\n    lastSeen: '2 min ago',\n    ipAddress: '*************'\n  }, {\n    id: 'linux-1',\n    name: 'Ubuntu Server - Home',\n    type: 'Ubuntu 22.04',\n    status: 'disconnected',\n    lastSeen: '1 hour ago',\n    ipAddress: '*************'\n  }]);\n  const [discoveredDevices] = useState([{\n    id: 'johns-laptop',\n    name: 'John\\'s Laptop',\n    type: 'Windows 10',\n    status: 'discovering',\n    lastSeen: 'Available for pairing',\n    ipAddress: '*************'\n  }, {\n    id: 'sarahs-desktop',\n    name: 'Sarah\\'s Desktop',\n    type: 'Ubuntu 22.04',\n    status: 'discovering',\n    lastSeen: 'Available for pairing',\n    ipAddress: '*************'\n  }]);\n\n  // Device info state - get actual Windows device details\n  const [deviceInfo, setDeviceInfo] = useState({\n    name: 'Windows PC - Loading...',\n    type: 'Windows',\n    status: 'active',\n    lastSeen: 'now',\n    ipAddress: '*************'\n  });\n\n  // Get actual device name and OS details\n  React.useEffect(() => {\n    const getDeviceInfo = async () => {\n      try {\n        // Get device name from various sources\n        let deviceName = 'Windows PC';\n        let osVersion = 'Windows';\n\n        // Try to get computer name from environment or navigator\n        if (navigator.userAgentData) {\n          const platform = navigator.userAgentData.platform;\n          osVersion = platform || 'Windows';\n        } else if (navigator.userAgent) {\n          // Parse user agent for Windows version\n          const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\n          if (windowsMatch) {\n            const version = windowsMatch[1];\n            switch (version) {\n              case '10.0':\n                osVersion = 'Windows 10/11';\n                break;\n              case '6.3':\n                osVersion = 'Windows 8.1';\n                break;\n              case '6.2':\n                osVersion = 'Windows 8';\n                break;\n              case '6.1':\n                osVersion = 'Windows 7';\n                break;\n              default:\n                osVersion = `Windows NT ${version}`;\n            }\n          }\n        }\n\n        // Try to get hostname if available\n        try {\n          if (window.location.hostname && window.location.hostname !== 'localhost') {\n            deviceName = `${window.location.hostname} - Windows PC`;\n          } else {\n            // Use a more descriptive name based on OS\n            deviceName = `${osVersion} PC - Main`;\n          }\n        } catch (e) {\n          deviceName = `${osVersion} PC - Main`;\n        }\n\n        // Check if any devices are connected\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\n        setDeviceInfo(prev => ({\n          ...prev,\n          name: deviceName,\n          type: osVersion,\n          status: deviceStatus,\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\n        }));\n      } catch (error) {\n        console.log('Could not get device info:', error);\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\n        setDeviceInfo(prev => ({\n          ...prev,\n          name: 'Windows PC - Main',\n          type: 'Windows',\n          status: deviceStatus,\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\n        }));\n      }\n    };\n    getDeviceInfo();\n  }, [pairedDevices]); // Changed dependency from connectionStatus to pairedDevices\n\n  // Functions\n  const showMessage = text => {\n    setSuccessMessage(text);\n    setTimeout(() => setSuccessMessage(''), 3000);\n  };\n  const copyToClipboard = async content => {\n    try {\n      await navigator.clipboard.writeText(content);\n      setThisDeviceClipboard(content);\n      showMessage('✅ Text copied to clipboard!');\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error);\n      showMessage('❌ Failed to copy to clipboard');\n    }\n  };\n  const selectItem = item => {\n    copyToClipboard(item.content);\n    setConnectedDeviceClipboard(item.content);\n  };\n  const deleteHistoryItem = itemId => {\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\n    setHistoryItems(updatedHistory);\n    showMessage('🗑️ History item deleted!');\n  };\n\n  // Device edit functions\n  const startEditingThisDevice = () => {\n    setEditingThisDeviceText(thisDeviceClipboard);\n    setIsEditingThisDevice(true);\n  };\n  const saveThisDeviceEdit = async () => {\n    const newContent = editingThisDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setThisDeviceClipboard(newContent);\n    try {\n      await navigator.clipboard.writeText(newContent);\n    } catch (error) {\n      console.warn('Failed to update clipboard:', error);\n    }\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n    showMessage('✅ This Device clipboard updated!');\n  };\n  const cancelThisDeviceEdit = () => {\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n  };\n  const startEditingConnectedDevice = () => {\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\n    setIsEditingConnectedDevice(true);\n  };\n  const saveConnectedDeviceEdit = () => {\n    const newContent = editingConnectedDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setConnectedDeviceClipboard(newContent);\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n    showMessage('✅ Connected Device clipboard updated!');\n  };\n  const cancelConnectedDeviceEdit = () => {\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n  };\n  const syncNow = () => {\n    showMessage('🔄 Syncing with paired devices...');\n    setTimeout(() => {\n      showMessage('✅ Sync completed!');\n    }, 1000);\n  };\n  const toggleAlwaysOnTop = () => {\n    setIsAlwaysOnTop(!isAlwaysOnTop);\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\n  };\n  const minimizeToTray = () => {\n    showMessage('➖ Minimizing to background...');\n  };\n  const toggleFloatingOverlay = () => {\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\n  };\n\n  // Device Management Functions\n  const removeDevice = deviceId => {\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\n    setPairedDevices(updatedDevices);\n    showMessage('🗑️ Device removed from paired devices');\n  };\n  const connectDevice = deviceId => {\n    const updatedDevices = pairedDevices.map(device => device.id === deviceId ? {\n      ...device,\n      status: 'connected',\n      lastSeen: 'Just now'\n    } : device);\n    setPairedDevices(updatedDevices);\n    showMessage('✅ Device connected successfully');\n  };\n  const disconnectDevice = deviceId => {\n    const updatedDevices = pairedDevices.map(device => device.id === deviceId ? {\n      ...device,\n      status: 'disconnected',\n      lastSeen: 'Just now'\n    } : device);\n    setPairedDevices(updatedDevices);\n    showMessage('🔌 Device disconnected');\n  };\n  const pairDevice = deviceId => {\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\n    if (deviceToPair) {\n      const newPairedDevice = {\n        ...deviceToPair,\n        status: 'connected',\n        lastSeen: 'Just now'\n      };\n      setPairedDevices([...pairedDevices, newPairedDevice]);\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\n    }\n  };\n  const refreshDiscovery = () => {\n    if (isDiscovering) return;\n    setIsDiscovering(true);\n    showMessage('🔍 Scanning for devices...');\n\n    // Simulate discovery process\n    setTimeout(() => {\n      setNetworkDevices(discoveredDevices);\n      setIsDiscovering(false);\n      showMessage(`📱 Found ${discoveredDevices.length} available devices`);\n    }, 2000);\n  };\n  const scanQRCode = () => {\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\n  };\n  const generateQRCode = () => {\n    // Generate connection info for QR code\n    const connectionInfo = {\n      deviceName: deviceInfo.name,\n      deviceType: deviceInfo.type,\n      ipAddress: deviceInfo.ipAddress,\n      port: 3001,\n      protocol: 'clipsy-sync',\n      timestamp: Date.now()\n    };\n    const qrData = JSON.stringify(connectionInfo);\n    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrData)}`;\n\n    // Create and show QR code modal\n    const qrModal = document.createElement('div');\n    qrModal.className = 'qr-modal-overlay';\n    qrModal.innerHTML = `\n      <div class=\"qr-modal-content\">\n        <div class=\"qr-modal-header\">\n          <h3>📱 Scan to Connect Android Device</h3>\n          <button class=\"qr-modal-close\">✕</button>\n        </div>\n        <div class=\"qr-modal-body\">\n          <img src=\"${qrCodeUrl}\" alt=\"QR Code\" class=\"qr-code-image\" />\n          <p class=\"qr-instructions\">\n            1. Open Clipsy app on your Android device<br/>\n            2. Go to Settings → Device Discovery<br/>\n            3. Tap \"Scan QR\" and scan this code<br/>\n            4. Your devices will be paired automatically\n          </p>\n          <div class=\"qr-device-info\">\n            <p><strong>Device:</strong> ${deviceInfo.name}</p>\n            <p><strong>IP:</strong> ${deviceInfo.ipAddress}</p>\n          </div>\n        </div>\n      </div>\n    `;\n    document.body.appendChild(qrModal);\n\n    // Close modal functionality\n    const closeModal = () => {\n      document.body.removeChild(qrModal);\n    };\n    qrModal.querySelector('.qr-modal-close').onclick = closeModal;\n    qrModal.onclick = e => {\n      if (e.target === qrModal) closeModal();\n    };\n    showMessage('📱 QR Code generated! Scan with Android Clipsy app to connect.');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"title-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/clipsy-logo-no-bg.png\",\n            alt: \"Clipsy Logo\",\n            className: \"app-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: \"Clipsy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isFloatingOverlayVisible ? 'active' : ''}`,\n          onClick: toggleFloatingOverlay,\n          title: \"Toggle floating clipboard widget\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isAlwaysOnTop ? 'active' : ''}`,\n          onClick: toggleAlwaysOnTop,\n          title: \"Pin to top\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pin-icon\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-head\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-body\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: minimizeToTray,\n          title: \"Minimize\",\n          children: \"\\u2796\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: () => setShowSettings(true),\n          title: \"Settings\",\n          children: \"\\u2699\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"device-section-title-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"device-section-title\",\n              children: \"\\uD83D\\uDCBB This Device\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"device-section-subtitle\",\n              children: deviceInfo.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), isEditingThisDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingThisDeviceText,\n            onChange: e => setEditingThisDeviceText(e.target.value),\n            placeholder: \"Edit this device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button this-device\",\n              onClick: saveThisDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button this-device\",\n              onClick: cancelThisDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content this-device-content\",\n            onClick: () => copyToClipboard(thisDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: thisDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Real-time sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingThisDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"device-section-title\",\n            children: \"\\uD83D\\uDD17 Connected Device\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"device-section-subtitle\",\n            children: \"Android Phone - Personal \\uD83D\\uDFE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), isEditingConnectedDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingConnectedDeviceText,\n            onChange: e => setEditingConnectedDeviceText(e.target.value),\n            placeholder: \"Edit connected device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button connected-device\",\n              onClick: saveConnectedDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button connected-device\",\n              onClick: cancelConnectedDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content connected-device-content\",\n            onClick: () => copyToClipboard(connectedDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: connectedDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Bidirectional sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingConnectedDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"history-title\",\n        children: \"Clipboard History\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-list\",\n        children: historyItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-item\",\n          onClick: () => selectItem(item),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-item-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"timestamp\",\n              children: item.timestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"delete-button\",\n              onClick: e => {\n                e.stopPropagation();\n                deleteHistoryItem(item.id);\n              },\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"item-content\",\n            children: item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"floating-sync-button\",\n      onClick: syncNow,\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/sync.png\",\n        alt: \"Sync\",\n        className: \"sync-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 7\n    }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowSettings(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-sidebar\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-title\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: () => setShowSettings(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Device Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"device-info-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-name\",\n                children: deviceInfo.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail\",\n                children: deviceInfo.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail\",\n                children: [\"IP: \", deviceInfo.ipAddress]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-status-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `device-status-indicator ${deviceInfo.status}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `device-status-text ${deviceInfo.status}`,\n                  children: [\"Status: \", deviceInfo.status === 'active' ? 'Connected' : 'Disconnected']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this), deviceInfo.status === 'disconnected' && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail disconnected-notice\",\n                children: \"\\u26A0\\uFE0F No devices connected - Use QR code or device discovery to connect Android devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Paired Devices\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 17\n            }, this), pairedDevices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-device-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-text\",\n                children: \"No paired devices found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-subtext\",\n                children: \"Use QR code or device discovery to pair devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 19\n            }, this) : pairedDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"enhanced-device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"remove-button-top-right\",\n                onClick: () => removeDevice(device.id),\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"device-status-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `device-status-indicator ${device.status}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `device-status-text ${device.status}`,\n                    children: [device.status === 'connected' ? 'Connected' : 'Disconnected', \" \\u2022 \", device.lastSeen]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`,\n                onClick: () => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id),\n                children: device.status === 'connected' ? 'Disconnect' : 'Connect'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 23\n              }, this)]\n            }, device.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 21\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"section-title\",\n                children: \"Device Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`,\n                onClick: refreshDiscovery,\n                disabled: isDiscovering,\n                children: isDiscovering ? '🔍 Scanning...' : 'Discover'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this), networkDevices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-device-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-text\",\n                children: \"No devices found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-subtext\",\n                children: isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 19\n            }, this) : networkDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"discovered-device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-last-seen\",\n                  children: device.lastSeen\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"pair-button\",\n                onClick: () => pairDevice(device.id),\n                children: \"Pair\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 23\n              }, this)]\n            }, device.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 21\n            }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qr-buttons-container\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"qr-generate-button\",\n                onClick: generateQRCode,\n                children: \"Generate QR\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Additional Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => setShowSyncSettings(true),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Sync Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-arrow\",\n                children: \"\\u203A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => {\n                const newValue = !backgroundSyncEnabled;\n                setBackgroundSyncEnabled(newValue);\n                showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Background Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-value\",\n                children: backgroundSyncEnabled ? 'on' : 'off'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => {\n                if (isDiscovering) {\n                  setIsDiscovering(false);\n                  showMessage('Network discovery stopped');\n                } else {\n                  setIsDiscovering(true);\n                  showMessage('Network discovery started');\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Network Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-value\",\n                children: isDiscovering ? 'on' : 'off'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 9\n    }, this), showSyncSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowSyncSettings(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sync-settings-modal\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-title\",\n            children: \"Sync Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: () => setShowSyncSettings(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Auto Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.autoSync,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  autoSync: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: [\"Sync Delay: \", syncSettings.syncDelay, \" seconds\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"sync-setting-description\",\n                children: syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sync-delay-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"sync-delay-button\",\n                  onClick: () => setSyncSettings({\n                    ...syncSettings,\n                    syncDelay: Math.max(0, syncSettings.syncDelay - 1)\n                  }),\n                  children: \"-\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sync-delay-value\",\n                  children: [syncSettings.syncDelay, \"s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"sync-delay-button\",\n                  onClick: () => setSyncSettings({\n                    ...syncSettings,\n                    syncDelay: Math.min(30, syncSettings.syncDelay + 1)\n                  }),\n                  children: \"+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Sync on Connect\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.syncOnConnect,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  syncOnConnect: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Bidirectional Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.bidirectional,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  bidirectional: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.'),\n                children: \"\\uD83D\\uDCF1 Windows \\u2194 Android Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.'),\n                children: \"\\uD83D\\uDDA5\\uFE0F Windows \\u2194 Windows Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.'),\n                children: \"\\uD83D\\uDE80 Sync All Devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 787,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"settings-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"settings-section-title\",\n                children: \"\\uD83D\\uDCCB Floating Overlay Button Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"settings-description\",\n                children: \"Configure the floating overlay button for quick access to connected device clipboards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Enable Floating Overlay Button\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('📋 Floating overlay button is always enabled for accessibility'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Show Device Count Badge\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('🔢 Device count badge enabled'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 818,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Auto-hide After Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 823,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('⏱️ Auto-hide after copy enabled'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"settings-note\",\n                children: \"\\uD83D\\uDCA1 The floating overlay button (\\uD83D\\uDCCB) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 705,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 704,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 367,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"nTPZ0TWRncDLxHIgWbYHi30bIdE=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "App", "_s", "thisDeviceClipboard", "setThisDeviceClipboard", "connectedDeviceClipboard", "setConnectedDeviceClipboard", "isEditingThisDevice", "setIsEditingThisDevice", "isEditingConnectedDevice", "setIsEditingConnectedDevice", "editingThisDeviceText", "setEditingThisDeviceText", "editingConnectedDeviceText", "setEditingConnectedDeviceText", "historyItems", "setHistoryItems", "id", "content", "timestamp", "showSettings", "setShowSettings", "showSyncSettings", "setShowSyncSettings", "isAlwaysOnTop", "setIsAlwaysOnTop", "successMessage", "setSuccessMessage", "isFloatingOverlayVisible", "setIsFloatingOverlayVisible", "backgroundSyncEnabled", "setBackgroundSyncEnabled", "networkDevices", "setNetworkDevices", "isDiscovering", "setIsDiscovering", "syncSettings", "setSyncSettings", "autoSync", "syncD<PERSON>y", "syncOnConnect", "bidirectional", "pairedDevices", "setPairedDevices", "name", "type", "status", "lastSeen", "ip<PERSON><PERSON><PERSON>", "discoveredDevices", "deviceInfo", "setDeviceInfo", "getDeviceInfo", "deviceName", "osVersion", "navigator", "userAgentData", "platform", "userAgent", "windowsMatch", "match", "version", "window", "location", "hostname", "e", "hasConnectedDevices", "some", "device", "deviceStatus", "prev", "error", "console", "log", "showMessage", "text", "setTimeout", "copyToClipboard", "clipboard", "writeText", "selectItem", "item", "deleteHistoryItem", "itemId", "updatedHistory", "filter", "startEditingThisDevice", "saveThisDeviceEdit", "newContent", "trim", "warn", "cancelThisDeviceEdit", "startEditingConnectedDevice", "saveConnectedDeviceEdit", "cancelConnectedDeviceEdit", "syncNow", "toggleAlwaysOnTop", "minimizeToTray", "toggleFloatingOverlay", "removeDevice", "deviceId", "updatedDevices", "connectDevice", "map", "disconnectDevice", "pairDevice", "deviceToPair", "find", "newPairedDevice", "refreshDiscovery", "length", "scanQRCode", "generateQRCode", "connectionInfo", "deviceType", "port", "protocol", "Date", "now", "qrData", "JSON", "stringify", "qrCodeUrl", "encodeURIComponent", "qrModal", "document", "createElement", "className", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "closeModal", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "onclick", "target", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "value", "onChange", "placeholder", "autoFocus", "stopPropagation", "disabled", "newValue", "checked", "Math", "max", "min", "_c", "$RefreshReg$"], "sources": ["D:/new git/Clipsy-Windows/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  // State management matching Android app\r\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to <PERSON>lipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\r\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\r\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\r\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\r\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\r\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\r\n\r\n  const [historyItems, setHistoryItems] = useState([\r\n    { id: '1', content: 'This is an older clipboard item. It\\'s shorter.', timestamp: '2 minutes ago' },\r\n    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },\r\n    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },\r\n    { id: '4', content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }', timestamp: '5 hours ago' }\r\n  ]);\r\n\r\n  // UI State\r\n  const [showSettings, setShowSettings] = useState(false);\r\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\r\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\r\n\r\n  // Background Sync State\r\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\r\n  const [networkDevices, setNetworkDevices] = useState([]);\r\n  const [isDiscovering, setIsDiscovering] = useState(false);\r\n\r\n  // Sync Settings - matching Android app\r\n  const [syncSettings, setSyncSettings] = useState({\r\n    autoSync: true,\r\n    syncDelay: 2,\r\n    syncOnConnect: true,\r\n    bidirectional: true\r\n  });\r\n\r\n  // Device Management - matching Android app\r\n  const [pairedDevices, setPairedDevices] = useState([\r\n    {\r\n      id: 'android-1',\r\n      name: 'Android Phone - Personal',\r\n      type: 'Android 14',\r\n      status: 'connected',\r\n      lastSeen: '2 min ago',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'linux-1',\r\n      name: 'Ubuntu Server - Home',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'disconnected',\r\n      lastSeen: '1 hour ago',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  const [discoveredDevices] = useState([\r\n    {\r\n      id: 'johns-laptop',\r\n      name: 'John\\'s Laptop',\r\n      type: 'Windows 10',\r\n      status: 'discovering',\r\n      lastSeen: 'Available for pairing',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'sarahs-desktop',\r\n      name: 'Sarah\\'s Desktop',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'discovering',\r\n      lastSeen: 'Available for pairing',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  // Device info state - get actual Windows device details\r\n  const [deviceInfo, setDeviceInfo] = useState({\r\n    name: 'Windows PC - Loading...',\r\n    type: 'Windows',\r\n    status: 'active',\r\n    lastSeen: 'now',\r\n    ipAddress: '*************'\r\n  });\r\n\r\n  // Get actual device name and OS details\r\n  React.useEffect(() => {\r\n    const getDeviceInfo = async () => {\r\n      try {\r\n        // Get device name from various sources\r\n        let deviceName = 'Windows PC';\r\n        let osVersion = 'Windows';\r\n\r\n        // Try to get computer name from environment or navigator\r\n        if (navigator.userAgentData) {\r\n          const platform = navigator.userAgentData.platform;\r\n          osVersion = platform || 'Windows';\r\n        } else if (navigator.userAgent) {\r\n          // Parse user agent for Windows version\r\n          const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\r\n          if (windowsMatch) {\r\n            const version = windowsMatch[1];\r\n            switch (version) {\r\n              case '10.0': osVersion = 'Windows 10/11'; break;\r\n              case '6.3': osVersion = 'Windows 8.1'; break;\r\n              case '6.2': osVersion = 'Windows 8'; break;\r\n              case '6.1': osVersion = 'Windows 7'; break;\r\n              default: osVersion = `Windows NT ${version}`;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Try to get hostname if available\r\n        try {\r\n          if (window.location.hostname && window.location.hostname !== 'localhost') {\r\n            deviceName = `${window.location.hostname} - Windows PC`;\r\n          } else {\r\n            // Use a more descriptive name based on OS\r\n            deviceName = `${osVersion} PC - Main`;\r\n          }\r\n        } catch (e) {\r\n          deviceName = `${osVersion} PC - Main`;\r\n        }\r\n\r\n        // Check if any devices are connected\r\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\r\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\r\n\r\n        setDeviceInfo(prev => ({\r\n          ...prev,\r\n          name: deviceName,\r\n          type: osVersion,\r\n          status: deviceStatus,\r\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\r\n        }));\r\n      } catch (error) {\r\n        console.log('Could not get device info:', error);\r\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\r\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\r\n\r\n        setDeviceInfo(prev => ({\r\n          ...prev,\r\n          name: 'Windows PC - Main',\r\n          type: 'Windows',\r\n          status: deviceStatus,\r\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\r\n        }));\r\n      }\r\n    };\r\n\r\n    getDeviceInfo();\r\n  }, [pairedDevices]); // Changed dependency from connectionStatus to pairedDevices\r\n\r\n  // Functions\r\n  const showMessage = (text) => {\r\n    setSuccessMessage(text);\r\n    setTimeout(() => setSuccessMessage(''), 3000);\r\n  };\r\n\r\n  const copyToClipboard = async (content) => {\r\n    try {\r\n      await navigator.clipboard.writeText(content);\r\n      setThisDeviceClipboard(content);\r\n      showMessage('✅ Text copied to clipboard!');\r\n    } catch (error) {\r\n      console.error('Failed to copy to clipboard:', error);\r\n      showMessage('❌ Failed to copy to clipboard');\r\n    }\r\n  };\r\n\r\n  const selectItem = (item) => {\r\n    copyToClipboard(item.content);\r\n    setConnectedDeviceClipboard(item.content);\r\n  };\r\n\r\n  const deleteHistoryItem = (itemId) => {\r\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\r\n    setHistoryItems(updatedHistory);\r\n    showMessage('🗑️ History item deleted!');\r\n  };\r\n\r\n  // Device edit functions\r\n  const startEditingThisDevice = () => {\r\n    setEditingThisDeviceText(thisDeviceClipboard);\r\n    setIsEditingThisDevice(true);\r\n  };\r\n\r\n  const saveThisDeviceEdit = async () => {\r\n    const newContent = editingThisDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setThisDeviceClipboard(newContent);\r\n    try {\r\n      await navigator.clipboard.writeText(newContent);\r\n    } catch (error) {\r\n      console.warn('Failed to update clipboard:', error);\r\n    }\r\n\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n    showMessage('✅ This Device clipboard updated!');\r\n  };\r\n\r\n  const cancelThisDeviceEdit = () => {\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n  };\r\n\r\n  const startEditingConnectedDevice = () => {\r\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\r\n    setIsEditingConnectedDevice(true);\r\n  };\r\n\r\n  const saveConnectedDeviceEdit = () => {\r\n    const newContent = editingConnectedDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setConnectedDeviceClipboard(newContent);\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n    showMessage('✅ Connected Device clipboard updated!');\r\n  };\r\n\r\n  const cancelConnectedDeviceEdit = () => {\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n  };\r\n\r\n  const syncNow = () => {\r\n    showMessage('🔄 Syncing with paired devices...');\r\n    setTimeout(() => {\r\n      showMessage('✅ Sync completed!');\r\n    }, 1000);\r\n  };\r\n\r\n  const toggleAlwaysOnTop = () => {\r\n    setIsAlwaysOnTop(!isAlwaysOnTop);\r\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\r\n  };\r\n\r\n  const minimizeToTray = () => {\r\n    showMessage('➖ Minimizing to background...');\r\n  };\r\n\r\n  const toggleFloatingOverlay = () => {\r\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\r\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\r\n  };\r\n\r\n  // Device Management Functions\r\n  const removeDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🗑️ Device removed from paired devices');\r\n  };\r\n\r\n  const connectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'connected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('✅ Device connected successfully');\r\n  };\r\n\r\n  const disconnectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'disconnected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🔌 Device disconnected');\r\n  };\r\n\r\n  const pairDevice = (deviceId) => {\r\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\r\n    if (deviceToPair) {\r\n      const newPairedDevice = {\r\n        ...deviceToPair,\r\n        status: 'connected',\r\n        lastSeen: 'Just now'\r\n      };\r\n      setPairedDevices([...pairedDevices, newPairedDevice]);\r\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\r\n    }\r\n  };\r\n\r\n  const refreshDiscovery = () => {\r\n    if (isDiscovering) return;\r\n\r\n    setIsDiscovering(true);\r\n    showMessage('🔍 Scanning for devices...');\r\n\r\n    // Simulate discovery process\r\n    setTimeout(() => {\r\n      setNetworkDevices(discoveredDevices);\r\n      setIsDiscovering(false);\r\n      showMessage(`📱 Found ${discoveredDevices.length} available devices`);\r\n    }, 2000);\r\n  };\r\n\r\n  const scanQRCode = () => {\r\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\r\n  };\r\n\r\n  const generateQRCode = () => {\r\n    // Generate connection info for QR code\r\n    const connectionInfo = {\r\n      deviceName: deviceInfo.name,\r\n      deviceType: deviceInfo.type,\r\n      ipAddress: deviceInfo.ipAddress,\r\n      port: 3001,\r\n      protocol: 'clipsy-sync',\r\n      timestamp: Date.now()\r\n    };\r\n\r\n    const qrData = JSON.stringify(connectionInfo);\r\n    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrData)}`;\r\n\r\n    // Create and show QR code modal\r\n    const qrModal = document.createElement('div');\r\n    qrModal.className = 'qr-modal-overlay';\r\n    qrModal.innerHTML = `\r\n      <div class=\"qr-modal-content\">\r\n        <div class=\"qr-modal-header\">\r\n          <h3>📱 Scan to Connect Android Device</h3>\r\n          <button class=\"qr-modal-close\">✕</button>\r\n        </div>\r\n        <div class=\"qr-modal-body\">\r\n          <img src=\"${qrCodeUrl}\" alt=\"QR Code\" class=\"qr-code-image\" />\r\n          <p class=\"qr-instructions\">\r\n            1. Open Clipsy app on your Android device<br/>\r\n            2. Go to Settings → Device Discovery<br/>\r\n            3. Tap \"Scan QR\" and scan this code<br/>\r\n            4. Your devices will be paired automatically\r\n          </p>\r\n          <div class=\"qr-device-info\">\r\n            <p><strong>Device:</strong> ${deviceInfo.name}</p>\r\n            <p><strong>IP:</strong> ${deviceInfo.ipAddress}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    `;\r\n\r\n    document.body.appendChild(qrModal);\r\n\r\n    // Close modal functionality\r\n    const closeModal = () => {\r\n      document.body.removeChild(qrModal);\r\n    };\r\n\r\n    qrModal.querySelector('.qr-modal-close').onclick = closeModal;\r\n    qrModal.onclick = (e) => {\r\n      if (e.target === qrModal) closeModal();\r\n    };\r\n\r\n    showMessage('📱 QR Code generated! Scan with Android Clipsy app to connect.');\r\n  };\r\n\r\n  return (\r\n    <div className=\"app-container\">\r\n      {/* Header */}\r\n      <div className=\"header\">\r\n        <div className=\"title-container\">\r\n          <div className=\"logo-container\">\r\n            <img\r\n              src=\"/clipsy-logo-no-bg.png\"\r\n              alt=\"Clipsy Logo\"\r\n              className=\"app-logo\"\r\n            />\r\n            <div className={`connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`}></div>\r\n          </div>\r\n          <h1 className=\"title\">Clipsy</h1>\r\n        </div>\r\n        <div className=\"header-actions\">\r\n          <button\r\n            className={`icon-button ${isFloatingOverlayVisible ? 'active' : ''}`}\r\n            onClick={toggleFloatingOverlay}\r\n            title=\"Toggle floating clipboard widget\"\r\n          >\r\n            📋\r\n          </button>\r\n          <button\r\n            className={`icon-button ${isAlwaysOnTop ? 'active' : ''}`}\r\n            onClick={toggleAlwaysOnTop}\r\n            title=\"Pin to top\"\r\n          >\r\n            <div className=\"pin-icon\">\r\n              <div className=\"pin-head\"></div>\r\n              <div className=\"pin-body\"></div>\r\n            </div>\r\n          </button>\r\n          <button className=\"icon-button\" onClick={minimizeToTray} title=\"Minimize\">\r\n            ➖\r\n          </button>\r\n          <button className=\"icon-button\" onClick={() => setShowSettings(true)} title=\"Settings\">\r\n            ⚙️\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"success-message\">\r\n          <span>{successMessage}</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      <div className=\"main-content\">\r\n        {/* This Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <div className=\"device-section-title-container\">\r\n              <h2 className=\"device-section-title\">💻 This Device</h2>\r\n              <p className=\"device-section-subtitle\">{deviceInfo.name}</p>\r\n            </div>\r\n          </div>\r\n\r\n          {isEditingThisDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingThisDeviceText}\r\n                onChange={(e) => setEditingThisDeviceText(e.target.value)}\r\n                placeholder=\"Edit this device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button this-device\" onClick={saveThisDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button this-device\" onClick={cancelThisDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content this-device-content\"\r\n                onClick={() => copyToClipboard(thisDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{thisDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Real-time sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingThisDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Connected Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <h2 className=\"device-section-title\">🔗 Connected Device</h2>\r\n            <p className=\"device-section-subtitle\">Android Phone - Personal 🟢</p>\r\n          </div>\r\n\r\n          {isEditingConnectedDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingConnectedDeviceText}\r\n                onChange={(e) => setEditingConnectedDeviceText(e.target.value)}\r\n                placeholder=\"Edit connected device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button connected-device\" onClick={saveConnectedDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button connected-device\" onClick={cancelConnectedDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content connected-device-content\"\r\n                onClick={() => copyToClipboard(connectedDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{connectedDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Bidirectional sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingConnectedDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Clipboard History */}\r\n        <h2 className=\"history-title\">Clipboard History</h2>\r\n        <div className=\"history-list\">\r\n          {historyItems.map((item) => (\r\n            <div\r\n              key={item.id}\r\n              className=\"history-item\"\r\n              onClick={() => selectItem(item)}\r\n            >\r\n              <div className=\"history-item-header\">\r\n                <span className=\"timestamp\">{item.timestamp}</span>\r\n                <button\r\n                  className=\"delete-button\"\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    deleteHistoryItem(item.id);\r\n                  }}\r\n                >\r\n                  ✕\r\n                </button>\r\n              </div>\r\n              <p className=\"item-content\">{item.content}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Floating Sync Button */}\r\n      <button className=\"floating-sync-button\" onClick={syncNow}>\r\n        <img src=\"/sync.png\" alt=\"Sync\" className=\"sync-icon\" />\r\n      </button>\r\n\r\n      {/* Settings Modal */}\r\n      {showSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSettings(false)}>\r\n          <div className=\"settings-sidebar\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              {/* Device Info */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Device Info</h3>\r\n                <div className=\"device-info-card\">\r\n                  <p className=\"device-name\">{deviceInfo.name}</p>\r\n                  <p className=\"device-detail\">{deviceInfo.type}</p>\r\n                  <p className=\"device-detail\">IP: {deviceInfo.ipAddress}</p>\r\n                  <div className=\"device-status-row\">\r\n                    <div className={`device-status-indicator ${deviceInfo.status}`}></div>\r\n                    <span className={`device-status-text ${deviceInfo.status}`}>\r\n                      Status: {deviceInfo.status === 'active' ? 'Connected' : 'Disconnected'}\r\n                    </span>\r\n                  </div>\r\n                  {deviceInfo.status === 'disconnected' && (\r\n                    <p className=\"device-detail disconnected-notice\">\r\n                      ⚠️ No devices connected - Use QR code or device discovery to connect Android devices\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Paired Devices */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Paired Devices</h3>\r\n                {pairedDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No paired devices found</p>\r\n                    <p className=\"empty-device-subtext\">Use QR code or device discovery to pair devices</p>\r\n                  </div>\r\n                ) : (\r\n                  pairedDevices.map((device) => (\r\n                    <div key={device.id} className=\"enhanced-device-card\">\r\n                      {/* Remove Button - White X at top right corner */}\r\n                      <button\r\n                        className=\"remove-button-top-right\"\r\n                        onClick={() => removeDevice(device.id)}\r\n                      >\r\n                        ×\r\n                      </button>\r\n\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <div className=\"device-status-row\">\r\n                          <div className={`device-status-indicator ${device.status}`}></div>\r\n                          <span className={`device-status-text ${device.status}`}>\r\n                            {device.status === 'connected' ? 'Connected' : 'Disconnected'} • {device.lastSeen}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Connect/Disconnect Button - Bottom Right Corner */}\r\n                      <button\r\n                        className={`device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`}\r\n                        onClick={() => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id)}\r\n                      >\r\n                        {device.status === 'connected' ? 'Disconnect' : 'Connect'}\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n              </div>\r\n\r\n              {/* Device Discovery */}\r\n              <div className=\"settings-section\">\r\n                <div className=\"section-header\">\r\n                  <h3 className=\"section-title\">Device Discovery</h3>\r\n                  <button\r\n                    className={`discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`}\r\n                    onClick={refreshDiscovery}\r\n                    disabled={isDiscovering}\r\n                  >\r\n                    {isDiscovering ? '🔍 Scanning...' : 'Discover'}\r\n                  </button>\r\n                </div>\r\n                {networkDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No devices found</p>\r\n                    <p className=\"empty-device-subtext\">\r\n                      {isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'}\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  networkDevices.map((device) => (\r\n                    <div key={device.id} className=\"discovered-device-card\">\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <p className=\"device-card-last-seen\">{device.lastSeen}</p>\r\n                      </div>\r\n                      <button\r\n                        className=\"pair-button\"\r\n                        onClick={() => pairDevice(device.id)}\r\n                      >\r\n                        Pair\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n\r\n                <div className=\"qr-buttons-container\">\r\n                  <button className=\"qr-generate-button\" onClick={generateQRCode}>\r\n                    Generate QR\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Additional Settings */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Additional Settings</h3>\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => setShowSyncSettings(true)}\r\n                >\r\n                  <span className=\"setting-item-text\">Sync Settings</span>\r\n                  <span className=\"setting-item-arrow\">›</span>\r\n                </button>\r\n\r\n                {/* Background Sync Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    const newValue = !backgroundSyncEnabled;\r\n                    setBackgroundSyncEnabled(newValue);\r\n                    showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Background Sync</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {backgroundSyncEnabled ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n\r\n                {/* Network Discovery Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    if (isDiscovering) {\r\n                      setIsDiscovering(false);\r\n                      showMessage('Network discovery stopped');\r\n                    } else {\r\n                      setIsDiscovering(true);\r\n                      showMessage('Network discovery started');\r\n                    }\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Network Discovery</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {isDiscovering ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Sync Settings Modal */}\r\n      {showSyncSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSyncSettings(false)}>\r\n          <div className=\"sync-settings-modal\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Sync Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSyncSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              <div className=\"settings-section\">\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Auto Sync</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.autoSync}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, autoSync: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Sync Delay: {syncSettings.syncDelay} seconds</label>\r\n                  <p className=\"sync-setting-description\">\r\n                    {syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`}\r\n                  </p>\r\n                  <div className=\"sync-delay-controls\">\r\n                    <button\r\n                      className=\"sync-delay-button\"\r\n                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.max(0, syncSettings.syncDelay - 1)})}\r\n                    >\r\n                      -\r\n                    </button>\r\n                    <span className=\"sync-delay-value\">{syncSettings.syncDelay}s</span>\r\n                    <button\r\n                      className=\"sync-delay-button\"\r\n                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.min(30, syncSettings.syncDelay + 1)})}\r\n                    >\r\n                      +\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Sync on Connect</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.syncOnConnect}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, syncOnConnect: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Bidirectional Sync</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.bidirectional}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, bidirectional: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                {/* Cross-Platform Sync Controls */}\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.')}\r\n                  >\r\n                    📱 Windows ↔ Android Sync\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.')}\r\n                  >\r\n                    🖥️ Windows ↔ Windows Sync\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.')}\r\n                  >\r\n                    🚀 Sync All Devices\r\n                  </button>\r\n                </div>\r\n\r\n                {/* Floating Overlay Button Settings */}\r\n                <div className=\"settings-section\">\r\n                  <h3 className=\"settings-section-title\">📋 Floating Overlay Button Settings</h3>\r\n                  <p className=\"settings-description\">\r\n                    Configure the floating overlay button for quick access to connected device clipboards\r\n                  </p>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Enable Floating Overlay Button</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('📋 Floating overlay button is always enabled for accessibility')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Show Device Count Badge</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('🔢 Device count badge enabled')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Auto-hide After Copy</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('⏱️ Auto-hide after copy enabled')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <p className=\"settings-note\">\r\n                    💡 The floating overlay button (📋) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGP,QAAQ,CAAC,sIAAsI,CAAC;EACtM,MAAM,CAACQ,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGT,QAAQ,CAAC,0IAA0I,CAAC;EACpN,MAAM,CAACU,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACY,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACc,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACgB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEhF,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAC/C;IAAEoB,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,iDAAiD;IAAEC,SAAS,EAAE;EAAgB,CAAC,EACnG;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,gLAAgL;IAAEC,SAAS,EAAE;EAAiB,CAAC,EACnO;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,+BAA+B;IAAEC,SAAS,EAAE;EAAa,CAAC,EAC9E;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,sEAAsE;IAAEC,SAAS,EAAE;EAAc,CAAC,CACvH,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+B,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;;EAE/E;EACA,MAAM,CAACiC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC;IAC/CyC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,CACjD;IACEoB,EAAE,EAAE,WAAW;IACf2B,IAAI,EAAE,0BAA0B;IAChCC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,SAAS;IACb2B,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,cAAc;IACtBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,CACnC;IACEoB,EAAE,EAAE,cAAc;IAClB2B,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,uBAAuB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,gBAAgB;IACpB2B,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,uBAAuB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC;IAC3C+C,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACApD,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAMsD,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF;QACA,IAAIC,UAAU,GAAG,YAAY;QAC7B,IAAIC,SAAS,GAAG,SAAS;;QAEzB;QACA,IAAIC,SAAS,CAACC,aAAa,EAAE;UAC3B,MAAMC,QAAQ,GAAGF,SAAS,CAACC,aAAa,CAACC,QAAQ;UACjDH,SAAS,GAAGG,QAAQ,IAAI,SAAS;QACnC,CAAC,MAAM,IAAIF,SAAS,CAACG,SAAS,EAAE;UAC9B;UACA,MAAMC,YAAY,GAAGJ,SAAS,CAACG,SAAS,CAACE,KAAK,CAAC,uBAAuB,CAAC;UACvE,IAAID,YAAY,EAAE;YAChB,MAAME,OAAO,GAAGF,YAAY,CAAC,CAAC,CAAC;YAC/B,QAAQE,OAAO;cACb,KAAK,MAAM;gBAAEP,SAAS,GAAG,eAAe;gBAAE;cAC1C,KAAK,KAAK;gBAAEA,SAAS,GAAG,aAAa;gBAAE;cACvC,KAAK,KAAK;gBAAEA,SAAS,GAAG,WAAW;gBAAE;cACrC,KAAK,KAAK;gBAAEA,SAAS,GAAG,WAAW;gBAAE;cACrC;gBAASA,SAAS,GAAG,cAAcO,OAAO,EAAE;YAC9C;UACF;QACF;;QAEA;QACA,IAAI;UACF,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,IAAIF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,EAAE;YACxEX,UAAU,GAAG,GAAGS,MAAM,CAACC,QAAQ,CAACC,QAAQ,eAAe;UACzD,CAAC,MAAM;YACL;YACAX,UAAU,GAAG,GAAGC,SAAS,YAAY;UACvC;QACF,CAAC,CAAC,OAAOW,CAAC,EAAE;UACVZ,UAAU,GAAG,GAAGC,SAAS,YAAY;QACvC;;QAEA;QACA,MAAMY,mBAAmB,GAAGxB,aAAa,CAACyB,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACtB,MAAM,KAAK,WAAW,CAAC;QACvF,MAAMuB,YAAY,GAAGH,mBAAmB,GAAG,QAAQ,GAAG,cAAc;QAEpEf,aAAa,CAACmB,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP1B,IAAI,EAAES,UAAU;UAChBR,IAAI,EAAES,SAAS;UACfR,MAAM,EAAEuB,YAAY;UACpBtB,QAAQ,EAAEsB,YAAY,KAAK,QAAQ,GAAG,KAAK,GAAG;QAChD,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,KAAK,CAAC;QAChD,MAAML,mBAAmB,GAAGxB,aAAa,CAACyB,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACtB,MAAM,KAAK,WAAW,CAAC;QACvF,MAAMuB,YAAY,GAAGH,mBAAmB,GAAG,QAAQ,GAAG,cAAc;QAEpEf,aAAa,CAACmB,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP1B,IAAI,EAAE,mBAAmB;UACzBC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAEuB,YAAY;UACpBtB,QAAQ,EAAEsB,YAAY,KAAK,QAAQ,GAAG,KAAK,GAAG;QAChD,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAEDjB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACV,aAAa,CAAC,CAAC,CAAC,CAAC;;EAErB;EACA,MAAMgC,WAAW,GAAIC,IAAI,IAAK;IAC5BhD,iBAAiB,CAACgD,IAAI,CAAC;IACvBC,UAAU,CAAC,MAAMjD,iBAAiB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;EAED,MAAMkD,eAAe,GAAG,MAAO3D,OAAO,IAAK;IACzC,IAAI;MACF,MAAMqC,SAAS,CAACuB,SAAS,CAACC,SAAS,CAAC7D,OAAO,CAAC;MAC5Cd,sBAAsB,CAACc,OAAO,CAAC;MAC/BwD,WAAW,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDG,WAAW,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMM,UAAU,GAAIC,IAAI,IAAK;IAC3BJ,eAAe,CAACI,IAAI,CAAC/D,OAAO,CAAC;IAC7BZ,2BAA2B,CAAC2E,IAAI,CAAC/D,OAAO,CAAC;EAC3C,CAAC;EAED,MAAMgE,iBAAiB,GAAIC,MAAM,IAAK;IACpC,MAAMC,cAAc,GAAGrE,YAAY,CAACsE,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAAChE,EAAE,KAAKkE,MAAM,CAAC;IACtEnE,eAAe,CAACoE,cAAc,CAAC;IAC/BV,WAAW,CAAC,2BAA2B,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMY,sBAAsB,GAAGA,CAAA,KAAM;IACnC1E,wBAAwB,CAACT,mBAAmB,CAAC;IAC7CK,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM+E,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,UAAU,GAAG7E,qBAAqB,CAAC8E,IAAI,CAAC,CAAC;IAC/C,IAAI,CAACD,UAAU,EAAE;MACfd,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEAtE,sBAAsB,CAACoF,UAAU,CAAC;IAClC,IAAI;MACF,MAAMjC,SAAS,CAACuB,SAAS,CAACC,SAAS,CAACS,UAAU,CAAC;IACjD,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACkB,IAAI,CAAC,6BAA6B,EAAEnB,KAAK,CAAC;IACpD;IAEA/D,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;IAC5B8D,WAAW,CAAC,kCAAkC,CAAC;EACjD,CAAC;EAED,MAAMiB,oBAAoB,GAAGA,CAAA,KAAM;IACjCnF,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;EAC9B,CAAC;EAED,MAAMgF,2BAA2B,GAAGA,CAAA,KAAM;IACxC9E,6BAA6B,CAACT,wBAAwB,CAAC;IACvDK,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMmF,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAML,UAAU,GAAG3E,0BAA0B,CAAC4E,IAAI,CAAC,CAAC;IACpD,IAAI,CAACD,UAAU,EAAE;MACfd,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEApE,2BAA2B,CAACkF,UAAU,CAAC;IACvC9E,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;IACjC4D,WAAW,CAAC,uCAAuC,CAAC;EACtD,CAAC;EAED,MAAMoB,yBAAyB,GAAGA,CAAA,KAAM;IACtCpF,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;EACnC,CAAC;EAED,MAAMiF,OAAO,GAAGA,CAAA,KAAM;IACpBrB,WAAW,CAAC,mCAAmC,CAAC;IAChDE,UAAU,CAAC,MAAM;MACfF,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvE,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChCkD,WAAW,CAAClD,aAAa,GAAG,0BAA0B,GAAG,sBAAsB,CAAC;EAClF,CAAC;EAED,MAAMyE,cAAc,GAAGA,CAAA,KAAM;IAC3BvB,WAAW,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAMwB,qBAAqB,GAAGA,CAAA,KAAM;IAClCrE,2BAA2B,CAAC,CAACD,wBAAwB,CAAC;IACtD8C,WAAW,CAAC9C,wBAAwB,GAAG,2BAA2B,GAAG,0BAA0B,CAAC;EAClG,CAAC;;EAED;EACA,MAAMuE,YAAY,GAAIC,QAAQ,IAAK;IACjC,MAAMC,cAAc,GAAG3D,aAAa,CAAC2C,MAAM,CAACjB,MAAM,IAAIA,MAAM,CAACnD,EAAE,KAAKmF,QAAQ,CAAC;IAC7EzD,gBAAgB,CAAC0D,cAAc,CAAC;IAChC3B,WAAW,CAAC,wCAAwC,CAAC;EACvD,CAAC;EAED,MAAM4B,aAAa,GAAIF,QAAQ,IAAK;IAClC,MAAMC,cAAc,GAAG3D,aAAa,CAAC6D,GAAG,CAACnC,MAAM,IAC7CA,MAAM,CAACnD,EAAE,KAAKmF,QAAQ,GAAG;MAAE,GAAGhC,MAAM;MAAEtB,MAAM,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAW,CAAC,GAAGqB,MACtF,CAAC;IACDzB,gBAAgB,CAAC0D,cAAc,CAAC;IAChC3B,WAAW,CAAC,iCAAiC,CAAC;EAChD,CAAC;EAED,MAAM8B,gBAAgB,GAAIJ,QAAQ,IAAK;IACrC,MAAMC,cAAc,GAAG3D,aAAa,CAAC6D,GAAG,CAACnC,MAAM,IAC7CA,MAAM,CAACnD,EAAE,KAAKmF,QAAQ,GAAG;MAAE,GAAGhC,MAAM;MAAEtB,MAAM,EAAE,cAAc;MAAEC,QAAQ,EAAE;IAAW,CAAC,GAAGqB,MACzF,CAAC;IACDzB,gBAAgB,CAAC0D,cAAc,CAAC;IAChC3B,WAAW,CAAC,wBAAwB,CAAC;EACvC,CAAC;EAED,MAAM+B,UAAU,GAAIL,QAAQ,IAAK;IAC/B,MAAMM,YAAY,GAAGzD,iBAAiB,CAAC0D,IAAI,CAACvC,MAAM,IAAIA,MAAM,CAACnD,EAAE,KAAKmF,QAAQ,CAAC;IAC7E,IAAIM,YAAY,EAAE;MAChB,MAAME,eAAe,GAAG;QACtB,GAAGF,YAAY;QACf5D,MAAM,EAAE,WAAW;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDJ,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAEkE,eAAe,CAAC,CAAC;MACrDlC,WAAW,CAAC,8BAA8BgC,YAAY,CAAC9D,IAAI,EAAE,CAAC;IAChE;EACF,CAAC;EAED,MAAMiE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI3E,aAAa,EAAE;IAEnBC,gBAAgB,CAAC,IAAI,CAAC;IACtBuC,WAAW,CAAC,4BAA4B,CAAC;;IAEzC;IACAE,UAAU,CAAC,MAAM;MACf3C,iBAAiB,CAACgB,iBAAiB,CAAC;MACpCd,gBAAgB,CAAC,KAAK,CAAC;MACvBuC,WAAW,CAAC,YAAYzB,iBAAiB,CAAC6D,MAAM,oBAAoB,CAAC;IACvE,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBrC,WAAW,CAAC,2EAA2E,CAAC;EAC1F,CAAC;EAED,MAAMsC,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAMC,cAAc,GAAG;MACrB5D,UAAU,EAAEH,UAAU,CAACN,IAAI;MAC3BsE,UAAU,EAAEhE,UAAU,CAACL,IAAI;MAC3BG,SAAS,EAAEE,UAAU,CAACF,SAAS;MAC/BmE,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,aAAa;MACvBjG,SAAS,EAAEkG,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,MAAMC,MAAM,GAAGC,IAAI,CAACC,SAAS,CAACR,cAAc,CAAC;IAC7C,MAAMS,SAAS,GAAG,iEAAiEC,kBAAkB,CAACJ,MAAM,CAAC,EAAE;;IAE/G;IACA,MAAMK,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7CF,OAAO,CAACG,SAAS,GAAG,kBAAkB;IACtCH,OAAO,CAACI,SAAS,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBN,SAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0CxE,UAAU,CAACN,IAAI;AACzD,sCAAsCM,UAAU,CAACF,SAAS;AAC1D;AACA;AACA;AACA,KAAK;IAED6E,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,OAAO,CAAC;;IAElC;IACA,MAAMO,UAAU,GAAGA,CAAA,KAAM;MACvBN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,OAAO,CAAC;IACpC,CAAC;IAEDA,OAAO,CAACS,aAAa,CAAC,iBAAiB,CAAC,CAACC,OAAO,GAAGH,UAAU;IAC7DP,OAAO,CAACU,OAAO,GAAIrE,CAAC,IAAK;MACvB,IAAIA,CAAC,CAACsE,MAAM,KAAKX,OAAO,EAAEO,UAAU,CAAC,CAAC;IACxC,CAAC;IAEDzD,WAAW,CAAC,gEAAgE,CAAC;EAC/E,CAAC;EAED,oBACE1E,OAAA;IAAK+H,SAAS,EAAC,eAAe;IAAAS,QAAA,gBAE5BxI,OAAA;MAAK+H,SAAS,EAAC,QAAQ;MAAAS,QAAA,gBACrBxI,OAAA;QAAK+H,SAAS,EAAC,iBAAiB;QAAAS,QAAA,gBAC9BxI,OAAA;UAAK+H,SAAS,EAAC,gBAAgB;UAAAS,QAAA,gBAC7BxI,OAAA;YACEyI,GAAG,EAAC,wBAAwB;YAC5BC,GAAG,EAAC,aAAa;YACjBX,SAAS,EAAC;UAAU;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACF9I,OAAA;YAAK+H,SAAS,EAAE,kBAAkBrF,aAAa,CAACyB,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACtB,MAAM,KAAK,WAAW,CAAC,GAAG,WAAW,GAAG,cAAc;UAAG;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnI,CAAC,eACN9I,OAAA;UAAI+H,SAAS,EAAC,OAAO;UAAAS,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACN9I,OAAA;QAAK+H,SAAS,EAAC,gBAAgB;QAAAS,QAAA,gBAC7BxI,OAAA;UACE+H,SAAS,EAAE,eAAenG,wBAAwB,GAAG,QAAQ,GAAG,EAAE,EAAG;UACrEmH,OAAO,EAAE7C,qBAAsB;UAC/B8C,KAAK,EAAC,kCAAkC;UAAAR,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9I,OAAA;UACE+H,SAAS,EAAE,eAAevG,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC1DuH,OAAO,EAAE/C,iBAAkB;UAC3BgD,KAAK,EAAC,YAAY;UAAAR,QAAA,eAElBxI,OAAA;YAAK+H,SAAS,EAAC,UAAU;YAAAS,QAAA,gBACvBxI,OAAA;cAAK+H,SAAS,EAAC;YAAU;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChC9I,OAAA;cAAK+H,SAAS,EAAC;YAAU;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACT9I,OAAA;UAAQ+H,SAAS,EAAC,aAAa;UAACgB,OAAO,EAAE9C,cAAe;UAAC+C,KAAK,EAAC,UAAU;UAAAR,QAAA,EAAC;QAE1E;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9I,OAAA;UAAQ+H,SAAS,EAAC,aAAa;UAACgB,OAAO,EAAEA,CAAA,KAAM1H,eAAe,CAAC,IAAI,CAAE;UAAC2H,KAAK,EAAC,UAAU;UAAAR,QAAA,EAAC;QAEvF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLpH,cAAc,iBACb1B,OAAA;MAAK+H,SAAS,EAAC,iBAAiB;MAAAS,QAAA,eAC9BxI,OAAA;QAAAwI,QAAA,EAAO9G;MAAc;QAAAiH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGD9I,OAAA;MAAK+H,SAAS,EAAC,cAAc;MAAAS,QAAA,gBAE3BxI,OAAA;QAAK+H,SAAS,EAAC,gBAAgB;QAAAS,QAAA,gBAC7BxI,OAAA;UAAK+H,SAAS,EAAC,uBAAuB;UAAAS,QAAA,eACpCxI,OAAA;YAAK+H,SAAS,EAAC,gCAAgC;YAAAS,QAAA,gBAC7CxI,OAAA;cAAI+H,SAAS,EAAC,sBAAsB;cAAAS,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD9I,OAAA;cAAG+H,SAAS,EAAC,yBAAyB;cAAAS,QAAA,EAAEtF,UAAU,CAACN;YAAI;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELvI,mBAAmB,gBAClBP,OAAA;UAAK+H,SAAS,EAAC,gBAAgB;UAAAS,QAAA,gBAC7BxI,OAAA;YACE+H,SAAS,EAAC,iBAAiB;YAC3BkB,KAAK,EAAEtI,qBAAsB;YAC7BuI,QAAQ,EAAGjF,CAAC,IAAKrD,wBAAwB,CAACqD,CAAC,CAACsE,MAAM,CAACU,KAAK,CAAE;YAC1DE,WAAW,EAAC,uCAAuC;YACnDC,SAAS;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACF9I,OAAA;YAAK+H,SAAS,EAAC,cAAc;YAAAS,QAAA,gBAC3BxI,OAAA;cAAQ+H,SAAS,EAAC,yBAAyB;cAACgB,OAAO,EAAExD,kBAAmB;cAAAiD,QAAA,EAAC;YAEzE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9I,OAAA;cAAQ+H,SAAS,EAAC,2BAA2B;cAACgB,OAAO,EAAEpD,oBAAqB;cAAA6C,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN9I,OAAA;UAAK+H,SAAS,EAAC,kBAAkB;UAAAS,QAAA,eAC/BxI,OAAA;YACE+H,SAAS,EAAC,uCAAuC;YACjDgB,OAAO,EAAEA,CAAA,KAAMlE,eAAe,CAAC1E,mBAAmB,CAAE;YAAAqI,QAAA,gBAEpDxI,OAAA;cAAG+H,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAErI;YAAmB;cAAAwI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvD9I,OAAA;cAAG+H,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAC;YAA8B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChE9I,OAAA;cAAQ+H,SAAS,EAAC,oBAAoB;cAACgB,OAAO,EAAEzD,sBAAuB;cAAAkD,QAAA,EAAC;YAExE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN9I,OAAA;QAAK+H,SAAS,EAAC,gBAAgB;QAAAS,QAAA,gBAC7BxI,OAAA;UAAK+H,SAAS,EAAC,uBAAuB;UAAAS,QAAA,gBACpCxI,OAAA;YAAI+H,SAAS,EAAC,sBAAsB;YAAAS,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7D9I,OAAA;YAAG+H,SAAS,EAAC,yBAAyB;YAAAS,QAAA,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,EAELrI,wBAAwB,gBACvBT,OAAA;UAAK+H,SAAS,EAAC,gBAAgB;UAAAS,QAAA,gBAC7BxI,OAAA;YACE+H,SAAS,EAAC,iBAAiB;YAC3BkB,KAAK,EAAEpI,0BAA2B;YAClCqI,QAAQ,EAAGjF,CAAC,IAAKnD,6BAA6B,CAACmD,CAAC,CAACsE,MAAM,CAACU,KAAK,CAAE;YAC/DE,WAAW,EAAC,4CAA4C;YACxDC,SAAS;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACF9I,OAAA;YAAK+H,SAAS,EAAC,cAAc;YAAAS,QAAA,gBAC3BxI,OAAA;cAAQ+H,SAAS,EAAC,8BAA8B;cAACgB,OAAO,EAAElD,uBAAwB;cAAA2C,QAAA,EAAC;YAEnF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9I,OAAA;cAAQ+H,SAAS,EAAC,gCAAgC;cAACgB,OAAO,EAAEjD,yBAA0B;cAAA0C,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN9I,OAAA;UAAK+H,SAAS,EAAC,kBAAkB;UAAAS,QAAA,eAC/BxI,OAAA;YACE+H,SAAS,EAAC,4CAA4C;YACtDgB,OAAO,EAAEA,CAAA,KAAMlE,eAAe,CAACxE,wBAAwB,CAAE;YAAAmI,QAAA,gBAEzDxI,OAAA;cAAG+H,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAEnI;YAAwB;cAAAsI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5D9I,OAAA;cAAG+H,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAC;YAAkC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpE9I,OAAA;cAAQ+H,SAAS,EAAC,oBAAoB;cAACgB,OAAO,EAAEnD,2BAA4B;cAAA4C,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN9I,OAAA;QAAI+H,SAAS,EAAC,eAAe;QAAAS,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpD9I,OAAA;QAAK+H,SAAS,EAAC,cAAc;QAAAS,QAAA,EAC1BzH,YAAY,CAACwF,GAAG,CAAEtB,IAAI,iBACrBjF,OAAA;UAEE+H,SAAS,EAAC,cAAc;UACxBgB,OAAO,EAAEA,CAAA,KAAM/D,UAAU,CAACC,IAAI,CAAE;UAAAuD,QAAA,gBAEhCxI,OAAA;YAAK+H,SAAS,EAAC,qBAAqB;YAAAS,QAAA,gBAClCxI,OAAA;cAAM+H,SAAS,EAAC,WAAW;cAAAS,QAAA,EAAEvD,IAAI,CAAC9D;YAAS;cAAAwH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD9I,OAAA;cACE+H,SAAS,EAAC,eAAe;cACzBgB,OAAO,EAAG9E,CAAC,IAAK;gBACdA,CAAC,CAACoF,eAAe,CAAC,CAAC;gBACnBnE,iBAAiB,CAACD,IAAI,CAAChE,EAAE,CAAC;cAC5B,CAAE;cAAAuH,QAAA,EACH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN9I,OAAA;YAAG+H,SAAS,EAAC,cAAc;YAAAS,QAAA,EAAEvD,IAAI,CAAC/D;UAAO;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GAhBzC7D,IAAI,CAAChE,EAAE;UAAA0H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBT,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9I,OAAA;MAAQ+H,SAAS,EAAC,sBAAsB;MAACgB,OAAO,EAAEhD,OAAQ;MAAAyC,QAAA,eACxDxI,OAAA;QAAKyI,GAAG,EAAC,WAAW;QAACC,GAAG,EAAC,MAAM;QAACX,SAAS,EAAC;MAAW;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,EAGR1H,YAAY,iBACXpB,OAAA;MAAK+H,SAAS,EAAC,eAAe;MAACgB,OAAO,EAAEA,CAAA,KAAM1H,eAAe,CAAC,KAAK,CAAE;MAAAmH,QAAA,eACnExI,OAAA;QAAK+H,SAAS,EAAC,kBAAkB;QAACgB,OAAO,EAAG9E,CAAC,IAAKA,CAAC,CAACoF,eAAe,CAAC,CAAE;QAAAb,QAAA,gBACpExI,OAAA;UAAK+H,SAAS,EAAC,iBAAiB;UAAAS,QAAA,gBAC9BxI,OAAA;YAAI+H,SAAS,EAAC,gBAAgB;YAAAS,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C9I,OAAA;YAAQ+H,SAAS,EAAC,cAAc;YAACgB,OAAO,EAAEA,CAAA,KAAM1H,eAAe,CAAC,KAAK,CAAE;YAAAmH,QAAA,EAAC;UAExE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9I,OAAA;UAAK+H,SAAS,EAAC,kBAAkB;UAAAS,QAAA,gBAE/BxI,OAAA;YAAK+H,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BxI,OAAA;cAAI+H,SAAS,EAAC,eAAe;cAAAS,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C9I,OAAA;cAAK+H,SAAS,EAAC,kBAAkB;cAAAS,QAAA,gBAC/BxI,OAAA;gBAAG+H,SAAS,EAAC,aAAa;gBAAAS,QAAA,EAAEtF,UAAU,CAACN;cAAI;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChD9I,OAAA;gBAAG+H,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAEtF,UAAU,CAACL;cAAI;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClD9I,OAAA;gBAAG+H,SAAS,EAAC,eAAe;gBAAAS,QAAA,GAAC,MAAI,EAACtF,UAAU,CAACF,SAAS;cAAA;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3D9I,OAAA;gBAAK+H,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,gBAChCxI,OAAA;kBAAK+H,SAAS,EAAE,2BAA2B7E,UAAU,CAACJ,MAAM;gBAAG;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtE9I,OAAA;kBAAM+H,SAAS,EAAE,sBAAsB7E,UAAU,CAACJ,MAAM,EAAG;kBAAA0F,QAAA,GAAC,UAClD,EAACtF,UAAU,CAACJ,MAAM,KAAK,QAAQ,GAAG,WAAW,GAAG,cAAc;gBAAA;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EACL5F,UAAU,CAACJ,MAAM,KAAK,cAAc,iBACnC9C,OAAA;gBAAG+H,SAAS,EAAC,mCAAmC;gBAAAS,QAAA,EAAC;cAEjD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9I,OAAA;YAAK+H,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BxI,OAAA;cAAI+H,SAAS,EAAC,eAAe;cAAAS,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChDpG,aAAa,CAACoE,MAAM,KAAK,CAAC,gBACzB9G,OAAA;cAAK+H,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCxI,OAAA;gBAAG+H,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5D9I,OAAA;gBAAG+H,SAAS,EAAC,sBAAsB;gBAAAS,QAAA,EAAC;cAA+C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,GAENpG,aAAa,CAAC6D,GAAG,CAAEnC,MAAM,iBACvBpE,OAAA;cAAqB+H,SAAS,EAAC,sBAAsB;cAAAS,QAAA,gBAEnDxI,OAAA;gBACE+H,SAAS,EAAC,yBAAyB;gBACnCgB,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC/B,MAAM,CAACnD,EAAE,CAAE;gBAAAuH,QAAA,EACxC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAET9I,OAAA;gBAAK+H,SAAS,EAAC,aAAa;gBAAAS,QAAA,gBAC1BxI,OAAA;kBAAG+H,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAEpE,MAAM,CAACxB;gBAAI;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD9I,OAAA;kBAAG+H,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAEpE,MAAM,CAACvB;gBAAI;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD9I,OAAA;kBAAK+H,SAAS,EAAC,mBAAmB;kBAAAS,QAAA,gBAChCxI,OAAA;oBAAK+H,SAAS,EAAE,2BAA2B3D,MAAM,CAACtB,MAAM;kBAAG;oBAAA6F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClE9I,OAAA;oBAAM+H,SAAS,EAAE,sBAAsB3D,MAAM,CAACtB,MAAM,EAAG;oBAAA0F,QAAA,GACpDpE,MAAM,CAACtB,MAAM,KAAK,WAAW,GAAG,WAAW,GAAG,cAAc,EAAC,UAAG,EAACsB,MAAM,CAACrB,QAAQ;kBAAA;oBAAA4F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9I,OAAA;gBACE+H,SAAS,EAAE,qCAAqC3D,MAAM,CAACtB,MAAM,KAAK,WAAW,GAAG,mBAAmB,GAAG,gBAAgB,EAAG;gBACzHiG,OAAO,EAAEA,CAAA,KAAM3E,MAAM,CAACtB,MAAM,KAAK,WAAW,GAAG0D,gBAAgB,CAACpC,MAAM,CAACnD,EAAE,CAAC,GAAGqF,aAAa,CAAClC,MAAM,CAACnD,EAAE,CAAE;gBAAAuH,QAAA,EAErGpE,MAAM,CAACtB,MAAM,KAAK,WAAW,GAAG,YAAY,GAAG;cAAS;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA,GA1BD1E,MAAM,CAACnD,EAAE;cAAA0H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2Bd,CACN,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN9I,OAAA;YAAK+H,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BxI,OAAA;cAAK+H,SAAS,EAAC,gBAAgB;cAAAS,QAAA,gBAC7BxI,OAAA;gBAAI+H,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnD9I,OAAA;gBACE+H,SAAS,EAAE,mBAAmB7F,aAAa,GAAG,0BAA0B,GAAG,EAAE,EAAG;gBAChF6G,OAAO,EAAElC,gBAAiB;gBAC1ByC,QAAQ,EAAEpH,aAAc;gBAAAsG,QAAA,EAEvBtG,aAAa,GAAG,gBAAgB,GAAG;cAAU;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL9G,cAAc,CAAC8E,MAAM,KAAK,CAAC,gBAC1B9G,OAAA;cAAK+H,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCxI,OAAA;gBAAG+H,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrD9I,OAAA;gBAAG+H,SAAS,EAAC,sBAAsB;gBAAAS,QAAA,EAChCtG,aAAa,GAAG,yBAAyB,GAAG;cAAoC;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,GAEN9G,cAAc,CAACuE,GAAG,CAAEnC,MAAM,iBACxBpE,OAAA;cAAqB+H,SAAS,EAAC,wBAAwB;cAAAS,QAAA,gBACrDxI,OAAA;gBAAK+H,SAAS,EAAC,aAAa;gBAAAS,QAAA,gBAC1BxI,OAAA;kBAAG+H,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAEpE,MAAM,CAACxB;gBAAI;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD9I,OAAA;kBAAG+H,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAEpE,MAAM,CAACvB;gBAAI;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjD9I,OAAA;kBAAG+H,SAAS,EAAC,uBAAuB;kBAAAS,QAAA,EAAEpE,MAAM,CAACrB;gBAAQ;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN9I,OAAA;gBACE+H,SAAS,EAAC,aAAa;gBACvBgB,OAAO,EAAEA,CAAA,KAAMtC,UAAU,CAACrC,MAAM,CAACnD,EAAE,CAAE;gBAAAuH,QAAA,EACtC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAXD1E,MAAM,CAACnD,EAAE;cAAA0H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYd,CACN,CACF,eAED9I,OAAA;cAAK+H,SAAS,EAAC,sBAAsB;cAAAS,QAAA,eACnCxI,OAAA;gBAAQ+H,SAAS,EAAC,oBAAoB;gBAACgB,OAAO,EAAE/B,cAAe;gBAAAwB,QAAA,EAAC;cAEhE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9I,OAAA;YAAK+H,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BxI,OAAA;cAAI+H,SAAS,EAAC,eAAe;cAAAS,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtD9I,OAAA;cACE+H,SAAS,EAAC,cAAc;cACxBgB,OAAO,EAAEA,CAAA,KAAMxH,mBAAmB,CAAC,IAAI,CAAE;cAAAiH,QAAA,gBAEzCxI,OAAA;gBAAM+H,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxD9I,OAAA;gBAAM+H,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eAGT9I,OAAA;cACE+H,SAAS,EAAC,cAAc;cACxBgB,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMQ,QAAQ,GAAG,CAACzH,qBAAqB;gBACvCC,wBAAwB,CAACwH,QAAQ,CAAC;gBAClC7E,WAAW,CAAC6E,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B,CAAC;cAChF,CAAE;cAAAf,QAAA,gBAEFxI,OAAA;gBAAM+H,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1D9I,OAAA;gBAAM+H,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EACjC1G,qBAAqB,GAAG,IAAI,GAAG;cAAK;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGT9I,OAAA;cACE+H,SAAS,EAAC,cAAc;cACxBgB,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI7G,aAAa,EAAE;kBACjBC,gBAAgB,CAAC,KAAK,CAAC;kBACvBuC,WAAW,CAAC,2BAA2B,CAAC;gBAC1C,CAAC,MAAM;kBACLvC,gBAAgB,CAAC,IAAI,CAAC;kBACtBuC,WAAW,CAAC,2BAA2B,CAAC;gBAC1C;cACF,CAAE;cAAA8D,QAAA,gBAEFxI,OAAA;gBAAM+H,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5D9I,OAAA;gBAAM+H,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EACjCtG,aAAa,GAAG,IAAI,GAAG;cAAK;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAxH,gBAAgB,iBACftB,OAAA;MAAK+H,SAAS,EAAC,eAAe;MAACgB,OAAO,EAAEA,CAAA,KAAMxH,mBAAmB,CAAC,KAAK,CAAE;MAAAiH,QAAA,eACvExI,OAAA;QAAK+H,SAAS,EAAC,qBAAqB;QAACgB,OAAO,EAAG9E,CAAC,IAAKA,CAAC,CAACoF,eAAe,CAAC,CAAE;QAAAb,QAAA,gBACvExI,OAAA;UAAK+H,SAAS,EAAC,iBAAiB;UAAAS,QAAA,gBAC9BxI,OAAA;YAAI+H,SAAS,EAAC,gBAAgB;YAAAS,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjD9I,OAAA;YAAQ+H,SAAS,EAAC,cAAc;YAACgB,OAAO,EAAEA,CAAA,KAAMxH,mBAAmB,CAAC,KAAK,CAAE;YAAAiH,QAAA,EAAC;UAE5E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9I,OAAA;UAAK+H,SAAS,EAAC,kBAAkB;UAAAS,QAAA,eAC/BxI,OAAA;YAAK+H,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/BxI,OAAA;cAAK+H,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCxI,OAAA;gBAAO+H,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvD9I,OAAA;gBACE6C,IAAI,EAAC,UAAU;gBACfkF,SAAS,EAAC,uBAAuB;gBACjCyB,OAAO,EAAEpH,YAAY,CAACE,QAAS;gBAC/B4G,QAAQ,EAAGjF,CAAC,IAAK5B,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEE,QAAQ,EAAE2B,CAAC,CAACsE,MAAM,CAACiB;gBAAO,CAAC;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN9I,OAAA;cAAK+H,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCxI,OAAA;gBAAO+H,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,GAAC,cAAY,EAACpG,YAAY,CAACG,SAAS,EAAC,UAAQ;cAAA;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1F9I,OAAA;gBAAG+H,SAAS,EAAC,0BAA0B;gBAAAS,QAAA,EACpCpG,YAAY,CAACG,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG,GAAGH,YAAY,CAACG,SAAS;cAAe;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC,eACJ9I,OAAA;gBAAK+H,SAAS,EAAC,qBAAqB;gBAAAS,QAAA,gBAClCxI,OAAA;kBACE+H,SAAS,EAAC,mBAAmB;kBAC7BgB,OAAO,EAAEA,CAAA,KAAM1G,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEG,SAAS,EAAEkH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEtH,YAAY,CAACG,SAAS,GAAG,CAAC;kBAAC,CAAC,CAAE;kBAAAiG,QAAA,EACvG;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9I,OAAA;kBAAM+H,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,GAAEpG,YAAY,CAACG,SAAS,EAAC,GAAC;gBAAA;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnE9I,OAAA;kBACE+H,SAAS,EAAC,mBAAmB;kBAC7BgB,OAAO,EAAEA,CAAA,KAAM1G,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEG,SAAS,EAAEkH,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEvH,YAAY,CAACG,SAAS,GAAG,CAAC;kBAAC,CAAC,CAAE;kBAAAiG,QAAA,EACxG;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9I,OAAA;cAAK+H,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCxI,OAAA;gBAAO+H,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7D9I,OAAA;gBACE6C,IAAI,EAAC,UAAU;gBACfkF,SAAS,EAAC,uBAAuB;gBACjCyB,OAAO,EAAEpH,YAAY,CAACI,aAAc;gBACpC0G,QAAQ,EAAGjF,CAAC,IAAK5B,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEI,aAAa,EAAEyB,CAAC,CAACsE,MAAM,CAACiB;gBAAO,CAAC;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN9I,OAAA;cAAK+H,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChCxI,OAAA;gBAAO+H,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChE9I,OAAA;gBACE6C,IAAI,EAAC,UAAU;gBACfkF,SAAS,EAAC,uBAAuB;gBACjCyB,OAAO,EAAEpH,YAAY,CAACK,aAAc;gBACpCyG,QAAQ,EAAGjF,CAAC,IAAK5B,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEK,aAAa,EAAEwB,CAAC,CAACsE,MAAM,CAACiB;gBAAO,CAAC;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN9I,OAAA;cAAK+H,SAAS,EAAC,mBAAmB;cAAAS,QAAA,eAChCxI,OAAA;gBACE+H,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,6FAA6F,CAAE;gBAAA8D,QAAA,EAC3H;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN9I,OAAA;cAAK+H,SAAS,EAAC,mBAAmB;cAAAS,QAAA,eAChCxI,OAAA;gBACE+H,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,8EAA8E,CAAE;gBAAA8D,QAAA,EAC5G;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN9I,OAAA;cAAK+H,SAAS,EAAC,mBAAmB;cAAAS,QAAA,eAChCxI,OAAA;gBACE+H,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,8EAA8E,CAAE;gBAAA8D,QAAA,EAC5G;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN9I,OAAA;cAAK+H,SAAS,EAAC,kBAAkB;cAAAS,QAAA,gBAC/BxI,OAAA;gBAAI+H,SAAS,EAAC,wBAAwB;gBAAAS,QAAA,EAAC;cAAmC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/E9I,OAAA;gBAAG+H,SAAS,EAAC,sBAAsB;gBAAAS,QAAA,EAAC;cAEpC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAEJ9I,OAAA;gBAAK+H,SAAS,EAAC,cAAc;gBAAAS,QAAA,gBAC3BxI,OAAA;kBAAM+H,SAAS,EAAC,gBAAgB;kBAAAS,QAAA,EAAC;gBAA8B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtE9I,OAAA;kBACE+H,SAAS,EAAC,wBAAwB;kBAClCgB,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,gEAAgE,CAAE;kBAAA8D,QAAA,eAE7FxI,OAAA;oBAAK+H,SAAS,EAAC;kBAA8B;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN9I,OAAA;gBAAK+H,SAAS,EAAC,cAAc;gBAAAS,QAAA,gBAC3BxI,OAAA;kBAAM+H,SAAS,EAAC,gBAAgB;kBAAAS,QAAA,EAAC;gBAAuB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/D9I,OAAA;kBACE+H,SAAS,EAAC,wBAAwB;kBAClCgB,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,+BAA+B,CAAE;kBAAA8D,QAAA,eAE5DxI,OAAA;oBAAK+H,SAAS,EAAC;kBAA8B;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN9I,OAAA;gBAAK+H,SAAS,EAAC,cAAc;gBAAAS,QAAA,gBAC3BxI,OAAA;kBAAM+H,SAAS,EAAC,gBAAgB;kBAAAS,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5D9I,OAAA;kBACE+H,SAAS,EAAC,wBAAwB;kBAClCgB,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,iCAAiC,CAAE;kBAAA8D,QAAA,eAE9DxI,OAAA;oBAAK+H,SAAS,EAAC;kBAA8B;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN9I,OAAA;gBAAG+H,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAC;cAE7B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC5I,EAAA,CAv0BQD,GAAG;AAAA2J,EAAA,GAAH3J,GAAG;AAy0BZ,eAAeA,GAAG;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}