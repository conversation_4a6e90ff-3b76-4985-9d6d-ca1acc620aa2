{"ast": null, "code": "var _jsxFileName = \"D:\\\\new git\\\\Clipsy-Windows\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  // State management matching Android app\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to Clipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\n  const [historyItems, setHistoryItems] = useState([{\n    id: '1',\n    content: 'This is an older clipboard item. It\\'s shorter.',\n    timestamp: '2 minutes ago'\n  }, {\n    id: '2',\n    content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.',\n    timestamp: '10 minutes ago'\n  }, {\n    id: '3',\n    content: 'Yet another historical entry.',\n    timestamp: '1 hour ago'\n  }, {\n    id: '4',\n    content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }',\n    timestamp: '5 hours ago'\n  }]);\n\n  // UI State\n  const [showSettings, setShowSettings] = useState(false);\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\n\n  // Background Sync State\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\n  const [networkDevices, setNetworkDevices] = useState([]);\n  const [isDiscovering, setIsDiscovering] = useState(false);\n\n  // Sync Settings - matching Android app\n  const [syncSettings, setSyncSettings] = useState({\n    autoSync: true,\n    syncDelay: 2,\n    syncOnConnect: true,\n    bidirectional: true\n  });\n\n  // Device Management - matching Android app\n  const [pairedDevices, setPairedDevices] = useState([{\n    id: 'android-1',\n    name: 'Android Phone - Personal',\n    type: 'Android 14',\n    status: 'connected',\n    lastSeen: '2 min ago',\n    ipAddress: '*************'\n  }, {\n    id: 'linux-1',\n    name: 'Ubuntu Server - Home',\n    type: 'Ubuntu 22.04',\n    status: 'disconnected',\n    lastSeen: '1 hour ago',\n    ipAddress: '*************'\n  }]);\n  const [discoveredDevices] = useState([{\n    id: 'johns-laptop',\n    name: 'John\\'s Laptop',\n    type: 'Windows 10',\n    status: 'discovering',\n    lastSeen: 'Available for pairing',\n    ipAddress: '*************'\n  }, {\n    id: 'sarahs-desktop',\n    name: 'Sarah\\'s Desktop',\n    type: 'Ubuntu 22.04',\n    status: 'discovering',\n    lastSeen: 'Available for pairing',\n    ipAddress: '*************'\n  }]);\n\n  // Device info state - get actual Windows device details\n  const [deviceInfo, setDeviceInfo] = useState({\n    name: 'Windows PC - Loading...',\n    type: 'Windows',\n    status: 'active',\n    lastSeen: 'now',\n    ipAddress: '*************'\n  });\n\n  // Get actual Windows computer name and OS details\n  React.useEffect(() => {\n    const getDeviceInfo = async () => {\n      try {\n        let osVersion = 'Windows';\n\n        // Get OS version from user agent\n        if (navigator.userAgentData) {\n          const platform = navigator.userAgentData.platform;\n          osVersion = platform || 'Windows';\n        } else if (navigator.userAgent) {\n          const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\n          if (windowsMatch) {\n            const version = windowsMatch[1];\n            switch (version) {\n              case '10.0':\n                osVersion = 'Windows 10/11';\n                break;\n              case '6.3':\n                osVersion = 'Windows 8.1';\n                break;\n              case '6.2':\n                osVersion = 'Windows 8';\n                break;\n              case '6.1':\n                osVersion = 'Windows 7';\n                break;\n              default:\n                osVersion = `Windows NT ${version}`;\n            }\n          }\n        }\n\n        // Get the actual computer name\n        const computerName = await getWindowsComputerName();\n        const deviceName = `${computerName} - ${osVersion}`;\n\n        // Check if any devices are connected\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\n        setDeviceInfo(prev => ({\n          ...prev,\n          name: deviceName,\n          type: osVersion,\n          status: deviceStatus,\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\n        }));\n        console.log('Device info updated:', {\n          computerName,\n          deviceName,\n          osVersion\n        });\n      } catch (error) {\n        console.log('Could not get device info:', error);\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\n        setDeviceInfo(prev => ({\n          ...prev,\n          name: 'Windows PC - Main',\n          type: 'Windows',\n          status: deviceStatus,\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\n        }));\n      }\n    };\n    getDeviceInfo();\n  }, [pairedDevices]);\n\n  // Functions\n  const showMessage = text => {\n    setSuccessMessage(text);\n    setTimeout(() => setSuccessMessage(''), 3000);\n  };\n\n  // Get Windows computer name using multiple methods\n  const getWindowsComputerName = async () => {\n    try {\n      // Method 1: Try PowerShell command (if available in Electron or similar environment)\n      if (window.electronAPI) {\n        try {\n          const computerName = await window.electronAPI.getComputerName();\n          if (computerName) return computerName.trim().toUpperCase();\n        } catch (e) {\n          console.log('Electron method failed:', e);\n        }\n      }\n\n      // Method 2: Try to get from environment variables (Node.js environment)\n      if (typeof process !== 'undefined' && process.env) {\n        if (process.env.COMPUTERNAME) return process.env.COMPUTERNAME.toUpperCase();\n        if (process.env.HOSTNAME) return process.env.HOSTNAME.toUpperCase();\n      }\n\n      // Method 3: Try to get from Windows registry via fetch (if CORS allows)\n      try {\n        const response = await fetch('/api/computer-name');\n        if (response.ok) {\n          const data = await response.json();\n          if (data.computerName) return data.computerName.toUpperCase();\n        }\n      } catch (e) {\n        console.log('API method failed:', e);\n      }\n\n      // Method 4: Use hostname if it's not localhost\n      if (window.location.hostname && window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {\n        return window.location.hostname.toUpperCase();\n      }\n\n      // Method 5: Generate persistent unique name\n      let storedName = localStorage.getItem('clipsy-computer-name');\n      if (!storedName) {\n        // Create a unique identifier based on browser characteristics\n        const userAgent = navigator.userAgent;\n        const screen = `${window.screen.width}x${window.screen.height}`;\n        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n        const language = navigator.language;\n\n        // Create a simple hash\n        const hashInput = `${userAgent}-${screen}-${timezone}-${language}`;\n        let hash = 0;\n        for (let i = 0; i < hashInput.length; i++) {\n          const char = hashInput.charCodeAt(i);\n          hash = (hash << 5) - hash + char;\n          hash = hash & hash; // Convert to 32-bit integer\n        }\n        storedName = `WINDOWS-PC-${Math.abs(hash).toString(16).toUpperCase().slice(0, 6)}`;\n        localStorage.setItem('clipsy-computer-name', storedName);\n      }\n      return storedName;\n    } catch (error) {\n      console.log('All computer name methods failed:', error);\n      return 'WINDOWS-PC';\n    }\n  };\n  const copyToClipboard = async content => {\n    try {\n      await navigator.clipboard.writeText(content);\n      setThisDeviceClipboard(content);\n      showMessage('✅ Text copied to clipboard!');\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error);\n      showMessage('❌ Failed to copy to clipboard');\n    }\n  };\n  const selectItem = item => {\n    copyToClipboard(item.content);\n    setConnectedDeviceClipboard(item.content);\n  };\n  const deleteHistoryItem = itemId => {\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\n    setHistoryItems(updatedHistory);\n    showMessage('🗑️ History item deleted!');\n  };\n\n  // Device edit functions\n  const startEditingThisDevice = () => {\n    setEditingThisDeviceText(thisDeviceClipboard);\n    setIsEditingThisDevice(true);\n  };\n  const saveThisDeviceEdit = async () => {\n    const newContent = editingThisDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setThisDeviceClipboard(newContent);\n    try {\n      await navigator.clipboard.writeText(newContent);\n    } catch (error) {\n      console.warn('Failed to update clipboard:', error);\n    }\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n    showMessage('✅ This Device clipboard updated!');\n  };\n  const cancelThisDeviceEdit = () => {\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n  };\n  const startEditingConnectedDevice = () => {\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\n    setIsEditingConnectedDevice(true);\n  };\n  const saveConnectedDeviceEdit = () => {\n    const newContent = editingConnectedDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setConnectedDeviceClipboard(newContent);\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n    showMessage('✅ Connected Device clipboard updated!');\n  };\n  const cancelConnectedDeviceEdit = () => {\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n  };\n  const syncNow = () => {\n    showMessage('🔄 Syncing with paired devices...');\n    setTimeout(() => {\n      showMessage('✅ Sync completed!');\n    }, 1000);\n  };\n  const toggleAlwaysOnTop = () => {\n    setIsAlwaysOnTop(!isAlwaysOnTop);\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\n  };\n  const minimizeToTray = () => {\n    showMessage('➖ Minimizing to background...');\n  };\n  const toggleFloatingOverlay = () => {\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\n  };\n\n  // Device Management Functions\n  const removeDevice = deviceId => {\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\n    setPairedDevices(updatedDevices);\n    showMessage('🗑️ Device removed from paired devices');\n  };\n  const connectDevice = deviceId => {\n    const updatedDevices = pairedDevices.map(device => device.id === deviceId ? {\n      ...device,\n      status: 'connected',\n      lastSeen: 'Just now'\n    } : device);\n    setPairedDevices(updatedDevices);\n    showMessage('✅ Device connected successfully');\n  };\n  const disconnectDevice = deviceId => {\n    const updatedDevices = pairedDevices.map(device => device.id === deviceId ? {\n      ...device,\n      status: 'disconnected',\n      lastSeen: 'Just now'\n    } : device);\n    setPairedDevices(updatedDevices);\n    showMessage('🔌 Device disconnected');\n  };\n  const pairDevice = deviceId => {\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\n    if (deviceToPair) {\n      const newPairedDevice = {\n        ...deviceToPair,\n        status: 'connected',\n        lastSeen: 'Just now'\n      };\n      setPairedDevices([...pairedDevices, newPairedDevice]);\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\n    }\n  };\n  const refreshDiscovery = () => {\n    if (isDiscovering) return;\n    setIsDiscovering(true);\n    showMessage('🔍 Scanning for devices...');\n\n    // Simulate discovery process\n    setTimeout(() => {\n      setNetworkDevices(discoveredDevices);\n      setIsDiscovering(false);\n      showMessage(`📱 Found ${discoveredDevices.length} available devices`);\n    }, 2000);\n  };\n  const scanQRCode = () => {\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\n  };\n  const generateQRCode = () => {\n    // Generate connection info for QR code\n    const connectionInfo = {\n      deviceName: deviceInfo.name,\n      deviceType: deviceInfo.type,\n      ipAddress: deviceInfo.ipAddress,\n      port: 3001,\n      protocol: 'clipsy-sync',\n      timestamp: Date.now()\n    };\n    const qrData = JSON.stringify(connectionInfo);\n    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrData)}`;\n\n    // Create and show QR code modal\n    const qrModal = document.createElement('div');\n    qrModal.className = 'qr-modal-overlay';\n    qrModal.innerHTML = `\n      <div class=\"qr-modal-content\">\n        <div class=\"qr-modal-header\">\n          <h3>📱 Scan to Connect Android Device</h3>\n          <button class=\"qr-modal-close\">✕</button>\n        </div>\n        <div class=\"qr-modal-body\">\n          <img src=\"${qrCodeUrl}\" alt=\"QR Code\" class=\"qr-code-image\" />\n          <p class=\"qr-instructions\">\n            1. Open Clipsy app on your Android device<br/>\n            2. Go to Settings → Device Discovery<br/>\n            3. Tap \"Scan QR\" and scan this code<br/>\n            4. Your devices will be paired automatically\n          </p>\n          <div class=\"qr-device-info\">\n            <p><strong>Device:</strong> ${deviceInfo.name}</p>\n            <p><strong>IP:</strong> ${deviceInfo.ipAddress}</p>\n          </div>\n        </div>\n      </div>\n    `;\n    document.body.appendChild(qrModal);\n\n    // Close modal functionality\n    const closeModal = () => {\n      document.body.removeChild(qrModal);\n    };\n    qrModal.querySelector('.qr-modal-close').onclick = closeModal;\n    qrModal.onclick = e => {\n      if (e.target === qrModal) closeModal();\n    };\n    showMessage('📱 QR Code generated! Scan with Android Clipsy app to connect.');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"title-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/clipsy-logo-no-bg.png\",\n            alt: \"Clipsy Logo\",\n            className: \"app-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: \"Clipsy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isFloatingOverlayVisible ? 'active' : ''}`,\n          onClick: toggleFloatingOverlay,\n          title: \"Toggle floating clipboard widget\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isAlwaysOnTop ? 'active' : ''}`,\n          onClick: toggleAlwaysOnTop,\n          title: \"Pin to top\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pin-icon\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-head\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-body\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: minimizeToTray,\n          title: \"Minimize\",\n          children: \"\\u2796\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: () => setShowSettings(true),\n          title: \"Settings\",\n          children: \"\\u2699\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"device-section-title-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"device-section-title\",\n              children: \"\\uD83D\\uDCBB This Device\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"device-section-subtitle\",\n              children: deviceInfo.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this), isEditingThisDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingThisDeviceText,\n            onChange: e => setEditingThisDeviceText(e.target.value),\n            placeholder: \"Edit this device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button this-device\",\n              onClick: saveThisDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button this-device\",\n              onClick: cancelThisDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content this-device-content\",\n            onClick: () => copyToClipboard(thisDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: thisDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Real-time sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingThisDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"device-section-title\",\n            children: \"\\uD83D\\uDD17 Connected Device\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"device-section-subtitle\",\n            children: \"Android Phone - Personal \\uD83D\\uDFE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this), isEditingConnectedDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingConnectedDeviceText,\n            onChange: e => setEditingConnectedDeviceText(e.target.value),\n            placeholder: \"Edit connected device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button connected-device\",\n              onClick: saveConnectedDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button connected-device\",\n              onClick: cancelConnectedDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content connected-device-content\",\n            onClick: () => copyToClipboard(connectedDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: connectedDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Bidirectional sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingConnectedDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"history-title\",\n        children: \"Clipboard History\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-list\",\n        children: historyItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-item\",\n          onClick: () => selectItem(item),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-item-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"timestamp\",\n              children: item.timestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"delete-button\",\n              onClick: e => {\n                e.stopPropagation();\n                deleteHistoryItem(item.id);\n              },\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"item-content\",\n            children: item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"floating-sync-button\",\n      onClick: syncNow,\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/sync.png\",\n        alt: \"Sync\",\n        className: \"sync-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 7\n    }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowSettings(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-sidebar\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-title\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: () => setShowSettings(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Device Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"device-info-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-name\",\n                children: deviceInfo.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail\",\n                children: deviceInfo.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail\",\n                children: [\"IP: \", deviceInfo.ipAddress]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-status-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `device-status-indicator ${deviceInfo.status}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `device-status-text ${deviceInfo.status}`,\n                  children: [\"Status: \", deviceInfo.status === 'active' ? 'Connected' : 'Disconnected']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 19\n              }, this), deviceInfo.status === 'disconnected' && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail disconnected-notice\",\n                children: \"\\u26A0\\uFE0F No devices connected - Use QR code or device discovery to connect Android devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"refresh-device-name-button\",\n                onClick: async () => {\n                  const newComputerName = await getWindowsComputerName();\n                  showMessage(`🖥️ Computer name refreshed: ${newComputerName}`);\n                  // Trigger device info refresh\n                  const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\n                  const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\n                  let osVersion = 'Windows';\n                  if (navigator.userAgent) {\n                    const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\n                    if (windowsMatch) {\n                      const version = windowsMatch[1];\n                      switch (version) {\n                        case '10.0':\n                          osVersion = 'Windows 10/11';\n                          break;\n                        case '6.3':\n                          osVersion = 'Windows 8.1';\n                          break;\n                        case '6.2':\n                          osVersion = 'Windows 8';\n                          break;\n                        case '6.1':\n                          osVersion = 'Windows 7';\n                          break;\n                        default:\n                          osVersion = `Windows NT ${version}`;\n                      }\n                    }\n                  }\n                  setDeviceInfo(prev => ({\n                    ...prev,\n                    name: `${newComputerName} - ${osVersion}`,\n                    type: osVersion,\n                    status: deviceStatus,\n                    lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\n                  }));\n                },\n                children: \"\\uD83D\\uDD04 Refresh Computer Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Paired Devices\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 17\n            }, this), pairedDevices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-device-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-text\",\n                children: \"No paired devices found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-subtext\",\n                children: \"Use QR code or device discovery to pair devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 19\n            }, this) : pairedDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"enhanced-device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"remove-button-top-right\",\n                onClick: () => removeDevice(device.id),\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"device-status-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `device-status-indicator ${device.status}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `device-status-text ${device.status}`,\n                    children: [device.status === 'connected' ? 'Connected' : 'Disconnected', \" \\u2022 \", device.lastSeen]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`,\n                onClick: () => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id),\n                children: device.status === 'connected' ? 'Disconnect' : 'Connect'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 23\n              }, this)]\n            }, device.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 21\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"section-title\",\n                children: \"Device Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`,\n                onClick: refreshDiscovery,\n                disabled: isDiscovering,\n                children: isDiscovering ? '🔍 Scanning...' : 'Discover'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 17\n            }, this), networkDevices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-device-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-text\",\n                children: \"No devices found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-subtext\",\n                children: isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 19\n            }, this) : networkDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"discovered-device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-last-seen\",\n                  children: device.lastSeen\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"pair-button\",\n                onClick: () => pairDevice(device.id),\n                children: \"Pair\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 23\n              }, this)]\n            }, device.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 21\n            }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qr-buttons-container\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"qr-generate-button\",\n                onClick: generateQRCode,\n                children: \"Generate QR\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Additional Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => setShowSyncSettings(true),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Sync Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-arrow\",\n                children: \"\\u203A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => {\n                const newValue = !backgroundSyncEnabled;\n                setBackgroundSyncEnabled(newValue);\n                showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Background Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-value\",\n                children: backgroundSyncEnabled ? 'on' : 'off'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => {\n                if (isDiscovering) {\n                  setIsDiscovering(false);\n                  showMessage('Network discovery stopped');\n                } else {\n                  setIsDiscovering(true);\n                  showMessage('Network discovery started');\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Network Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-value\",\n                children: isDiscovering ? 'on' : 'off'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 769,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 592,\n      columnNumber: 9\n    }, this), showSyncSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowSyncSettings(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sync-settings-modal\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-title\",\n            children: \"Sync Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 797,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: () => setShowSyncSettings(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 796,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Auto Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.autoSync,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  autoSync: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: [\"Sync Delay: \", syncSettings.syncDelay, \" seconds\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"sync-setting-description\",\n                children: syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sync-delay-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"sync-delay-button\",\n                  onClick: () => setSyncSettings({\n                    ...syncSettings,\n                    syncDelay: Math.max(0, syncSettings.syncDelay - 1)\n                  }),\n                  children: \"-\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sync-delay-value\",\n                  children: [syncSettings.syncDelay, \"s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"sync-delay-button\",\n                  onClick: () => setSyncSettings({\n                    ...syncSettings,\n                    syncDelay: Math.min(30, syncSettings.syncDelay + 1)\n                  }),\n                  children: \"+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Sync on Connect\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.syncOnConnect,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  syncOnConnect: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Bidirectional Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.bidirectional,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  bidirectional: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.'),\n                children: \"\\uD83D\\uDCF1 Windows \\u2194 Android Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.'),\n                children: \"\\uD83D\\uDDA5\\uFE0F Windows \\u2194 Windows Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.'),\n                children: \"\\uD83D\\uDE80 Sync All Devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"settings-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"settings-section-title\",\n                children: \"\\uD83D\\uDCCB Floating Overlay Button Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"settings-description\",\n                children: \"Configure the floating overlay button for quick access to connected device clipboards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Enable Floating Overlay Button\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('📋 Floating overlay button is always enabled for accessibility'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 898,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 894,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Show Device Count Badge\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 903,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('🔢 Device count badge enabled'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 908,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Auto-hide After Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('⏱️ Auto-hide after copy enabled'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 918,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 912,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"settings-note\",\n                children: \"\\uD83D\\uDCA1 The floating overlay button (\\uD83D\\uDCCB) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 795,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 794,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 424,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"nTPZ0TWRncDLxHIgWbYHi30bIdE=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "App", "_s", "thisDeviceClipboard", "setThisDeviceClipboard", "connectedDeviceClipboard", "setConnectedDeviceClipboard", "isEditingThisDevice", "setIsEditingThisDevice", "isEditingConnectedDevice", "setIsEditingConnectedDevice", "editingThisDeviceText", "setEditingThisDeviceText", "editingConnectedDeviceText", "setEditingConnectedDeviceText", "historyItems", "setHistoryItems", "id", "content", "timestamp", "showSettings", "setShowSettings", "showSyncSettings", "setShowSyncSettings", "isAlwaysOnTop", "setIsAlwaysOnTop", "successMessage", "setSuccessMessage", "isFloatingOverlayVisible", "setIsFloatingOverlayVisible", "backgroundSyncEnabled", "setBackgroundSyncEnabled", "networkDevices", "setNetworkDevices", "isDiscovering", "setIsDiscovering", "syncSettings", "setSyncSettings", "autoSync", "syncD<PERSON>y", "syncOnConnect", "bidirectional", "pairedDevices", "setPairedDevices", "name", "type", "status", "lastSeen", "ip<PERSON><PERSON><PERSON>", "discoveredDevices", "deviceInfo", "setDeviceInfo", "getDeviceInfo", "osVersion", "navigator", "userAgentData", "platform", "userAgent", "windowsMatch", "match", "version", "computerName", "getWindowsComputerName", "deviceName", "hasConnectedDevices", "some", "device", "deviceStatus", "prev", "console", "log", "error", "showMessage", "text", "setTimeout", "window", "electronAPI", "getComputerName", "trim", "toUpperCase", "e", "process", "env", "COMPUTERNAME", "HOSTNAME", "response", "fetch", "ok", "data", "json", "location", "hostname", "storedName", "localStorage", "getItem", "screen", "width", "height", "timezone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "language", "hashInput", "hash", "i", "length", "char", "charCodeAt", "Math", "abs", "toString", "slice", "setItem", "copyToClipboard", "clipboard", "writeText", "selectItem", "item", "deleteHistoryItem", "itemId", "updatedHistory", "filter", "startEditingThisDevice", "saveThisDeviceEdit", "newContent", "warn", "cancelThisDeviceEdit", "startEditingConnectedDevice", "saveConnectedDeviceEdit", "cancelConnectedDeviceEdit", "syncNow", "toggleAlwaysOnTop", "minimizeToTray", "toggleFloatingOverlay", "removeDevice", "deviceId", "updatedDevices", "connectDevice", "map", "disconnectDevice", "pairDevice", "deviceToPair", "find", "newPairedDevice", "refreshDiscovery", "scanQRCode", "generateQRCode", "connectionInfo", "deviceType", "port", "protocol", "Date", "now", "qrData", "JSON", "stringify", "qrCodeUrl", "encodeURIComponent", "qrModal", "document", "createElement", "className", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "closeModal", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "onclick", "target", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "value", "onChange", "placeholder", "autoFocus", "stopPropagation", "newComputerName", "disabled", "newValue", "checked", "max", "min", "_c", "$RefreshReg$"], "sources": ["D:/new git/Clipsy-Windows/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  // State management matching Android app\r\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to <PERSON>lipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\r\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\r\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\r\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\r\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\r\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\r\n\r\n  const [historyItems, setHistoryItems] = useState([\r\n    { id: '1', content: 'This is an older clipboard item. It\\'s shorter.', timestamp: '2 minutes ago' },\r\n    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },\r\n    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },\r\n    { id: '4', content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }', timestamp: '5 hours ago' }\r\n  ]);\r\n\r\n  // UI State\r\n  const [showSettings, setShowSettings] = useState(false);\r\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\r\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\r\n\r\n  // Background Sync State\r\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\r\n  const [networkDevices, setNetworkDevices] = useState([]);\r\n  const [isDiscovering, setIsDiscovering] = useState(false);\r\n\r\n  // Sync Settings - matching Android app\r\n  const [syncSettings, setSyncSettings] = useState({\r\n    autoSync: true,\r\n    syncDelay: 2,\r\n    syncOnConnect: true,\r\n    bidirectional: true\r\n  });\r\n\r\n  // Device Management - matching Android app\r\n  const [pairedDevices, setPairedDevices] = useState([\r\n    {\r\n      id: 'android-1',\r\n      name: 'Android Phone - Personal',\r\n      type: 'Android 14',\r\n      status: 'connected',\r\n      lastSeen: '2 min ago',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'linux-1',\r\n      name: 'Ubuntu Server - Home',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'disconnected',\r\n      lastSeen: '1 hour ago',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  const [discoveredDevices] = useState([\r\n    {\r\n      id: 'johns-laptop',\r\n      name: 'John\\'s Laptop',\r\n      type: 'Windows 10',\r\n      status: 'discovering',\r\n      lastSeen: 'Available for pairing',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'sarahs-desktop',\r\n      name: 'Sarah\\'s Desktop',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'discovering',\r\n      lastSeen: 'Available for pairing',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  // Device info state - get actual Windows device details\r\n  const [deviceInfo, setDeviceInfo] = useState({\r\n    name: 'Windows PC - Loading...',\r\n    type: 'Windows',\r\n    status: 'active',\r\n    lastSeen: 'now',\r\n    ipAddress: '*************'\r\n  });\r\n\r\n  // Get actual Windows computer name and OS details\r\n  React.useEffect(() => {\r\n    const getDeviceInfo = async () => {\r\n      try {\r\n        let osVersion = 'Windows';\r\n\r\n        // Get OS version from user agent\r\n        if (navigator.userAgentData) {\r\n          const platform = navigator.userAgentData.platform;\r\n          osVersion = platform || 'Windows';\r\n        } else if (navigator.userAgent) {\r\n          const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\r\n          if (windowsMatch) {\r\n            const version = windowsMatch[1];\r\n            switch (version) {\r\n              case '10.0': osVersion = 'Windows 10/11'; break;\r\n              case '6.3': osVersion = 'Windows 8.1'; break;\r\n              case '6.2': osVersion = 'Windows 8'; break;\r\n              case '6.1': osVersion = 'Windows 7'; break;\r\n              default: osVersion = `Windows NT ${version}`;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Get the actual computer name\r\n        const computerName = await getWindowsComputerName();\r\n        const deviceName = `${computerName} - ${osVersion}`;\r\n\r\n        // Check if any devices are connected\r\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\r\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\r\n\r\n        setDeviceInfo(prev => ({\r\n          ...prev,\r\n          name: deviceName,\r\n          type: osVersion,\r\n          status: deviceStatus,\r\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\r\n        }));\r\n\r\n        console.log('Device info updated:', { computerName, deviceName, osVersion });\r\n      } catch (error) {\r\n        console.log('Could not get device info:', error);\r\n        const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\r\n        const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\r\n\r\n        setDeviceInfo(prev => ({\r\n          ...prev,\r\n          name: 'Windows PC - Main',\r\n          type: 'Windows',\r\n          status: deviceStatus,\r\n          lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\r\n        }));\r\n      }\r\n    };\r\n\r\n    getDeviceInfo();\r\n  }, [pairedDevices]);\r\n\r\n  // Functions\r\n  const showMessage = (text) => {\r\n    setSuccessMessage(text);\r\n    setTimeout(() => setSuccessMessage(''), 3000);\r\n  };\r\n\r\n  // Get Windows computer name using multiple methods\r\n  const getWindowsComputerName = async () => {\r\n    try {\r\n      // Method 1: Try PowerShell command (if available in Electron or similar environment)\r\n      if (window.electronAPI) {\r\n        try {\r\n          const computerName = await window.electronAPI.getComputerName();\r\n          if (computerName) return computerName.trim().toUpperCase();\r\n        } catch (e) {\r\n          console.log('Electron method failed:', e);\r\n        }\r\n      }\r\n\r\n      // Method 2: Try to get from environment variables (Node.js environment)\r\n      if (typeof process !== 'undefined' && process.env) {\r\n        if (process.env.COMPUTERNAME) return process.env.COMPUTERNAME.toUpperCase();\r\n        if (process.env.HOSTNAME) return process.env.HOSTNAME.toUpperCase();\r\n      }\r\n\r\n      // Method 3: Try to get from Windows registry via fetch (if CORS allows)\r\n      try {\r\n        const response = await fetch('/api/computer-name');\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          if (data.computerName) return data.computerName.toUpperCase();\r\n        }\r\n      } catch (e) {\r\n        console.log('API method failed:', e);\r\n      }\r\n\r\n      // Method 4: Use hostname if it's not localhost\r\n      if (window.location.hostname &&\r\n          window.location.hostname !== 'localhost' &&\r\n          window.location.hostname !== '127.0.0.1') {\r\n        return window.location.hostname.toUpperCase();\r\n      }\r\n\r\n      // Method 5: Generate persistent unique name\r\n      let storedName = localStorage.getItem('clipsy-computer-name');\r\n      if (!storedName) {\r\n        // Create a unique identifier based on browser characteristics\r\n        const userAgent = navigator.userAgent;\r\n        const screen = `${window.screen.width}x${window.screen.height}`;\r\n        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\r\n        const language = navigator.language;\r\n\r\n        // Create a simple hash\r\n        const hashInput = `${userAgent}-${screen}-${timezone}-${language}`;\r\n        let hash = 0;\r\n        for (let i = 0; i < hashInput.length; i++) {\r\n          const char = hashInput.charCodeAt(i);\r\n          hash = ((hash << 5) - hash) + char;\r\n          hash = hash & hash; // Convert to 32-bit integer\r\n        }\r\n\r\n        storedName = `WINDOWS-PC-${Math.abs(hash).toString(16).toUpperCase().slice(0, 6)}`;\r\n        localStorage.setItem('clipsy-computer-name', storedName);\r\n      }\r\n\r\n      return storedName;\r\n    } catch (error) {\r\n      console.log('All computer name methods failed:', error);\r\n      return 'WINDOWS-PC';\r\n    }\r\n  };\r\n\r\n  const copyToClipboard = async (content) => {\r\n    try {\r\n      await navigator.clipboard.writeText(content);\r\n      setThisDeviceClipboard(content);\r\n      showMessage('✅ Text copied to clipboard!');\r\n    } catch (error) {\r\n      console.error('Failed to copy to clipboard:', error);\r\n      showMessage('❌ Failed to copy to clipboard');\r\n    }\r\n  };\r\n\r\n  const selectItem = (item) => {\r\n    copyToClipboard(item.content);\r\n    setConnectedDeviceClipboard(item.content);\r\n  };\r\n\r\n  const deleteHistoryItem = (itemId) => {\r\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\r\n    setHistoryItems(updatedHistory);\r\n    showMessage('🗑️ History item deleted!');\r\n  };\r\n\r\n  // Device edit functions\r\n  const startEditingThisDevice = () => {\r\n    setEditingThisDeviceText(thisDeviceClipboard);\r\n    setIsEditingThisDevice(true);\r\n  };\r\n\r\n  const saveThisDeviceEdit = async () => {\r\n    const newContent = editingThisDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setThisDeviceClipboard(newContent);\r\n    try {\r\n      await navigator.clipboard.writeText(newContent);\r\n    } catch (error) {\r\n      console.warn('Failed to update clipboard:', error);\r\n    }\r\n\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n    showMessage('✅ This Device clipboard updated!');\r\n  };\r\n\r\n  const cancelThisDeviceEdit = () => {\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n  };\r\n\r\n  const startEditingConnectedDevice = () => {\r\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\r\n    setIsEditingConnectedDevice(true);\r\n  };\r\n\r\n  const saveConnectedDeviceEdit = () => {\r\n    const newContent = editingConnectedDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setConnectedDeviceClipboard(newContent);\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n    showMessage('✅ Connected Device clipboard updated!');\r\n  };\r\n\r\n  const cancelConnectedDeviceEdit = () => {\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n  };\r\n\r\n  const syncNow = () => {\r\n    showMessage('🔄 Syncing with paired devices...');\r\n    setTimeout(() => {\r\n      showMessage('✅ Sync completed!');\r\n    }, 1000);\r\n  };\r\n\r\n  const toggleAlwaysOnTop = () => {\r\n    setIsAlwaysOnTop(!isAlwaysOnTop);\r\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\r\n  };\r\n\r\n  const minimizeToTray = () => {\r\n    showMessage('➖ Minimizing to background...');\r\n  };\r\n\r\n  const toggleFloatingOverlay = () => {\r\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\r\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\r\n  };\r\n\r\n  // Device Management Functions\r\n  const removeDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🗑️ Device removed from paired devices');\r\n  };\r\n\r\n  const connectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'connected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('✅ Device connected successfully');\r\n  };\r\n\r\n  const disconnectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'disconnected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🔌 Device disconnected');\r\n  };\r\n\r\n  const pairDevice = (deviceId) => {\r\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\r\n    if (deviceToPair) {\r\n      const newPairedDevice = {\r\n        ...deviceToPair,\r\n        status: 'connected',\r\n        lastSeen: 'Just now'\r\n      };\r\n      setPairedDevices([...pairedDevices, newPairedDevice]);\r\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\r\n    }\r\n  };\r\n\r\n  const refreshDiscovery = () => {\r\n    if (isDiscovering) return;\r\n\r\n    setIsDiscovering(true);\r\n    showMessage('🔍 Scanning for devices...');\r\n\r\n    // Simulate discovery process\r\n    setTimeout(() => {\r\n      setNetworkDevices(discoveredDevices);\r\n      setIsDiscovering(false);\r\n      showMessage(`📱 Found ${discoveredDevices.length} available devices`);\r\n    }, 2000);\r\n  };\r\n\r\n  const scanQRCode = () => {\r\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\r\n  };\r\n\r\n  const generateQRCode = () => {\r\n    // Generate connection info for QR code\r\n    const connectionInfo = {\r\n      deviceName: deviceInfo.name,\r\n      deviceType: deviceInfo.type,\r\n      ipAddress: deviceInfo.ipAddress,\r\n      port: 3001,\r\n      protocol: 'clipsy-sync',\r\n      timestamp: Date.now()\r\n    };\r\n\r\n    const qrData = JSON.stringify(connectionInfo);\r\n    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(qrData)}`;\r\n\r\n    // Create and show QR code modal\r\n    const qrModal = document.createElement('div');\r\n    qrModal.className = 'qr-modal-overlay';\r\n    qrModal.innerHTML = `\r\n      <div class=\"qr-modal-content\">\r\n        <div class=\"qr-modal-header\">\r\n          <h3>📱 Scan to Connect Android Device</h3>\r\n          <button class=\"qr-modal-close\">✕</button>\r\n        </div>\r\n        <div class=\"qr-modal-body\">\r\n          <img src=\"${qrCodeUrl}\" alt=\"QR Code\" class=\"qr-code-image\" />\r\n          <p class=\"qr-instructions\">\r\n            1. Open Clipsy app on your Android device<br/>\r\n            2. Go to Settings → Device Discovery<br/>\r\n            3. Tap \"Scan QR\" and scan this code<br/>\r\n            4. Your devices will be paired automatically\r\n          </p>\r\n          <div class=\"qr-device-info\">\r\n            <p><strong>Device:</strong> ${deviceInfo.name}</p>\r\n            <p><strong>IP:</strong> ${deviceInfo.ipAddress}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    `;\r\n\r\n    document.body.appendChild(qrModal);\r\n\r\n    // Close modal functionality\r\n    const closeModal = () => {\r\n      document.body.removeChild(qrModal);\r\n    };\r\n\r\n    qrModal.querySelector('.qr-modal-close').onclick = closeModal;\r\n    qrModal.onclick = (e) => {\r\n      if (e.target === qrModal) closeModal();\r\n    };\r\n\r\n    showMessage('📱 QR Code generated! Scan with Android Clipsy app to connect.');\r\n  };\r\n\r\n  return (\r\n    <div className=\"app-container\">\r\n      {/* Header */}\r\n      <div className=\"header\">\r\n        <div className=\"title-container\">\r\n          <div className=\"logo-container\">\r\n            <img\r\n              src=\"/clipsy-logo-no-bg.png\"\r\n              alt=\"Clipsy Logo\"\r\n              className=\"app-logo\"\r\n            />\r\n            <div className={`connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`}></div>\r\n          </div>\r\n          <h1 className=\"title\">Clipsy</h1>\r\n        </div>\r\n        <div className=\"header-actions\">\r\n          <button\r\n            className={`icon-button ${isFloatingOverlayVisible ? 'active' : ''}`}\r\n            onClick={toggleFloatingOverlay}\r\n            title=\"Toggle floating clipboard widget\"\r\n          >\r\n            📋\r\n          </button>\r\n          <button\r\n            className={`icon-button ${isAlwaysOnTop ? 'active' : ''}`}\r\n            onClick={toggleAlwaysOnTop}\r\n            title=\"Pin to top\"\r\n          >\r\n            <div className=\"pin-icon\">\r\n              <div className=\"pin-head\"></div>\r\n              <div className=\"pin-body\"></div>\r\n            </div>\r\n          </button>\r\n          <button className=\"icon-button\" onClick={minimizeToTray} title=\"Minimize\">\r\n            ➖\r\n          </button>\r\n          <button className=\"icon-button\" onClick={() => setShowSettings(true)} title=\"Settings\">\r\n            ⚙️\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"success-message\">\r\n          <span>{successMessage}</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      <div className=\"main-content\">\r\n        {/* This Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <div className=\"device-section-title-container\">\r\n              <h2 className=\"device-section-title\">💻 This Device</h2>\r\n              <p className=\"device-section-subtitle\">{deviceInfo.name}</p>\r\n            </div>\r\n          </div>\r\n\r\n          {isEditingThisDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingThisDeviceText}\r\n                onChange={(e) => setEditingThisDeviceText(e.target.value)}\r\n                placeholder=\"Edit this device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button this-device\" onClick={saveThisDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button this-device\" onClick={cancelThisDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content this-device-content\"\r\n                onClick={() => copyToClipboard(thisDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{thisDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Real-time sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingThisDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Connected Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <h2 className=\"device-section-title\">🔗 Connected Device</h2>\r\n            <p className=\"device-section-subtitle\">Android Phone - Personal 🟢</p>\r\n          </div>\r\n\r\n          {isEditingConnectedDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingConnectedDeviceText}\r\n                onChange={(e) => setEditingConnectedDeviceText(e.target.value)}\r\n                placeholder=\"Edit connected device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button connected-device\" onClick={saveConnectedDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button connected-device\" onClick={cancelConnectedDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content connected-device-content\"\r\n                onClick={() => copyToClipboard(connectedDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{connectedDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Bidirectional sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingConnectedDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Clipboard History */}\r\n        <h2 className=\"history-title\">Clipboard History</h2>\r\n        <div className=\"history-list\">\r\n          {historyItems.map((item) => (\r\n            <div\r\n              key={item.id}\r\n              className=\"history-item\"\r\n              onClick={() => selectItem(item)}\r\n            >\r\n              <div className=\"history-item-header\">\r\n                <span className=\"timestamp\">{item.timestamp}</span>\r\n                <button\r\n                  className=\"delete-button\"\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    deleteHistoryItem(item.id);\r\n                  }}\r\n                >\r\n                  ✕\r\n                </button>\r\n              </div>\r\n              <p className=\"item-content\">{item.content}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Floating Sync Button */}\r\n      <button className=\"floating-sync-button\" onClick={syncNow}>\r\n        <img src=\"/sync.png\" alt=\"Sync\" className=\"sync-icon\" />\r\n      </button>\r\n\r\n      {/* Settings Modal */}\r\n      {showSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSettings(false)}>\r\n          <div className=\"settings-sidebar\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              {/* Device Info */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Device Info</h3>\r\n                <div className=\"device-info-card\">\r\n                  <p className=\"device-name\">{deviceInfo.name}</p>\r\n                  <p className=\"device-detail\">{deviceInfo.type}</p>\r\n                  <p className=\"device-detail\">IP: {deviceInfo.ipAddress}</p>\r\n                  <div className=\"device-status-row\">\r\n                    <div className={`device-status-indicator ${deviceInfo.status}`}></div>\r\n                    <span className={`device-status-text ${deviceInfo.status}`}>\r\n                      Status: {deviceInfo.status === 'active' ? 'Connected' : 'Disconnected'}\r\n                    </span>\r\n                  </div>\r\n                  {deviceInfo.status === 'disconnected' && (\r\n                    <p className=\"device-detail disconnected-notice\">\r\n                      ⚠️ No devices connected - Use QR code or device discovery to connect Android devices\r\n                    </p>\r\n                  )}\r\n                  <button\r\n                    className=\"refresh-device-name-button\"\r\n                    onClick={async () => {\r\n                      const newComputerName = await getWindowsComputerName();\r\n                      showMessage(`🖥️ Computer name refreshed: ${newComputerName}`);\r\n                      // Trigger device info refresh\r\n                      const hasConnectedDevices = pairedDevices.some(device => device.status === 'connected');\r\n                      const deviceStatus = hasConnectedDevices ? 'active' : 'disconnected';\r\n                      let osVersion = 'Windows';\r\n                      if (navigator.userAgent) {\r\n                        const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\r\n                        if (windowsMatch) {\r\n                          const version = windowsMatch[1];\r\n                          switch (version) {\r\n                            case '10.0': osVersion = 'Windows 10/11'; break;\r\n                            case '6.3': osVersion = 'Windows 8.1'; break;\r\n                            case '6.2': osVersion = 'Windows 8'; break;\r\n                            case '6.1': osVersion = 'Windows 7'; break;\r\n                            default: osVersion = `Windows NT ${version}`;\r\n                          }\r\n                        }\r\n                      }\r\n                      setDeviceInfo(prev => ({\r\n                        ...prev,\r\n                        name: `${newComputerName} - ${osVersion}`,\r\n                        type: osVersion,\r\n                        status: deviceStatus,\r\n                        lastSeen: deviceStatus === 'active' ? 'now' : 'no connected devices'\r\n                      }));\r\n                    }}\r\n                  >\r\n                    🔄 Refresh Computer Name\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Paired Devices */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Paired Devices</h3>\r\n                {pairedDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No paired devices found</p>\r\n                    <p className=\"empty-device-subtext\">Use QR code or device discovery to pair devices</p>\r\n                  </div>\r\n                ) : (\r\n                  pairedDevices.map((device) => (\r\n                    <div key={device.id} className=\"enhanced-device-card\">\r\n                      {/* Remove Button - White X at top right corner */}\r\n                      <button\r\n                        className=\"remove-button-top-right\"\r\n                        onClick={() => removeDevice(device.id)}\r\n                      >\r\n                        ×\r\n                      </button>\r\n\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <div className=\"device-status-row\">\r\n                          <div className={`device-status-indicator ${device.status}`}></div>\r\n                          <span className={`device-status-text ${device.status}`}>\r\n                            {device.status === 'connected' ? 'Connected' : 'Disconnected'} • {device.lastSeen}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Connect/Disconnect Button - Bottom Right Corner */}\r\n                      <button\r\n                        className={`device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`}\r\n                        onClick={() => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id)}\r\n                      >\r\n                        {device.status === 'connected' ? 'Disconnect' : 'Connect'}\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n              </div>\r\n\r\n              {/* Device Discovery */}\r\n              <div className=\"settings-section\">\r\n                <div className=\"section-header\">\r\n                  <h3 className=\"section-title\">Device Discovery</h3>\r\n                  <button\r\n                    className={`discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`}\r\n                    onClick={refreshDiscovery}\r\n                    disabled={isDiscovering}\r\n                  >\r\n                    {isDiscovering ? '🔍 Scanning...' : 'Discover'}\r\n                  </button>\r\n                </div>\r\n                {networkDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No devices found</p>\r\n                    <p className=\"empty-device-subtext\">\r\n                      {isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'}\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  networkDevices.map((device) => (\r\n                    <div key={device.id} className=\"discovered-device-card\">\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <p className=\"device-card-last-seen\">{device.lastSeen}</p>\r\n                      </div>\r\n                      <button\r\n                        className=\"pair-button\"\r\n                        onClick={() => pairDevice(device.id)}\r\n                      >\r\n                        Pair\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n\r\n                <div className=\"qr-buttons-container\">\r\n                  <button className=\"qr-generate-button\" onClick={generateQRCode}>\r\n                    Generate QR\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Additional Settings */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Additional Settings</h3>\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => setShowSyncSettings(true)}\r\n                >\r\n                  <span className=\"setting-item-text\">Sync Settings</span>\r\n                  <span className=\"setting-item-arrow\">›</span>\r\n                </button>\r\n\r\n                {/* Background Sync Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    const newValue = !backgroundSyncEnabled;\r\n                    setBackgroundSyncEnabled(newValue);\r\n                    showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Background Sync</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {backgroundSyncEnabled ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n\r\n                {/* Network Discovery Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    if (isDiscovering) {\r\n                      setIsDiscovering(false);\r\n                      showMessage('Network discovery stopped');\r\n                    } else {\r\n                      setIsDiscovering(true);\r\n                      showMessage('Network discovery started');\r\n                    }\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Network Discovery</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {isDiscovering ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Sync Settings Modal */}\r\n      {showSyncSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSyncSettings(false)}>\r\n          <div className=\"sync-settings-modal\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Sync Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSyncSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              <div className=\"settings-section\">\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Auto Sync</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.autoSync}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, autoSync: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Sync Delay: {syncSettings.syncDelay} seconds</label>\r\n                  <p className=\"sync-setting-description\">\r\n                    {syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`}\r\n                  </p>\r\n                  <div className=\"sync-delay-controls\">\r\n                    <button\r\n                      className=\"sync-delay-button\"\r\n                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.max(0, syncSettings.syncDelay - 1)})}\r\n                    >\r\n                      -\r\n                    </button>\r\n                    <span className=\"sync-delay-value\">{syncSettings.syncDelay}s</span>\r\n                    <button\r\n                      className=\"sync-delay-button\"\r\n                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.min(30, syncSettings.syncDelay + 1)})}\r\n                    >\r\n                      +\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Sync on Connect</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.syncOnConnect}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, syncOnConnect: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Bidirectional Sync</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.bidirectional}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, bidirectional: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                {/* Cross-Platform Sync Controls */}\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.')}\r\n                  >\r\n                    📱 Windows ↔ Android Sync\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.')}\r\n                  >\r\n                    🖥️ Windows ↔ Windows Sync\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.')}\r\n                  >\r\n                    🚀 Sync All Devices\r\n                  </button>\r\n                </div>\r\n\r\n                {/* Floating Overlay Button Settings */}\r\n                <div className=\"settings-section\">\r\n                  <h3 className=\"settings-section-title\">📋 Floating Overlay Button Settings</h3>\r\n                  <p className=\"settings-description\">\r\n                    Configure the floating overlay button for quick access to connected device clipboards\r\n                  </p>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Enable Floating Overlay Button</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('📋 Floating overlay button is always enabled for accessibility')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Show Device Count Badge</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('🔢 Device count badge enabled')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Auto-hide After Copy</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('⏱️ Auto-hide after copy enabled')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <p className=\"settings-note\">\r\n                    💡 The floating overlay button (📋) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGP,QAAQ,CAAC,sIAAsI,CAAC;EACtM,MAAM,CAACQ,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGT,QAAQ,CAAC,0IAA0I,CAAC;EACpN,MAAM,CAACU,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACY,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACc,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACgB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEhF,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAC/C;IAAEoB,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,iDAAiD;IAAEC,SAAS,EAAE;EAAgB,CAAC,EACnG;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,gLAAgL;IAAEC,SAAS,EAAE;EAAiB,CAAC,EACnO;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,+BAA+B;IAAEC,SAAS,EAAE;EAAa,CAAC,EAC9E;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,sEAAsE;IAAEC,SAAS,EAAE;EAAc,CAAC,CACvH,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+B,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;;EAE/E;EACA,MAAM,CAACiC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC;IAC/CyC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,CACjD;IACEoB,EAAE,EAAE,WAAW;IACf2B,IAAI,EAAE,0BAA0B;IAChCC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,SAAS;IACb2B,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,cAAc;IACtBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,CACnC;IACEoB,EAAE,EAAE,cAAc;IAClB2B,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,uBAAuB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,gBAAgB;IACpB2B,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,uBAAuB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC;IAC3C+C,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACApD,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAMsD,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,IAAIC,SAAS,GAAG,SAAS;;QAEzB;QACA,IAAIC,SAAS,CAACC,aAAa,EAAE;UAC3B,MAAMC,QAAQ,GAAGF,SAAS,CAACC,aAAa,CAACC,QAAQ;UACjDH,SAAS,GAAGG,QAAQ,IAAI,SAAS;QACnC,CAAC,MAAM,IAAIF,SAAS,CAACG,SAAS,EAAE;UAC9B,MAAMC,YAAY,GAAGJ,SAAS,CAACG,SAAS,CAACE,KAAK,CAAC,uBAAuB,CAAC;UACvE,IAAID,YAAY,EAAE;YAChB,MAAME,OAAO,GAAGF,YAAY,CAAC,CAAC,CAAC;YAC/B,QAAQE,OAAO;cACb,KAAK,MAAM;gBAAEP,SAAS,GAAG,eAAe;gBAAE;cAC1C,KAAK,KAAK;gBAAEA,SAAS,GAAG,aAAa;gBAAE;cACvC,KAAK,KAAK;gBAAEA,SAAS,GAAG,WAAW;gBAAE;cACrC,KAAK,KAAK;gBAAEA,SAAS,GAAG,WAAW;gBAAE;cACrC;gBAASA,SAAS,GAAG,cAAcO,OAAO,EAAE;YAC9C;UACF;QACF;;QAEA;QACA,MAAMC,YAAY,GAAG,MAAMC,sBAAsB,CAAC,CAAC;QACnD,MAAMC,UAAU,GAAG,GAAGF,YAAY,MAAMR,SAAS,EAAE;;QAEnD;QACA,MAAMW,mBAAmB,GAAGtB,aAAa,CAACuB,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACpB,MAAM,KAAK,WAAW,CAAC;QACvF,MAAMqB,YAAY,GAAGH,mBAAmB,GAAG,QAAQ,GAAG,cAAc;QAEpEb,aAAa,CAACiB,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPxB,IAAI,EAAEmB,UAAU;UAChBlB,IAAI,EAAEQ,SAAS;UACfP,MAAM,EAAEqB,YAAY;UACpBpB,QAAQ,EAAEoB,YAAY,KAAK,QAAQ,GAAG,KAAK,GAAG;QAChD,CAAC,CAAC,CAAC;QAEHE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;UAAET,YAAY;UAAEE,UAAU;UAAEV;QAAU,CAAC,CAAC;MAC9E,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACdF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,KAAK,CAAC;QAChD,MAAMP,mBAAmB,GAAGtB,aAAa,CAACuB,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACpB,MAAM,KAAK,WAAW,CAAC;QACvF,MAAMqB,YAAY,GAAGH,mBAAmB,GAAG,QAAQ,GAAG,cAAc;QAEpEb,aAAa,CAACiB,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPxB,IAAI,EAAE,mBAAmB;UACzBC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAEqB,YAAY;UACpBpB,QAAQ,EAAEoB,YAAY,KAAK,QAAQ,GAAG,KAAK,GAAG;QAChD,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAEDf,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACV,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM8B,WAAW,GAAIC,IAAI,IAAK;IAC5B9C,iBAAiB,CAAC8C,IAAI,CAAC;IACvBC,UAAU,CAAC,MAAM/C,iBAAiB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMmC,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF;MACA,IAAIa,MAAM,CAACC,WAAW,EAAE;QACtB,IAAI;UACF,MAAMf,YAAY,GAAG,MAAMc,MAAM,CAACC,WAAW,CAACC,eAAe,CAAC,CAAC;UAC/D,IAAIhB,YAAY,EAAE,OAAOA,YAAY,CAACiB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC5D,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVX,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEU,CAAC,CAAC;QAC3C;MACF;;MAEA;MACA,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,GAAG,EAAE;QACjD,IAAID,OAAO,CAACC,GAAG,CAACC,YAAY,EAAE,OAAOF,OAAO,CAACC,GAAG,CAACC,YAAY,CAACJ,WAAW,CAAC,CAAC;QAC3E,IAAIE,OAAO,CAACC,GAAG,CAACE,QAAQ,EAAE,OAAOH,OAAO,CAACC,GAAG,CAACE,QAAQ,CAACL,WAAW,CAAC,CAAC;MACrE;;MAEA;MACA,IAAI;QACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,oBAAoB,CAAC;QAClD,IAAID,QAAQ,CAACE,EAAE,EAAE;UACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;UAClC,IAAID,IAAI,CAAC3B,YAAY,EAAE,OAAO2B,IAAI,CAAC3B,YAAY,CAACkB,WAAW,CAAC,CAAC;QAC/D;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVX,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEU,CAAC,CAAC;MACtC;;MAEA;MACA,IAAIL,MAAM,CAACe,QAAQ,CAACC,QAAQ,IACxBhB,MAAM,CAACe,QAAQ,CAACC,QAAQ,KAAK,WAAW,IACxChB,MAAM,CAACe,QAAQ,CAACC,QAAQ,KAAK,WAAW,EAAE;QAC5C,OAAOhB,MAAM,CAACe,QAAQ,CAACC,QAAQ,CAACZ,WAAW,CAAC,CAAC;MAC/C;;MAEA;MACA,IAAIa,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;MAC7D,IAAI,CAACF,UAAU,EAAE;QACf;QACA,MAAMnC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAMsC,MAAM,GAAG,GAAGpB,MAAM,CAACoB,MAAM,CAACC,KAAK,IAAIrB,MAAM,CAACoB,MAAM,CAACE,MAAM,EAAE;QAC/D,MAAMC,QAAQ,GAAGC,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACC,QAAQ;QACjE,MAAMC,QAAQ,GAAGjD,SAAS,CAACiD,QAAQ;;QAEnC;QACA,MAAMC,SAAS,GAAG,GAAG/C,SAAS,IAAIsC,MAAM,IAAIG,QAAQ,IAAIK,QAAQ,EAAE;QAClE,IAAIE,IAAI,GAAG,CAAC;QACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACzC,MAAME,IAAI,GAAGJ,SAAS,CAACK,UAAU,CAACH,CAAC,CAAC;UACpCD,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAIG,IAAI;UAClCH,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC,CAAC;QACtB;QAEAb,UAAU,GAAG,cAAckB,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC,CAACO,QAAQ,CAAC,EAAE,CAAC,CAACjC,WAAW,CAAC,CAAC,CAACkC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAClFpB,YAAY,CAACqB,OAAO,CAAC,sBAAsB,EAAEtB,UAAU,CAAC;MAC1D;MAEA,OAAOA,UAAU;IACnB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEC,KAAK,CAAC;MACvD,OAAO,YAAY;IACrB;EACF,CAAC;EAED,MAAM4C,eAAe,GAAG,MAAOjG,OAAO,IAAK;IACzC,IAAI;MACF,MAAMoC,SAAS,CAAC8D,SAAS,CAACC,SAAS,CAACnG,OAAO,CAAC;MAC5Cd,sBAAsB,CAACc,OAAO,CAAC;MAC/BsD,WAAW,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDC,WAAW,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAM8C,UAAU,GAAIC,IAAI,IAAK;IAC3BJ,eAAe,CAACI,IAAI,CAACrG,OAAO,CAAC;IAC7BZ,2BAA2B,CAACiH,IAAI,CAACrG,OAAO,CAAC;EAC3C,CAAC;EAED,MAAMsG,iBAAiB,GAAIC,MAAM,IAAK;IACpC,MAAMC,cAAc,GAAG3G,YAAY,CAAC4G,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAACtG,EAAE,KAAKwG,MAAM,CAAC;IACtEzG,eAAe,CAAC0G,cAAc,CAAC;IAC/BlD,WAAW,CAAC,2BAA2B,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMoD,sBAAsB,GAAGA,CAAA,KAAM;IACnChH,wBAAwB,CAACT,mBAAmB,CAAC;IAC7CK,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMqH,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,UAAU,GAAGnH,qBAAqB,CAACmE,IAAI,CAAC,CAAC;IAC/C,IAAI,CAACgD,UAAU,EAAE;MACftD,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEApE,sBAAsB,CAAC0H,UAAU,CAAC;IAClC,IAAI;MACF,MAAMxE,SAAS,CAAC8D,SAAS,CAACC,SAAS,CAACS,UAAU,CAAC;IACjD,CAAC,CAAC,OAAOvD,KAAK,EAAE;MACdF,OAAO,CAAC0D,IAAI,CAAC,6BAA6B,EAAExD,KAAK,CAAC;IACpD;IAEA/D,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;IAC5B4D,WAAW,CAAC,kCAAkC,CAAC;EACjD,CAAC;EAED,MAAMwD,oBAAoB,GAAGA,CAAA,KAAM;IACjCxH,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;EAC9B,CAAC;EAED,MAAMqH,2BAA2B,GAAGA,CAAA,KAAM;IACxCnH,6BAA6B,CAACT,wBAAwB,CAAC;IACvDK,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMwH,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMJ,UAAU,GAAGjH,0BAA0B,CAACiE,IAAI,CAAC,CAAC;IACpD,IAAI,CAACgD,UAAU,EAAE;MACftD,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEAlE,2BAA2B,CAACwH,UAAU,CAAC;IACvCpH,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;IACjC0D,WAAW,CAAC,uCAAuC,CAAC;EACtD,CAAC;EAED,MAAM2D,yBAAyB,GAAGA,CAAA,KAAM;IACtCzH,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;EACnC,CAAC;EAED,MAAMsH,OAAO,GAAGA,CAAA,KAAM;IACpB5D,WAAW,CAAC,mCAAmC,CAAC;IAChDE,UAAU,CAAC,MAAM;MACfF,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM6D,iBAAiB,GAAGA,CAAA,KAAM;IAC9B5G,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChCgD,WAAW,CAAChD,aAAa,GAAG,0BAA0B,GAAG,sBAAsB,CAAC;EAClF,CAAC;EAED,MAAM8G,cAAc,GAAGA,CAAA,KAAM;IAC3B9D,WAAW,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAM+D,qBAAqB,GAAGA,CAAA,KAAM;IAClC1G,2BAA2B,CAAC,CAACD,wBAAwB,CAAC;IACtD4C,WAAW,CAAC5C,wBAAwB,GAAG,2BAA2B,GAAG,0BAA0B,CAAC;EAClG,CAAC;;EAED;EACA,MAAM4G,YAAY,GAAIC,QAAQ,IAAK;IACjC,MAAMC,cAAc,GAAGhG,aAAa,CAACiF,MAAM,CAACzD,MAAM,IAAIA,MAAM,CAACjD,EAAE,KAAKwH,QAAQ,CAAC;IAC7E9F,gBAAgB,CAAC+F,cAAc,CAAC;IAChClE,WAAW,CAAC,wCAAwC,CAAC;EACvD,CAAC;EAED,MAAMmE,aAAa,GAAIF,QAAQ,IAAK;IAClC,MAAMC,cAAc,GAAGhG,aAAa,CAACkG,GAAG,CAAC1E,MAAM,IAC7CA,MAAM,CAACjD,EAAE,KAAKwH,QAAQ,GAAG;MAAE,GAAGvE,MAAM;MAAEpB,MAAM,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAW,CAAC,GAAGmB,MACtF,CAAC;IACDvB,gBAAgB,CAAC+F,cAAc,CAAC;IAChClE,WAAW,CAAC,iCAAiC,CAAC;EAChD,CAAC;EAED,MAAMqE,gBAAgB,GAAIJ,QAAQ,IAAK;IACrC,MAAMC,cAAc,GAAGhG,aAAa,CAACkG,GAAG,CAAC1E,MAAM,IAC7CA,MAAM,CAACjD,EAAE,KAAKwH,QAAQ,GAAG;MAAE,GAAGvE,MAAM;MAAEpB,MAAM,EAAE,cAAc;MAAEC,QAAQ,EAAE;IAAW,CAAC,GAAGmB,MACzF,CAAC;IACDvB,gBAAgB,CAAC+F,cAAc,CAAC;IAChClE,WAAW,CAAC,wBAAwB,CAAC;EACvC,CAAC;EAED,MAAMsE,UAAU,GAAIL,QAAQ,IAAK;IAC/B,MAAMM,YAAY,GAAG9F,iBAAiB,CAAC+F,IAAI,CAAC9E,MAAM,IAAIA,MAAM,CAACjD,EAAE,KAAKwH,QAAQ,CAAC;IAC7E,IAAIM,YAAY,EAAE;MAChB,MAAME,eAAe,GAAG;QACtB,GAAGF,YAAY;QACfjG,MAAM,EAAE,WAAW;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDJ,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAEuG,eAAe,CAAC,CAAC;MACrDzE,WAAW,CAAC,8BAA8BuE,YAAY,CAACnG,IAAI,EAAE,CAAC;IAChE;EACF,CAAC;EAED,MAAMsG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIhH,aAAa,EAAE;IAEnBC,gBAAgB,CAAC,IAAI,CAAC;IACtBqC,WAAW,CAAC,4BAA4B,CAAC;;IAEzC;IACAE,UAAU,CAAC,MAAM;MACfzC,iBAAiB,CAACgB,iBAAiB,CAAC;MACpCd,gBAAgB,CAAC,KAAK,CAAC;MACvBqC,WAAW,CAAC,YAAYvB,iBAAiB,CAAC0D,MAAM,oBAAoB,CAAC;IACvE,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMwC,UAAU,GAAGA,CAAA,KAAM;IACvB3E,WAAW,CAAC,2EAA2E,CAAC;EAC1F,CAAC;EAED,MAAM4E,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAMC,cAAc,GAAG;MACrBtF,UAAU,EAAEb,UAAU,CAACN,IAAI;MAC3B0G,UAAU,EAAEpG,UAAU,CAACL,IAAI;MAC3BG,SAAS,EAAEE,UAAU,CAACF,SAAS;MAC/BuG,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,aAAa;MACvBrI,SAAS,EAAEsI,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,MAAMC,MAAM,GAAGC,IAAI,CAACC,SAAS,CAACR,cAAc,CAAC;IAC7C,MAAMS,SAAS,GAAG,iEAAiEC,kBAAkB,CAACJ,MAAM,CAAC,EAAE;;IAE/G;IACA,MAAMK,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC7CF,OAAO,CAACG,SAAS,GAAG,kBAAkB;IACtCH,OAAO,CAACI,SAAS,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBN,SAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C5G,UAAU,CAACN,IAAI;AACzD,sCAAsCM,UAAU,CAACF,SAAS;AAC1D;AACA;AACA;AACA,KAAK;IAEDiH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,OAAO,CAAC;;IAElC;IACA,MAAMO,UAAU,GAAGA,CAAA,KAAM;MACvBN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,OAAO,CAAC;IACpC,CAAC;IAEDA,OAAO,CAACS,aAAa,CAAC,iBAAiB,CAAC,CAACC,OAAO,GAAGH,UAAU;IAC7DP,OAAO,CAACU,OAAO,GAAI1F,CAAC,IAAK;MACvB,IAAIA,CAAC,CAAC2F,MAAM,KAAKX,OAAO,EAAEO,UAAU,CAAC,CAAC;IACxC,CAAC;IAED/F,WAAW,CAAC,gEAAgE,CAAC;EAC/E,CAAC;EAED,oBACExE,OAAA;IAAKmK,SAAS,EAAC,eAAe;IAAAS,QAAA,gBAE5B5K,OAAA;MAAKmK,SAAS,EAAC,QAAQ;MAAAS,QAAA,gBACrB5K,OAAA;QAAKmK,SAAS,EAAC,iBAAiB;QAAAS,QAAA,gBAC9B5K,OAAA;UAAKmK,SAAS,EAAC,gBAAgB;UAAAS,QAAA,gBAC7B5K,OAAA;YACE6K,GAAG,EAAC,wBAAwB;YAC5BC,GAAG,EAAC,aAAa;YACjBX,SAAS,EAAC;UAAU;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACFlL,OAAA;YAAKmK,SAAS,EAAE,kBAAkBzH,aAAa,CAACuB,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACpB,MAAM,KAAK,WAAW,CAAC,GAAG,WAAW,GAAG,cAAc;UAAG;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnI,CAAC,eACNlL,OAAA;UAAImK,SAAS,EAAC,OAAO;UAAAS,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACNlL,OAAA;QAAKmK,SAAS,EAAC,gBAAgB;QAAAS,QAAA,gBAC7B5K,OAAA;UACEmK,SAAS,EAAE,eAAevI,wBAAwB,GAAG,QAAQ,GAAG,EAAE,EAAG;UACrEuJ,OAAO,EAAE5C,qBAAsB;UAC/B6C,KAAK,EAAC,kCAAkC;UAAAR,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlL,OAAA;UACEmK,SAAS,EAAE,eAAe3I,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC1D2J,OAAO,EAAE9C,iBAAkB;UAC3B+C,KAAK,EAAC,YAAY;UAAAR,QAAA,eAElB5K,OAAA;YAAKmK,SAAS,EAAC,UAAU;YAAAS,QAAA,gBACvB5K,OAAA;cAAKmK,SAAS,EAAC;YAAU;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChClL,OAAA;cAAKmK,SAAS,EAAC;YAAU;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTlL,OAAA;UAAQmK,SAAS,EAAC,aAAa;UAACgB,OAAO,EAAE7C,cAAe;UAAC8C,KAAK,EAAC,UAAU;UAAAR,QAAA,EAAC;QAE1E;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlL,OAAA;UAAQmK,SAAS,EAAC,aAAa;UAACgB,OAAO,EAAEA,CAAA,KAAM9J,eAAe,CAAC,IAAI,CAAE;UAAC+J,KAAK,EAAC,UAAU;UAAAR,QAAA,EAAC;QAEvF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLxJ,cAAc,iBACb1B,OAAA;MAAKmK,SAAS,EAAC,iBAAiB;MAAAS,QAAA,eAC9B5K,OAAA;QAAA4K,QAAA,EAAOlJ;MAAc;QAAAqJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDlL,OAAA;MAAKmK,SAAS,EAAC,cAAc;MAAAS,QAAA,gBAE3B5K,OAAA;QAAKmK,SAAS,EAAC,gBAAgB;QAAAS,QAAA,gBAC7B5K,OAAA;UAAKmK,SAAS,EAAC,uBAAuB;UAAAS,QAAA,eACpC5K,OAAA;YAAKmK,SAAS,EAAC,gCAAgC;YAAAS,QAAA,gBAC7C5K,OAAA;cAAImK,SAAS,EAAC,sBAAsB;cAAAS,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDlL,OAAA;cAAGmK,SAAS,EAAC,yBAAyB;cAAAS,QAAA,EAAE1H,UAAU,CAACN;YAAI;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL3K,mBAAmB,gBAClBP,OAAA;UAAKmK,SAAS,EAAC,gBAAgB;UAAAS,QAAA,gBAC7B5K,OAAA;YACEmK,SAAS,EAAC,iBAAiB;YAC3BkB,KAAK,EAAE1K,qBAAsB;YAC7B2K,QAAQ,EAAGtG,CAAC,IAAKpE,wBAAwB,CAACoE,CAAC,CAAC2F,MAAM,CAACU,KAAK,CAAE;YAC1DE,WAAW,EAAC,uCAAuC;YACnDC,SAAS;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACFlL,OAAA;YAAKmK,SAAS,EAAC,cAAc;YAAAS,QAAA,gBAC3B5K,OAAA;cAAQmK,SAAS,EAAC,yBAAyB;cAACgB,OAAO,EAAEtD,kBAAmB;cAAA+C,QAAA,EAAC;YAEzE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlL,OAAA;cAAQmK,SAAS,EAAC,2BAA2B;cAACgB,OAAO,EAAEnD,oBAAqB;cAAA4C,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENlL,OAAA;UAAKmK,SAAS,EAAC,kBAAkB;UAAAS,QAAA,eAC/B5K,OAAA;YACEmK,SAAS,EAAC,uCAAuC;YACjDgB,OAAO,EAAEA,CAAA,KAAMhE,eAAe,CAAChH,mBAAmB,CAAE;YAAAyK,QAAA,gBAEpD5K,OAAA;cAAGmK,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAEzK;YAAmB;cAAA4K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDlL,OAAA;cAAGmK,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAC;YAA8B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChElL,OAAA;cAAQmK,SAAS,EAAC,oBAAoB;cAACgB,OAAO,EAAEvD,sBAAuB;cAAAgD,QAAA,EAAC;YAExE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNlL,OAAA;QAAKmK,SAAS,EAAC,gBAAgB;QAAAS,QAAA,gBAC7B5K,OAAA;UAAKmK,SAAS,EAAC,uBAAuB;UAAAS,QAAA,gBACpC5K,OAAA;YAAImK,SAAS,EAAC,sBAAsB;YAAAS,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DlL,OAAA;YAAGmK,SAAS,EAAC,yBAAyB;YAAAS,QAAA,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,EAELzK,wBAAwB,gBACvBT,OAAA;UAAKmK,SAAS,EAAC,gBAAgB;UAAAS,QAAA,gBAC7B5K,OAAA;YACEmK,SAAS,EAAC,iBAAiB;YAC3BkB,KAAK,EAAExK,0BAA2B;YAClCyK,QAAQ,EAAGtG,CAAC,IAAKlE,6BAA6B,CAACkE,CAAC,CAAC2F,MAAM,CAACU,KAAK,CAAE;YAC/DE,WAAW,EAAC,4CAA4C;YACxDC,SAAS;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACFlL,OAAA;YAAKmK,SAAS,EAAC,cAAc;YAAAS,QAAA,gBAC3B5K,OAAA;cAAQmK,SAAS,EAAC,8BAA8B;cAACgB,OAAO,EAAEjD,uBAAwB;cAAA0C,QAAA,EAAC;YAEnF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlL,OAAA;cAAQmK,SAAS,EAAC,gCAAgC;cAACgB,OAAO,EAAEhD,yBAA0B;cAAAyC,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENlL,OAAA;UAAKmK,SAAS,EAAC,kBAAkB;UAAAS,QAAA,eAC/B5K,OAAA;YACEmK,SAAS,EAAC,4CAA4C;YACtDgB,OAAO,EAAEA,CAAA,KAAMhE,eAAe,CAAC9G,wBAAwB,CAAE;YAAAuK,QAAA,gBAEzD5K,OAAA;cAAGmK,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAEvK;YAAwB;cAAA0K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DlL,OAAA;cAAGmK,SAAS,EAAC,gBAAgB;cAAAS,QAAA,EAAC;YAAkC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpElL,OAAA;cAAQmK,SAAS,EAAC,oBAAoB;cAACgB,OAAO,EAAElD,2BAA4B;cAAA2C,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNlL,OAAA;QAAImK,SAAS,EAAC,eAAe;QAAAS,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDlL,OAAA;QAAKmK,SAAS,EAAC,cAAc;QAAAS,QAAA,EAC1B7J,YAAY,CAAC6H,GAAG,CAAErB,IAAI,iBACrBvH,OAAA;UAEEmK,SAAS,EAAC,cAAc;UACxBgB,OAAO,EAAEA,CAAA,KAAM7D,UAAU,CAACC,IAAI,CAAE;UAAAqD,QAAA,gBAEhC5K,OAAA;YAAKmK,SAAS,EAAC,qBAAqB;YAAAS,QAAA,gBAClC5K,OAAA;cAAMmK,SAAS,EAAC,WAAW;cAAAS,QAAA,EAAErD,IAAI,CAACpG;YAAS;cAAA4J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDlL,OAAA;cACEmK,SAAS,EAAC,eAAe;cACzBgB,OAAO,EAAGnG,CAAC,IAAK;gBACdA,CAAC,CAACyG,eAAe,CAAC,CAAC;gBACnBjE,iBAAiB,CAACD,IAAI,CAACtG,EAAE,CAAC;cAC5B,CAAE;cAAA2J,QAAA,EACH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNlL,OAAA;YAAGmK,SAAS,EAAC,cAAc;YAAAS,QAAA,EAAErD,IAAI,CAACrG;UAAO;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GAhBzC3D,IAAI,CAACtG,EAAE;UAAA8J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBT,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlL,OAAA;MAAQmK,SAAS,EAAC,sBAAsB;MAACgB,OAAO,EAAE/C,OAAQ;MAAAwC,QAAA,eACxD5K,OAAA;QAAK6K,GAAG,EAAC,WAAW;QAACC,GAAG,EAAC,MAAM;QAACX,SAAS,EAAC;MAAW;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,EAGR9J,YAAY,iBACXpB,OAAA;MAAKmK,SAAS,EAAC,eAAe;MAACgB,OAAO,EAAEA,CAAA,KAAM9J,eAAe,CAAC,KAAK,CAAE;MAAAuJ,QAAA,eACnE5K,OAAA;QAAKmK,SAAS,EAAC,kBAAkB;QAACgB,OAAO,EAAGnG,CAAC,IAAKA,CAAC,CAACyG,eAAe,CAAC,CAAE;QAAAb,QAAA,gBACpE5K,OAAA;UAAKmK,SAAS,EAAC,iBAAiB;UAAAS,QAAA,gBAC9B5K,OAAA;YAAImK,SAAS,EAAC,gBAAgB;YAAAS,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5ClL,OAAA;YAAQmK,SAAS,EAAC,cAAc;YAACgB,OAAO,EAAEA,CAAA,KAAM9J,eAAe,CAAC,KAAK,CAAE;YAAAuJ,QAAA,EAAC;UAExE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENlL,OAAA;UAAKmK,SAAS,EAAC,kBAAkB;UAAAS,QAAA,gBAE/B5K,OAAA;YAAKmK,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/B5K,OAAA;cAAImK,SAAS,EAAC,eAAe;cAAAS,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ClL,OAAA;cAAKmK,SAAS,EAAC,kBAAkB;cAAAS,QAAA,gBAC/B5K,OAAA;gBAAGmK,SAAS,EAAC,aAAa;gBAAAS,QAAA,EAAE1H,UAAU,CAACN;cAAI;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDlL,OAAA;gBAAGmK,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAE1H,UAAU,CAACL;cAAI;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDlL,OAAA;gBAAGmK,SAAS,EAAC,eAAe;gBAAAS,QAAA,GAAC,MAAI,EAAC1H,UAAU,CAACF,SAAS;cAAA;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DlL,OAAA;gBAAKmK,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,gBAChC5K,OAAA;kBAAKmK,SAAS,EAAE,2BAA2BjH,UAAU,CAACJ,MAAM;gBAAG;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtElL,OAAA;kBAAMmK,SAAS,EAAE,sBAAsBjH,UAAU,CAACJ,MAAM,EAAG;kBAAA8H,QAAA,GAAC,UAClD,EAAC1H,UAAU,CAACJ,MAAM,KAAK,QAAQ,GAAG,WAAW,GAAG,cAAc;gBAAA;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EACLhI,UAAU,CAACJ,MAAM,KAAK,cAAc,iBACnC9C,OAAA;gBAAGmK,SAAS,EAAC,mCAAmC;gBAAAS,QAAA,EAAC;cAEjD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ,eACDlL,OAAA;gBACEmK,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,MAAMO,eAAe,GAAG,MAAM5H,sBAAsB,CAAC,CAAC;kBACtDU,WAAW,CAAC,gCAAgCkH,eAAe,EAAE,CAAC;kBAC9D;kBACA,MAAM1H,mBAAmB,GAAGtB,aAAa,CAACuB,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACpB,MAAM,KAAK,WAAW,CAAC;kBACvF,MAAMqB,YAAY,GAAGH,mBAAmB,GAAG,QAAQ,GAAG,cAAc;kBACpE,IAAIX,SAAS,GAAG,SAAS;kBACzB,IAAIC,SAAS,CAACG,SAAS,EAAE;oBACvB,MAAMC,YAAY,GAAGJ,SAAS,CAACG,SAAS,CAACE,KAAK,CAAC,uBAAuB,CAAC;oBACvE,IAAID,YAAY,EAAE;sBAChB,MAAME,OAAO,GAAGF,YAAY,CAAC,CAAC,CAAC;sBAC/B,QAAQE,OAAO;wBACb,KAAK,MAAM;0BAAEP,SAAS,GAAG,eAAe;0BAAE;wBAC1C,KAAK,KAAK;0BAAEA,SAAS,GAAG,aAAa;0BAAE;wBACvC,KAAK,KAAK;0BAAEA,SAAS,GAAG,WAAW;0BAAE;wBACrC,KAAK,KAAK;0BAAEA,SAAS,GAAG,WAAW;0BAAE;wBACrC;0BAASA,SAAS,GAAG,cAAcO,OAAO,EAAE;sBAC9C;oBACF;kBACF;kBACAT,aAAa,CAACiB,IAAI,KAAK;oBACrB,GAAGA,IAAI;oBACPxB,IAAI,EAAE,GAAG8I,eAAe,MAAMrI,SAAS,EAAE;oBACzCR,IAAI,EAAEQ,SAAS;oBACfP,MAAM,EAAEqB,YAAY;oBACpBpB,QAAQ,EAAEoB,YAAY,KAAK,QAAQ,GAAG,KAAK,GAAG;kBAChD,CAAC,CAAC,CAAC;gBACL,CAAE;gBAAAyG,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlL,OAAA;YAAKmK,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/B5K,OAAA;cAAImK,SAAS,EAAC,eAAe;cAAAS,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChDxI,aAAa,CAACiE,MAAM,KAAK,CAAC,gBACzB3G,OAAA;cAAKmK,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChC5K,OAAA;gBAAGmK,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5DlL,OAAA;gBAAGmK,SAAS,EAAC,sBAAsB;gBAAAS,QAAA,EAAC;cAA+C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,GAENxI,aAAa,CAACkG,GAAG,CAAE1E,MAAM,iBACvBlE,OAAA;cAAqBmK,SAAS,EAAC,sBAAsB;cAAAS,QAAA,gBAEnD5K,OAAA;gBACEmK,SAAS,EAAC,yBAAyB;gBACnCgB,OAAO,EAAEA,CAAA,KAAM3C,YAAY,CAACtE,MAAM,CAACjD,EAAE,CAAE;gBAAA2J,QAAA,EACxC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETlL,OAAA;gBAAKmK,SAAS,EAAC,aAAa;gBAAAS,QAAA,gBAC1B5K,OAAA;kBAAGmK,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAE1G,MAAM,CAACtB;gBAAI;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDlL,OAAA;kBAAGmK,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAE1G,MAAM,CAACrB;gBAAI;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDlL,OAAA;kBAAKmK,SAAS,EAAC,mBAAmB;kBAAAS,QAAA,gBAChC5K,OAAA;oBAAKmK,SAAS,EAAE,2BAA2BjG,MAAM,CAACpB,MAAM;kBAAG;oBAAAiI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClElL,OAAA;oBAAMmK,SAAS,EAAE,sBAAsBjG,MAAM,CAACpB,MAAM,EAAG;oBAAA8H,QAAA,GACpD1G,MAAM,CAACpB,MAAM,KAAK,WAAW,GAAG,WAAW,GAAG,cAAc,EAAC,UAAG,EAACoB,MAAM,CAACnB,QAAQ;kBAAA;oBAAAgI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlL,OAAA;gBACEmK,SAAS,EAAE,qCAAqCjG,MAAM,CAACpB,MAAM,KAAK,WAAW,GAAG,mBAAmB,GAAG,gBAAgB,EAAG;gBACzHqI,OAAO,EAAEA,CAAA,KAAMjH,MAAM,CAACpB,MAAM,KAAK,WAAW,GAAG+F,gBAAgB,CAAC3E,MAAM,CAACjD,EAAE,CAAC,GAAG0H,aAAa,CAACzE,MAAM,CAACjD,EAAE,CAAE;gBAAA2J,QAAA,EAErG1G,MAAM,CAACpB,MAAM,KAAK,WAAW,GAAG,YAAY,GAAG;cAAS;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA,GA1BDhH,MAAM,CAACjD,EAAE;cAAA8J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2Bd,CACN,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlL,OAAA;YAAKmK,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/B5K,OAAA;cAAKmK,SAAS,EAAC,gBAAgB;cAAAS,QAAA,gBAC7B5K,OAAA;gBAAImK,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDlL,OAAA;gBACEmK,SAAS,EAAE,mBAAmBjI,aAAa,GAAG,0BAA0B,GAAG,EAAE,EAAG;gBAChFiJ,OAAO,EAAEjC,gBAAiB;gBAC1ByC,QAAQ,EAAEzJ,aAAc;gBAAA0I,QAAA,EAEvB1I,aAAa,GAAG,gBAAgB,GAAG;cAAU;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLlJ,cAAc,CAAC2E,MAAM,KAAK,CAAC,gBAC1B3G,OAAA;cAAKmK,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChC5K,OAAA;gBAAGmK,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrDlL,OAAA;gBAAGmK,SAAS,EAAC,sBAAsB;gBAAAS,QAAA,EAChC1I,aAAa,GAAG,yBAAyB,GAAG;cAAoC;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,GAENlJ,cAAc,CAAC4G,GAAG,CAAE1E,MAAM,iBACxBlE,OAAA;cAAqBmK,SAAS,EAAC,wBAAwB;cAAAS,QAAA,gBACrD5K,OAAA;gBAAKmK,SAAS,EAAC,aAAa;gBAAAS,QAAA,gBAC1B5K,OAAA;kBAAGmK,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAE1G,MAAM,CAACtB;gBAAI;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDlL,OAAA;kBAAGmK,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,EAAE1G,MAAM,CAACrB;gBAAI;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDlL,OAAA;kBAAGmK,SAAS,EAAC,uBAAuB;kBAAAS,QAAA,EAAE1G,MAAM,CAACnB;gBAAQ;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNlL,OAAA;gBACEmK,SAAS,EAAC,aAAa;gBACvBgB,OAAO,EAAEA,CAAA,KAAMrC,UAAU,CAAC5E,MAAM,CAACjD,EAAE,CAAE;gBAAA2J,QAAA,EACtC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAXDhH,MAAM,CAACjD,EAAE;cAAA8J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYd,CACN,CACF,eAEDlL,OAAA;cAAKmK,SAAS,EAAC,sBAAsB;cAAAS,QAAA,eACnC5K,OAAA;gBAAQmK,SAAS,EAAC,oBAAoB;gBAACgB,OAAO,EAAE/B,cAAe;gBAAAwB,QAAA,EAAC;cAEhE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlL,OAAA;YAAKmK,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/B5K,OAAA;cAAImK,SAAS,EAAC,eAAe;cAAAS,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtDlL,OAAA;cACEmK,SAAS,EAAC,cAAc;cACxBgB,OAAO,EAAEA,CAAA,KAAM5J,mBAAmB,CAAC,IAAI,CAAE;cAAAqJ,QAAA,gBAEzC5K,OAAA;gBAAMmK,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDlL,OAAA;gBAAMmK,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eAGTlL,OAAA;cACEmK,SAAS,EAAC,cAAc;cACxBgB,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMS,QAAQ,GAAG,CAAC9J,qBAAqB;gBACvCC,wBAAwB,CAAC6J,QAAQ,CAAC;gBAClCpH,WAAW,CAACoH,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B,CAAC;cAChF,CAAE;cAAAhB,QAAA,gBAEF5K,OAAA;gBAAMmK,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DlL,OAAA;gBAAMmK,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EACjC9I,qBAAqB,GAAG,IAAI,GAAG;cAAK;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGTlL,OAAA;cACEmK,SAAS,EAAC,cAAc;cACxBgB,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIjJ,aAAa,EAAE;kBACjBC,gBAAgB,CAAC,KAAK,CAAC;kBACvBqC,WAAW,CAAC,2BAA2B,CAAC;gBAC1C,CAAC,MAAM;kBACLrC,gBAAgB,CAAC,IAAI,CAAC;kBACtBqC,WAAW,CAAC,2BAA2B,CAAC;gBAC1C;cACF,CAAE;cAAAoG,QAAA,gBAEF5K,OAAA;gBAAMmK,SAAS,EAAC,mBAAmB;gBAAAS,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5DlL,OAAA;gBAAMmK,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EACjC1I,aAAa,GAAG,IAAI,GAAG;cAAK;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA5J,gBAAgB,iBACftB,OAAA;MAAKmK,SAAS,EAAC,eAAe;MAACgB,OAAO,EAAEA,CAAA,KAAM5J,mBAAmB,CAAC,KAAK,CAAE;MAAAqJ,QAAA,eACvE5K,OAAA;QAAKmK,SAAS,EAAC,qBAAqB;QAACgB,OAAO,EAAGnG,CAAC,IAAKA,CAAC,CAACyG,eAAe,CAAC,CAAE;QAAAb,QAAA,gBACvE5K,OAAA;UAAKmK,SAAS,EAAC,iBAAiB;UAAAS,QAAA,gBAC9B5K,OAAA;YAAImK,SAAS,EAAC,gBAAgB;YAAAS,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDlL,OAAA;YAAQmK,SAAS,EAAC,cAAc;YAACgB,OAAO,EAAEA,CAAA,KAAM5J,mBAAmB,CAAC,KAAK,CAAE;YAAAqJ,QAAA,EAAC;UAE5E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENlL,OAAA;UAAKmK,SAAS,EAAC,kBAAkB;UAAAS,QAAA,eAC/B5K,OAAA;YAAKmK,SAAS,EAAC,kBAAkB;YAAAS,QAAA,gBAC/B5K,OAAA;cAAKmK,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChC5K,OAAA;gBAAOmK,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvDlL,OAAA;gBACE6C,IAAI,EAAC,UAAU;gBACfsH,SAAS,EAAC,uBAAuB;gBACjC0B,OAAO,EAAEzJ,YAAY,CAACE,QAAS;gBAC/BgJ,QAAQ,EAAGtG,CAAC,IAAK3C,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEE,QAAQ,EAAE0C,CAAC,CAAC2F,MAAM,CAACkB;gBAAO,CAAC;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlL,OAAA;cAAKmK,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChC5K,OAAA;gBAAOmK,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,GAAC,cAAY,EAACxI,YAAY,CAACG,SAAS,EAAC,UAAQ;cAAA;gBAAAwI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1FlL,OAAA;gBAAGmK,SAAS,EAAC,0BAA0B;gBAAAS,QAAA,EACpCxI,YAAY,CAACG,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG,GAAGH,YAAY,CAACG,SAAS;cAAe;gBAAAwI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC,eACJlL,OAAA;gBAAKmK,SAAS,EAAC,qBAAqB;gBAAAS,QAAA,gBAClC5K,OAAA;kBACEmK,SAAS,EAAC,mBAAmB;kBAC7BgB,OAAO,EAAEA,CAAA,KAAM9I,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEG,SAAS,EAAEuE,IAAI,CAACgF,GAAG,CAAC,CAAC,EAAE1J,YAAY,CAACG,SAAS,GAAG,CAAC;kBAAC,CAAC,CAAE;kBAAAqI,QAAA,EACvG;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlL,OAAA;kBAAMmK,SAAS,EAAC,kBAAkB;kBAAAS,QAAA,GAAExI,YAAY,CAACG,SAAS,EAAC,GAAC;gBAAA;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnElL,OAAA;kBACEmK,SAAS,EAAC,mBAAmB;kBAC7BgB,OAAO,EAAEA,CAAA,KAAM9I,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEG,SAAS,EAAEuE,IAAI,CAACiF,GAAG,CAAC,EAAE,EAAE3J,YAAY,CAACG,SAAS,GAAG,CAAC;kBAAC,CAAC,CAAE;kBAAAqI,QAAA,EACxG;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlL,OAAA;cAAKmK,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChC5K,OAAA;gBAAOmK,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7DlL,OAAA;gBACE6C,IAAI,EAAC,UAAU;gBACfsH,SAAS,EAAC,uBAAuB;gBACjC0B,OAAO,EAAEzJ,YAAY,CAACI,aAAc;gBACpC8I,QAAQ,EAAGtG,CAAC,IAAK3C,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEI,aAAa,EAAEwC,CAAC,CAAC2F,MAAM,CAACkB;gBAAO,CAAC;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlL,OAAA;cAAKmK,SAAS,EAAC,mBAAmB;cAAAS,QAAA,gBAChC5K,OAAA;gBAAOmK,SAAS,EAAC,oBAAoB;gBAAAS,QAAA,EAAC;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChElL,OAAA;gBACE6C,IAAI,EAAC,UAAU;gBACfsH,SAAS,EAAC,uBAAuB;gBACjC0B,OAAO,EAAEzJ,YAAY,CAACK,aAAc;gBACpC6I,QAAQ,EAAGtG,CAAC,IAAK3C,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEK,aAAa,EAAEuC,CAAC,CAAC2F,MAAM,CAACkB;gBAAO,CAAC;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNlL,OAAA;cAAKmK,SAAS,EAAC,mBAAmB;cAAAS,QAAA,eAChC5K,OAAA;gBACEmK,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAEA,CAAA,KAAM3G,WAAW,CAAC,6FAA6F,CAAE;gBAAAoG,QAAA,EAC3H;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENlL,OAAA;cAAKmK,SAAS,EAAC,mBAAmB;cAAAS,QAAA,eAChC5K,OAAA;gBACEmK,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAEA,CAAA,KAAM3G,WAAW,CAAC,8EAA8E,CAAE;gBAAAoG,QAAA,EAC5G;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENlL,OAAA;cAAKmK,SAAS,EAAC,mBAAmB;cAAAS,QAAA,eAChC5K,OAAA;gBACEmK,SAAS,EAAC,4BAA4B;gBACtCgB,OAAO,EAAEA,CAAA,KAAM3G,WAAW,CAAC,8EAA8E,CAAE;gBAAAoG,QAAA,EAC5G;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNlL,OAAA;cAAKmK,SAAS,EAAC,kBAAkB;cAAAS,QAAA,gBAC/B5K,OAAA;gBAAImK,SAAS,EAAC,wBAAwB;gBAAAS,QAAA,EAAC;cAAmC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/ElL,OAAA;gBAAGmK,SAAS,EAAC,sBAAsB;gBAAAS,QAAA,EAAC;cAEpC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAEJlL,OAAA;gBAAKmK,SAAS,EAAC,cAAc;gBAAAS,QAAA,gBAC3B5K,OAAA;kBAAMmK,SAAS,EAAC,gBAAgB;kBAAAS,QAAA,EAAC;gBAA8B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtElL,OAAA;kBACEmK,SAAS,EAAC,wBAAwB;kBAClCgB,OAAO,EAAEA,CAAA,KAAM3G,WAAW,CAAC,gEAAgE,CAAE;kBAAAoG,QAAA,eAE7F5K,OAAA;oBAAKmK,SAAS,EAAC;kBAA8B;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENlL,OAAA;gBAAKmK,SAAS,EAAC,cAAc;gBAAAS,QAAA,gBAC3B5K,OAAA;kBAAMmK,SAAS,EAAC,gBAAgB;kBAAAS,QAAA,EAAC;gBAAuB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DlL,OAAA;kBACEmK,SAAS,EAAC,wBAAwB;kBAClCgB,OAAO,EAAEA,CAAA,KAAM3G,WAAW,CAAC,+BAA+B,CAAE;kBAAAoG,QAAA,eAE5D5K,OAAA;oBAAKmK,SAAS,EAAC;kBAA8B;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENlL,OAAA;gBAAKmK,SAAS,EAAC,cAAc;gBAAAS,QAAA,gBAC3B5K,OAAA;kBAAMmK,SAAS,EAAC,gBAAgB;kBAAAS,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5DlL,OAAA;kBACEmK,SAAS,EAAC,wBAAwB;kBAClCgB,OAAO,EAAEA,CAAA,KAAM3G,WAAW,CAAC,iCAAiC,CAAE;kBAAAoG,QAAA,eAE9D5K,OAAA;oBAAKmK,SAAS,EAAC;kBAA8B;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENlL,OAAA;gBAAGmK,SAAS,EAAC,eAAe;gBAAAS,QAAA,EAAC;cAE7B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAChL,EAAA,CAj6BQD,GAAG;AAAA+L,EAAA,GAAH/L,GAAG;AAm6BZ,eAAeA,GAAG;AAAC,IAAA+L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}