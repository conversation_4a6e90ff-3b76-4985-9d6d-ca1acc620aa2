{"ast": null, "code": "var _jsxFileName = \"D:\\\\new git\\\\Clipsy-Windows\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  // State management matching Android app\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to Clipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\n  const [historyItems, setHistoryItems] = useState([{\n    id: '1',\n    content: 'This is an older clipboard item. It\\'s shorter.',\n    timestamp: '2 minutes ago'\n  }, {\n    id: '2',\n    content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.',\n    timestamp: '10 minutes ago'\n  }, {\n    id: '3',\n    content: 'Yet another historical entry.',\n    timestamp: '1 hour ago'\n  }, {\n    id: '4',\n    content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }',\n    timestamp: '5 hours ago'\n  }]);\n\n  // UI State\n  const [showSettings, setShowSettings] = useState(false);\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [connectionStatus, setConnectionStatus] = useState('connected');\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\n\n  // Background Sync State\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\n  const [networkDevices, setNetworkDevices] = useState([]);\n  const [isDiscovering, setIsDiscovering] = useState(false);\n\n  // Sync Settings - matching Android app\n  const [syncSettings, setSyncSettings] = useState({\n    autoSync: true,\n    syncDelay: 2,\n    syncOnConnect: true,\n    bidirectional: true\n  });\n\n  // Device Management - matching Android app\n  const [pairedDevices, setPairedDevices] = useState([{\n    id: 'android-1',\n    name: 'Android Phone - Personal',\n    type: 'Android 14',\n    status: 'connected',\n    lastSeen: '2 min ago',\n    ipAddress: '*************'\n  }, {\n    id: 'linux-1',\n    name: 'Ubuntu Server - Home',\n    type: 'Ubuntu 22.04',\n    status: 'disconnected',\n    lastSeen: '1 hour ago',\n    ipAddress: '*************'\n  }]);\n  const [discoveredDevices] = useState([{\n    id: 'johns-laptop',\n    name: 'John\\'s Laptop',\n    type: 'Windows 10',\n    status: 'discovering',\n    lastSeen: 'Available for pairing',\n    ipAddress: '*************'\n  }, {\n    id: 'sarahs-desktop',\n    name: 'Sarah\\'s Desktop',\n    type: 'Ubuntu 22.04',\n    status: 'discovering',\n    lastSeen: 'Available for pairing',\n    ipAddress: '*************'\n  }]);\n\n  // Device info state - get actual Windows device details\n  const [deviceInfo, setDeviceInfo] = useState({\n    name: 'Windows PC - Loading...',\n    type: 'Windows',\n    status: 'active',\n    lastSeen: 'now',\n    ipAddress: '*************'\n  });\n\n  // Get actual device name and OS details\n  React.useEffect(() => {\n    const getDeviceInfo = async () => {\n      try {\n        // Get device name from various sources\n        let deviceName = 'Windows PC';\n        let osVersion = 'Windows';\n\n        // Try to get computer name from environment or navigator\n        if (navigator.userAgentData) {\n          const platform = navigator.userAgentData.platform;\n          osVersion = platform || 'Windows';\n        } else if (navigator.userAgent) {\n          // Parse user agent for Windows version\n          const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\n          if (windowsMatch) {\n            const version = windowsMatch[1];\n            switch (version) {\n              case '10.0':\n                osVersion = 'Windows 10/11';\n                break;\n              case '6.3':\n                osVersion = 'Windows 8.1';\n                break;\n              case '6.2':\n                osVersion = 'Windows 8';\n                break;\n              case '6.1':\n                osVersion = 'Windows 7';\n                break;\n              default:\n                osVersion = `Windows NT ${version}`;\n            }\n          }\n        }\n\n        // Try to get hostname if available\n        try {\n          if (window.location.hostname && window.location.hostname !== 'localhost') {\n            deviceName = `${window.location.hostname} - Windows PC`;\n          } else {\n            // Use a more descriptive name based on OS\n            deviceName = `${osVersion} PC - Main`;\n          }\n        } catch (e) {\n          deviceName = `${osVersion} PC - Main`;\n        }\n        setDeviceInfo(prev => ({\n          ...prev,\n          name: deviceName,\n          type: osVersion,\n          status: connectionStatus === 'connected' ? 'active' : 'disconnected',\n          lastSeen: connectionStatus === 'connected' ? 'now' : 'disconnected'\n        }));\n      } catch (error) {\n        console.log('Could not get device info:', error);\n        setDeviceInfo(prev => ({\n          ...prev,\n          name: 'Windows PC - Main',\n          type: 'Windows',\n          status: connectionStatus === 'connected' ? 'active' : 'disconnected',\n          lastSeen: connectionStatus === 'connected' ? 'now' : 'disconnected'\n        }));\n      }\n    };\n    getDeviceInfo();\n  }, [connectionStatus]);\n\n  // Functions\n  const showMessage = text => {\n    setSuccessMessage(text);\n    setTimeout(() => setSuccessMessage(''), 3000);\n  };\n  const copyToClipboard = async content => {\n    try {\n      await navigator.clipboard.writeText(content);\n      setThisDeviceClipboard(content);\n      showMessage('✅ Text copied to clipboard!');\n    } catch (error) {\n      console.error('Failed to copy to clipboard:', error);\n      showMessage('❌ Failed to copy to clipboard');\n    }\n  };\n  const selectItem = item => {\n    copyToClipboard(item.content);\n    setConnectedDeviceClipboard(item.content);\n  };\n  const deleteHistoryItem = itemId => {\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\n    setHistoryItems(updatedHistory);\n    showMessage('🗑️ History item deleted!');\n  };\n\n  // Device edit functions\n  const startEditingThisDevice = () => {\n    setEditingThisDeviceText(thisDeviceClipboard);\n    setIsEditingThisDevice(true);\n  };\n  const saveThisDeviceEdit = async () => {\n    const newContent = editingThisDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setThisDeviceClipboard(newContent);\n    try {\n      await navigator.clipboard.writeText(newContent);\n    } catch (error) {\n      console.warn('Failed to update clipboard:', error);\n    }\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n    showMessage('✅ This Device clipboard updated!');\n  };\n  const cancelThisDeviceEdit = () => {\n    setIsEditingThisDevice(false);\n    setEditingThisDeviceText('');\n  };\n  const startEditingConnectedDevice = () => {\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\n    setIsEditingConnectedDevice(true);\n  };\n  const saveConnectedDeviceEdit = () => {\n    const newContent = editingConnectedDeviceText.trim();\n    if (!newContent) {\n      showMessage('Content cannot be empty');\n      return;\n    }\n    setConnectedDeviceClipboard(newContent);\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n    showMessage('✅ Connected Device clipboard updated!');\n  };\n  const cancelConnectedDeviceEdit = () => {\n    setIsEditingConnectedDevice(false);\n    setEditingConnectedDeviceText('');\n  };\n  const syncNow = () => {\n    showMessage('🔄 Syncing with paired devices...');\n    setTimeout(() => {\n      showMessage('✅ Sync completed!');\n    }, 1000);\n  };\n  const toggleAlwaysOnTop = () => {\n    setIsAlwaysOnTop(!isAlwaysOnTop);\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\n  };\n  const minimizeToTray = () => {\n    showMessage('➖ Minimizing to background...');\n  };\n  const toggleFloatingOverlay = () => {\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\n  };\n\n  // Device Management Functions\n  const removeDevice = deviceId => {\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\n    setPairedDevices(updatedDevices);\n    showMessage('🗑️ Device removed from paired devices');\n  };\n  const connectDevice = deviceId => {\n    const updatedDevices = pairedDevices.map(device => device.id === deviceId ? {\n      ...device,\n      status: 'connected',\n      lastSeen: 'Just now'\n    } : device);\n    setPairedDevices(updatedDevices);\n    showMessage('✅ Device connected successfully');\n  };\n  const disconnectDevice = deviceId => {\n    const updatedDevices = pairedDevices.map(device => device.id === deviceId ? {\n      ...device,\n      status: 'disconnected',\n      lastSeen: 'Just now'\n    } : device);\n    setPairedDevices(updatedDevices);\n    showMessage('🔌 Device disconnected');\n  };\n  const pairDevice = deviceId => {\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\n    if (deviceToPair) {\n      const newPairedDevice = {\n        ...deviceToPair,\n        status: 'connected',\n        lastSeen: 'Just now'\n      };\n      setPairedDevices([...pairedDevices, newPairedDevice]);\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\n    }\n  };\n  const refreshDiscovery = () => {\n    if (isDiscovering) return;\n    setIsDiscovering(true);\n    showMessage('🔍 Scanning for devices...');\n\n    // Simulate discovery process\n    setTimeout(() => {\n      setNetworkDevices(discoveredDevices);\n      setIsDiscovering(false);\n      showMessage(`📱 Found ${discoveredDevices.length} available devices`);\n    }, 2000);\n  };\n  const scanQRCode = () => {\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\n  };\n  const generateQRCode = () => {\n    showMessage('📋 QR Code generation not available in web version. Use device discovery instead.');\n  };\n\n  // Simulate connection status changes for testing\n  const toggleConnectionStatus = () => {\n    const newStatus = connectionStatus === 'connected' ? 'disconnected' : 'connected';\n    setConnectionStatus(newStatus);\n    showMessage(`🔌 Connection status changed to: ${newStatus}`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"title-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/clipsy-logo-no-bg.png\",\n            alt: \"Clipsy Logo\",\n            className: \"app-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: \"Clipsy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isFloatingOverlayVisible ? 'active' : ''}`,\n          onClick: toggleFloatingOverlay,\n          title: \"Toggle floating clipboard widget\",\n          children: \"\\uD83D\\uDCCB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `icon-button ${isAlwaysOnTop ? 'active' : ''}`,\n          onClick: toggleAlwaysOnTop,\n          title: \"Pin to top\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pin-icon\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-head\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pin-body\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: minimizeToTray,\n          title: \"Minimize\",\n          children: \"\\u2796\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"icon-button\",\n          onClick: () => setShowSettings(true),\n          title: \"Settings\",\n          children: \"\\u2699\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"device-section-title-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"device-section-title\",\n              children: \"\\uD83D\\uDCBB This Device\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"device-section-subtitle\",\n              children: deviceInfo.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), isEditingThisDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingThisDeviceText,\n            onChange: e => setEditingThisDeviceText(e.target.value),\n            placeholder: \"Edit this device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button this-device\",\n              onClick: saveThisDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button this-device\",\n              onClick: cancelThisDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content this-device-content\",\n            onClick: () => copyToClipboard(thisDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: thisDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Real-time sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingThisDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"device-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-section-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"device-section-title\",\n            children: \"\\uD83D\\uDD17 Connected Device\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"device-section-subtitle\",\n            children: \"Android Phone - Personal \\uD83D\\uDFE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), isEditingConnectedDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"edit-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            className: \"edit-text-input\",\n            value: editingConnectedDeviceText,\n            onChange: e => setEditingConnectedDeviceText(e.target.value),\n            placeholder: \"Edit connected device clipboard content...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"edit-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"save-button connected-device\",\n              onClick: saveConnectedDeviceEdit,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"cancel-button connected-device\",\n              onClick: cancelConnectedDeviceEdit,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"device-clipboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"clipboard-content connected-device-content\",\n            onClick: () => copyToClipboard(connectedDeviceClipboard),\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-text\",\n              children: connectedDeviceClipboard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"clipboard-meta\",\n              children: \"Click to copy \\u2022 Bidirectional sync\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"edit-button-inside\",\n              onClick: startEditingConnectedDevice,\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"history-title\",\n        children: \"Clipboard History\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"history-list\",\n        children: historyItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"history-item\",\n          onClick: () => selectItem(item),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"history-item-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"timestamp\",\n              children: item.timestamp\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"delete-button\",\n              onClick: e => {\n                e.stopPropagation();\n                deleteHistoryItem(item.id);\n              },\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"item-content\",\n            children: item.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"floating-sync-button\",\n      onClick: syncNow,\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/sync.png\",\n        alt: \"Sync\",\n        className: \"sync-icon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 7\n    }, this), showSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowSettings(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-sidebar\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-title\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: () => setShowSettings(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Device Info\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"device-info-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-name\",\n                children: deviceInfo.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail\",\n                children: deviceInfo.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail\",\n                children: [\"IP: \", deviceInfo.ipAddress]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-status-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `device-status-indicator ${deviceInfo.status}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `device-status-text ${deviceInfo.status}`,\n                  children: [\"Status: \", deviceInfo.status === 'active' ? 'Connected' : 'Disconnected']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this), deviceInfo.status === 'disconnected' && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"device-detail disconnected-notice\",\n                children: \"\\u26A0\\uFE0F Device is currently disconnected from the network\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Paired Devices\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this), pairedDevices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-device-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-text\",\n                children: \"No paired devices found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-subtext\",\n                children: \"Use QR code or device discovery to pair devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 19\n            }, this) : pairedDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"enhanced-device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"remove-button-top-right\",\n                onClick: () => removeDevice(device.id),\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"device-status-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `device-status-indicator ${device.status}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `device-status-text ${device.status}`,\n                    children: [device.status === 'connected' ? 'Connected' : 'Disconnected', \" \\u2022 \", device.lastSeen]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`,\n                onClick: () => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id),\n                children: device.status === 'connected' ? 'Disconnect' : 'Connect'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 23\n              }, this)]\n            }, device.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 21\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"section-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"section-title\",\n                children: \"Device Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`,\n                onClick: refreshDiscovery,\n                disabled: isDiscovering,\n                children: isDiscovering ? '🔍 Scanning...' : 'Discover'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 17\n            }, this), networkDevices.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-device-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-text\",\n                children: \"No devices found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-device-subtext\",\n                children: isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 19\n            }, this) : networkDevices.map(device => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"discovered-device-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"device-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-name\",\n                  children: device.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-type\",\n                  children: device.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"device-card-last-seen\",\n                  children: device.lastSeen\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"pair-button\",\n                onClick: () => pairDevice(device.id),\n                children: \"Pair\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 23\n              }, this)]\n            }, device.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 21\n            }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qr-buttons-container\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"qr-generate-button\",\n                onClick: generateQRCode,\n                children: \"Generate QR\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"section-title\",\n              children: \"Additional Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => setShowSyncSettings(true),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Sync Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-arrow\",\n                children: \"\\u203A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => {\n                const newValue = !backgroundSyncEnabled;\n                setBackgroundSyncEnabled(newValue);\n                showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Background Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-value\",\n                children: backgroundSyncEnabled ? 'on' : 'off'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"setting-item\",\n              onClick: () => {\n                if (isDiscovering) {\n                  setIsDiscovering(false);\n                  showMessage('Network discovery stopped');\n                } else {\n                  setIsDiscovering(true);\n                  showMessage('Network discovery started');\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-text\",\n                children: \"Network Discovery\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"setting-item-value\",\n                children: isDiscovering ? 'on' : 'off'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 9\n    }, this), showSyncSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowSyncSettings(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sync-settings-modal\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-title\",\n            children: \"Sync Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"close-button\",\n            onClick: () => setShowSyncSettings(false),\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Auto Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.autoSync,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  autoSync: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: [\"Sync Delay: \", syncSettings.syncDelay, \" seconds\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"sync-setting-description\",\n                children: syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sync-delay-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"sync-delay-button\",\n                  onClick: () => setSyncSettings({\n                    ...syncSettings,\n                    syncDelay: Math.max(0, syncSettings.syncDelay - 1)\n                  }),\n                  children: \"-\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sync-delay-value\",\n                  children: [syncSettings.syncDelay, \"s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"sync-delay-button\",\n                  onClick: () => setSyncSettings({\n                    ...syncSettings,\n                    syncDelay: Math.min(30, syncSettings.syncDelay + 1)\n                  }),\n                  children: \"+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Sync on Connect\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.syncOnConnect,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  syncOnConnect: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"sync-setting-label\",\n                children: \"Bidirectional Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"sync-setting-checkbox\",\n                checked: syncSettings.bidirectional,\n                onChange: e => setSyncSettings({\n                  ...syncSettings,\n                  bidirectional: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.'),\n                children: \"\\uD83D\\uDCF1 Windows \\u2194 Android Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.'),\n                children: \"\\uD83D\\uDDA5\\uFE0F Windows \\u2194 Windows Sync\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sync-setting-item\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cross-platform-sync-button\",\n                onClick: () => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.'),\n                children: \"\\uD83D\\uDE80 Sync All Devices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"settings-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"settings-section-title\",\n                children: \"\\uD83D\\uDCCB Floating Overlay Button Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"settings-description\",\n                children: \"Configure the floating overlay button for quick access to connected device clipboards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Enable Floating Overlay Button\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('📋 Floating overlay button is always enabled for accessibility'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 759,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Show Device Count Badge\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('🔢 Device count badge enabled'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"settings-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"settings-label\",\n                  children: \"Auto-hide After Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"settings-toggle active\",\n                  onClick: () => showMessage('⏱️ Auto-hide after copy enabled'),\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"settings-toggle-thumb active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 779,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"settings-note\",\n                children: \"\\uD83D\\uDCA1 The floating overlay button (\\uD83D\\uDCCB) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 655,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 318,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"IH/yVk3dhHsGJJ0cEkWv434fulQ=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "App", "_s", "thisDeviceClipboard", "setThisDeviceClipboard", "connectedDeviceClipboard", "setConnectedDeviceClipboard", "isEditingThisDevice", "setIsEditingThisDevice", "isEditingConnectedDevice", "setIsEditingConnectedDevice", "editingThisDeviceText", "setEditingThisDeviceText", "editingConnectedDeviceText", "setEditingConnectedDeviceText", "historyItems", "setHistoryItems", "id", "content", "timestamp", "showSettings", "setShowSettings", "showSyncSettings", "setShowSyncSettings", "isAlwaysOnTop", "setIsAlwaysOnTop", "successMessage", "setSuccessMessage", "connectionStatus", "setConnectionStatus", "isFloatingOverlayVisible", "setIsFloatingOverlayVisible", "backgroundSyncEnabled", "setBackgroundSyncEnabled", "networkDevices", "setNetworkDevices", "isDiscovering", "setIsDiscovering", "syncSettings", "setSyncSettings", "autoSync", "syncD<PERSON>y", "syncOnConnect", "bidirectional", "pairedDevices", "setPairedDevices", "name", "type", "status", "lastSeen", "ip<PERSON><PERSON><PERSON>", "discoveredDevices", "deviceInfo", "setDeviceInfo", "getDeviceInfo", "deviceName", "osVersion", "navigator", "userAgentData", "platform", "userAgent", "windowsMatch", "match", "version", "window", "location", "hostname", "e", "prev", "error", "console", "log", "showMessage", "text", "setTimeout", "copyToClipboard", "clipboard", "writeText", "selectItem", "item", "deleteHistoryItem", "itemId", "updatedHistory", "filter", "startEditingThisDevice", "saveThisDeviceEdit", "newContent", "trim", "warn", "cancelThisDeviceEdit", "startEditingConnectedDevice", "saveConnectedDeviceEdit", "cancelConnectedDeviceEdit", "syncNow", "toggleAlwaysOnTop", "minimizeToTray", "toggleFloatingOverlay", "removeDevice", "deviceId", "updatedDevices", "device", "connectDevice", "map", "disconnectDevice", "pairDevice", "deviceToPair", "find", "newPairedDevice", "refreshDiscovery", "length", "scanQRCode", "generateQRCode", "toggleConnectionStatus", "newStatus", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "some", "onClick", "title", "value", "onChange", "target", "placeholder", "autoFocus", "stopPropagation", "disabled", "newValue", "checked", "Math", "max", "min", "_c", "$RefreshReg$"], "sources": ["D:/new git/Clipsy-Windows/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  // State management matching Android app\r\n  const [thisDeviceClipboard, setThisDeviceClipboard] = useState('Welcome to <PERSON>lipsy! This is your Windows device clipboard content. You can edit this content and it will sync with connected devices.');\r\n  const [connectedDeviceClipboard, setConnectedDeviceClipboard] = useState('This is the clipboard content from your connected Android device. You can edit this content and it will be sent to the connected device.');\r\n  const [isEditingThisDevice, setIsEditingThisDevice] = useState(false);\r\n  const [isEditingConnectedDevice, setIsEditingConnectedDevice] = useState(false);\r\n  const [editingThisDeviceText, setEditingThisDeviceText] = useState('');\r\n  const [editingConnectedDeviceText, setEditingConnectedDeviceText] = useState('');\r\n\r\n  const [historyItems, setHistoryItems] = useState([\r\n    { id: '1', content: 'This is an older clipboard item. It\\'s shorter.', timestamp: '2 minutes ago' },\r\n    { id: '2', content: 'Another item from history. This one might be a bit longer and if it is, it will initially be truncated but can be expanded to see the full content when the user clicks on it.', timestamp: '10 minutes ago' },\r\n    { id: '3', content: 'Yet another historical entry.', timestamp: '1 hour ago' },\r\n    { id: '4', content: 'Some code snippet: function hello() { console.log(\"Hello World!\"); }', timestamp: '5 hours ago' }\r\n  ]);\r\n\r\n  // UI State\r\n  const [showSettings, setShowSettings] = useState(false);\r\n  const [showSyncSettings, setShowSyncSettings] = useState(false);\r\n  const [isAlwaysOnTop, setIsAlwaysOnTop] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [connectionStatus, setConnectionStatus] = useState('connected');\r\n  const [isFloatingOverlayVisible, setIsFloatingOverlayVisible] = useState(false);\r\n\r\n  // Background Sync State\r\n  const [backgroundSyncEnabled, setBackgroundSyncEnabled] = useState(true);\r\n  const [networkDevices, setNetworkDevices] = useState([]);\r\n  const [isDiscovering, setIsDiscovering] = useState(false);\r\n\r\n  // Sync Settings - matching Android app\r\n  const [syncSettings, setSyncSettings] = useState({\r\n    autoSync: true,\r\n    syncDelay: 2,\r\n    syncOnConnect: true,\r\n    bidirectional: true\r\n  });\r\n\r\n  // Device Management - matching Android app\r\n  const [pairedDevices, setPairedDevices] = useState([\r\n    {\r\n      id: 'android-1',\r\n      name: 'Android Phone - Personal',\r\n      type: 'Android 14',\r\n      status: 'connected',\r\n      lastSeen: '2 min ago',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'linux-1',\r\n      name: 'Ubuntu Server - Home',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'disconnected',\r\n      lastSeen: '1 hour ago',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  const [discoveredDevices] = useState([\r\n    {\r\n      id: 'johns-laptop',\r\n      name: 'John\\'s Laptop',\r\n      type: 'Windows 10',\r\n      status: 'discovering',\r\n      lastSeen: 'Available for pairing',\r\n      ipAddress: '*************'\r\n    },\r\n    {\r\n      id: 'sarahs-desktop',\r\n      name: 'Sarah\\'s Desktop',\r\n      type: 'Ubuntu 22.04',\r\n      status: 'discovering',\r\n      lastSeen: 'Available for pairing',\r\n      ipAddress: '*************'\r\n    }\r\n  ]);\r\n\r\n  // Device info state - get actual Windows device details\r\n  const [deviceInfo, setDeviceInfo] = useState({\r\n    name: 'Windows PC - Loading...',\r\n    type: 'Windows',\r\n    status: 'active',\r\n    lastSeen: 'now',\r\n    ipAddress: '*************'\r\n  });\r\n\r\n  // Get actual device name and OS details\r\n  React.useEffect(() => {\r\n    const getDeviceInfo = async () => {\r\n      try {\r\n        // Get device name from various sources\r\n        let deviceName = 'Windows PC';\r\n        let osVersion = 'Windows';\r\n\r\n        // Try to get computer name from environment or navigator\r\n        if (navigator.userAgentData) {\r\n          const platform = navigator.userAgentData.platform;\r\n          osVersion = platform || 'Windows';\r\n        } else if (navigator.userAgent) {\r\n          // Parse user agent for Windows version\r\n          const windowsMatch = navigator.userAgent.match(/Windows NT (\\d+\\.\\d+)/);\r\n          if (windowsMatch) {\r\n            const version = windowsMatch[1];\r\n            switch (version) {\r\n              case '10.0': osVersion = 'Windows 10/11'; break;\r\n              case '6.3': osVersion = 'Windows 8.1'; break;\r\n              case '6.2': osVersion = 'Windows 8'; break;\r\n              case '6.1': osVersion = 'Windows 7'; break;\r\n              default: osVersion = `Windows NT ${version}`;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Try to get hostname if available\r\n        try {\r\n          if (window.location.hostname && window.location.hostname !== 'localhost') {\r\n            deviceName = `${window.location.hostname} - Windows PC`;\r\n          } else {\r\n            // Use a more descriptive name based on OS\r\n            deviceName = `${osVersion} PC - Main`;\r\n          }\r\n        } catch (e) {\r\n          deviceName = `${osVersion} PC - Main`;\r\n        }\r\n\r\n        setDeviceInfo(prev => ({\r\n          ...prev,\r\n          name: deviceName,\r\n          type: osVersion,\r\n          status: connectionStatus === 'connected' ? 'active' : 'disconnected',\r\n          lastSeen: connectionStatus === 'connected' ? 'now' : 'disconnected'\r\n        }));\r\n      } catch (error) {\r\n        console.log('Could not get device info:', error);\r\n        setDeviceInfo(prev => ({\r\n          ...prev,\r\n          name: 'Windows PC - Main',\r\n          type: 'Windows',\r\n          status: connectionStatus === 'connected' ? 'active' : 'disconnected',\r\n          lastSeen: connectionStatus === 'connected' ? 'now' : 'disconnected'\r\n        }));\r\n      }\r\n    };\r\n\r\n    getDeviceInfo();\r\n  }, [connectionStatus]);\r\n\r\n  // Functions\r\n  const showMessage = (text) => {\r\n    setSuccessMessage(text);\r\n    setTimeout(() => setSuccessMessage(''), 3000);\r\n  };\r\n\r\n  const copyToClipboard = async (content) => {\r\n    try {\r\n      await navigator.clipboard.writeText(content);\r\n      setThisDeviceClipboard(content);\r\n      showMessage('✅ Text copied to clipboard!');\r\n    } catch (error) {\r\n      console.error('Failed to copy to clipboard:', error);\r\n      showMessage('❌ Failed to copy to clipboard');\r\n    }\r\n  };\r\n\r\n  const selectItem = (item) => {\r\n    copyToClipboard(item.content);\r\n    setConnectedDeviceClipboard(item.content);\r\n  };\r\n\r\n  const deleteHistoryItem = (itemId) => {\r\n    const updatedHistory = historyItems.filter(item => item.id !== itemId);\r\n    setHistoryItems(updatedHistory);\r\n    showMessage('🗑️ History item deleted!');\r\n  };\r\n\r\n  // Device edit functions\r\n  const startEditingThisDevice = () => {\r\n    setEditingThisDeviceText(thisDeviceClipboard);\r\n    setIsEditingThisDevice(true);\r\n  };\r\n\r\n  const saveThisDeviceEdit = async () => {\r\n    const newContent = editingThisDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setThisDeviceClipboard(newContent);\r\n    try {\r\n      await navigator.clipboard.writeText(newContent);\r\n    } catch (error) {\r\n      console.warn('Failed to update clipboard:', error);\r\n    }\r\n\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n    showMessage('✅ This Device clipboard updated!');\r\n  };\r\n\r\n  const cancelThisDeviceEdit = () => {\r\n    setIsEditingThisDevice(false);\r\n    setEditingThisDeviceText('');\r\n  };\r\n\r\n  const startEditingConnectedDevice = () => {\r\n    setEditingConnectedDeviceText(connectedDeviceClipboard);\r\n    setIsEditingConnectedDevice(true);\r\n  };\r\n\r\n  const saveConnectedDeviceEdit = () => {\r\n    const newContent = editingConnectedDeviceText.trim();\r\n    if (!newContent) {\r\n      showMessage('Content cannot be empty');\r\n      return;\r\n    }\r\n\r\n    setConnectedDeviceClipboard(newContent);\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n    showMessage('✅ Connected Device clipboard updated!');\r\n  };\r\n\r\n  const cancelConnectedDeviceEdit = () => {\r\n    setIsEditingConnectedDevice(false);\r\n    setEditingConnectedDeviceText('');\r\n  };\r\n\r\n  const syncNow = () => {\r\n    showMessage('🔄 Syncing with paired devices...');\r\n    setTimeout(() => {\r\n      showMessage('✅ Sync completed!');\r\n    }, 1000);\r\n  };\r\n\r\n  const toggleAlwaysOnTop = () => {\r\n    setIsAlwaysOnTop(!isAlwaysOnTop);\r\n    showMessage(isAlwaysOnTop ? '📌 App unpinned from top' : '📌 App pinned to top');\r\n  };\r\n\r\n  const minimizeToTray = () => {\r\n    showMessage('➖ Minimizing to background...');\r\n  };\r\n\r\n  const toggleFloatingOverlay = () => {\r\n    setIsFloatingOverlayVisible(!isFloatingOverlayVisible);\r\n    showMessage(isFloatingOverlayVisible ? '🔄 Floating widget hidden' : '🔄 Floating widget shown');\r\n  };\r\n\r\n  // Device Management Functions\r\n  const removeDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.filter(device => device.id !== deviceId);\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🗑️ Device removed from paired devices');\r\n  };\r\n\r\n  const connectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'connected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('✅ Device connected successfully');\r\n  };\r\n\r\n  const disconnectDevice = (deviceId) => {\r\n    const updatedDevices = pairedDevices.map(device =>\r\n      device.id === deviceId ? { ...device, status: 'disconnected', lastSeen: 'Just now' } : device\r\n    );\r\n    setPairedDevices(updatedDevices);\r\n    showMessage('🔌 Device disconnected');\r\n  };\r\n\r\n  const pairDevice = (deviceId) => {\r\n    const deviceToPair = discoveredDevices.find(device => device.id === deviceId);\r\n    if (deviceToPair) {\r\n      const newPairedDevice = {\r\n        ...deviceToPair,\r\n        status: 'connected',\r\n        lastSeen: 'Just now'\r\n      };\r\n      setPairedDevices([...pairedDevices, newPairedDevice]);\r\n      showMessage(`✅ Successfully paired with ${deviceToPair.name}`);\r\n    }\r\n  };\r\n\r\n  const refreshDiscovery = () => {\r\n    if (isDiscovering) return;\r\n\r\n    setIsDiscovering(true);\r\n    showMessage('🔍 Scanning for devices...');\r\n\r\n    // Simulate discovery process\r\n    setTimeout(() => {\r\n      setNetworkDevices(discoveredDevices);\r\n      setIsDiscovering(false);\r\n      showMessage(`📱 Found ${discoveredDevices.length} available devices`);\r\n    }, 2000);\r\n  };\r\n\r\n  const scanQRCode = () => {\r\n    showMessage('📷 QR Scanner not available in web version. Use device discovery instead.');\r\n  };\r\n\r\n  const generateQRCode = () => {\r\n    showMessage('📋 QR Code generation not available in web version. Use device discovery instead.');\r\n  };\r\n\r\n  // Simulate connection status changes for testing\r\n  const toggleConnectionStatus = () => {\r\n    const newStatus = connectionStatus === 'connected' ? 'disconnected' : 'connected';\r\n    setConnectionStatus(newStatus);\r\n    showMessage(`🔌 Connection status changed to: ${newStatus}`);\r\n  };\r\n\r\n  return (\r\n    <div className=\"app-container\">\r\n      {/* Header */}\r\n      <div className=\"header\">\r\n        <div className=\"title-container\">\r\n          <div className=\"logo-container\">\r\n            <img\r\n              src=\"/clipsy-logo-no-bg.png\"\r\n              alt=\"Clipsy Logo\"\r\n              className=\"app-logo\"\r\n            />\r\n            <div className={`connection-dot ${pairedDevices.some(device => device.status === 'connected') ? 'connected' : 'disconnected'}`}></div>\r\n          </div>\r\n          <h1 className=\"title\">Clipsy</h1>\r\n        </div>\r\n        <div className=\"header-actions\">\r\n          <button\r\n            className={`icon-button ${isFloatingOverlayVisible ? 'active' : ''}`}\r\n            onClick={toggleFloatingOverlay}\r\n            title=\"Toggle floating clipboard widget\"\r\n          >\r\n            📋\r\n          </button>\r\n          <button\r\n            className={`icon-button ${isAlwaysOnTop ? 'active' : ''}`}\r\n            onClick={toggleAlwaysOnTop}\r\n            title=\"Pin to top\"\r\n          >\r\n            <div className=\"pin-icon\">\r\n              <div className=\"pin-head\"></div>\r\n              <div className=\"pin-body\"></div>\r\n            </div>\r\n          </button>\r\n          <button className=\"icon-button\" onClick={minimizeToTray} title=\"Minimize\">\r\n            ➖\r\n          </button>\r\n          <button className=\"icon-button\" onClick={() => setShowSettings(true)} title=\"Settings\">\r\n            ⚙️\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Success Message */}\r\n      {successMessage && (\r\n        <div className=\"success-message\">\r\n          <span>{successMessage}</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      <div className=\"main-content\">\r\n        {/* This Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <div className=\"device-section-title-container\">\r\n              <h2 className=\"device-section-title\">💻 This Device</h2>\r\n              <p className=\"device-section-subtitle\">{deviceInfo.name}</p>\r\n            </div>\r\n          </div>\r\n\r\n          {isEditingThisDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingThisDeviceText}\r\n                onChange={(e) => setEditingThisDeviceText(e.target.value)}\r\n                placeholder=\"Edit this device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button this-device\" onClick={saveThisDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button this-device\" onClick={cancelThisDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content this-device-content\"\r\n                onClick={() => copyToClipboard(thisDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{thisDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Real-time sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingThisDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Connected Device Section */}\r\n        <div className=\"device-section\">\r\n          <div className=\"device-section-header\">\r\n            <h2 className=\"device-section-title\">🔗 Connected Device</h2>\r\n            <p className=\"device-section-subtitle\">Android Phone - Personal 🟢</p>\r\n          </div>\r\n\r\n          {isEditingConnectedDevice ? (\r\n            <div className=\"edit-container\">\r\n              <textarea\r\n                className=\"edit-text-input\"\r\n                value={editingConnectedDeviceText}\r\n                onChange={(e) => setEditingConnectedDeviceText(e.target.value)}\r\n                placeholder=\"Edit connected device clipboard content...\"\r\n                autoFocus\r\n              />\r\n              <div className=\"edit-actions\">\r\n                <button className=\"save-button connected-device\" onClick={saveConnectedDeviceEdit}>\r\n                  Save\r\n                </button>\r\n                <button className=\"cancel-button connected-device\" onClick={cancelConnectedDeviceEdit}>\r\n                  Cancel\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"device-clipboard\">\r\n              <div\r\n                className=\"clipboard-content connected-device-content\"\r\n                onClick={() => copyToClipboard(connectedDeviceClipboard)}\r\n              >\r\n                <p className=\"clipboard-text\">{connectedDeviceClipboard}</p>\r\n                <p className=\"clipboard-meta\">Click to copy • Bidirectional sync</p>\r\n                <button className=\"edit-button-inside\" onClick={startEditingConnectedDevice}>\r\n                  ✎\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Clipboard History */}\r\n        <h2 className=\"history-title\">Clipboard History</h2>\r\n        <div className=\"history-list\">\r\n          {historyItems.map((item) => (\r\n            <div\r\n              key={item.id}\r\n              className=\"history-item\"\r\n              onClick={() => selectItem(item)}\r\n            >\r\n              <div className=\"history-item-header\">\r\n                <span className=\"timestamp\">{item.timestamp}</span>\r\n                <button\r\n                  className=\"delete-button\"\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    deleteHistoryItem(item.id);\r\n                  }}\r\n                >\r\n                  ✕\r\n                </button>\r\n              </div>\r\n              <p className=\"item-content\">{item.content}</p>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Floating Sync Button */}\r\n      <button className=\"floating-sync-button\" onClick={syncNow}>\r\n        <img src=\"/sync.png\" alt=\"Sync\" className=\"sync-icon\" />\r\n      </button>\r\n\r\n      {/* Settings Modal */}\r\n      {showSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSettings(false)}>\r\n          <div className=\"settings-sidebar\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              {/* Device Info */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Device Info</h3>\r\n                <div className=\"device-info-card\">\r\n                  <p className=\"device-name\">{deviceInfo.name}</p>\r\n                  <p className=\"device-detail\">{deviceInfo.type}</p>\r\n                  <p className=\"device-detail\">IP: {deviceInfo.ipAddress}</p>\r\n                  <div className=\"device-status-row\">\r\n                    <div className={`device-status-indicator ${deviceInfo.status}`}></div>\r\n                    <span className={`device-status-text ${deviceInfo.status}`}>\r\n                      Status: {deviceInfo.status === 'active' ? 'Connected' : 'Disconnected'}\r\n                    </span>\r\n                  </div>\r\n                  {deviceInfo.status === 'disconnected' && (\r\n                    <p className=\"device-detail disconnected-notice\">\r\n                      ⚠️ Device is currently disconnected from the network\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Paired Devices */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Paired Devices</h3>\r\n                {pairedDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No paired devices found</p>\r\n                    <p className=\"empty-device-subtext\">Use QR code or device discovery to pair devices</p>\r\n                  </div>\r\n                ) : (\r\n                  pairedDevices.map((device) => (\r\n                    <div key={device.id} className=\"enhanced-device-card\">\r\n                      {/* Remove Button - White X at top right corner */}\r\n                      <button\r\n                        className=\"remove-button-top-right\"\r\n                        onClick={() => removeDevice(device.id)}\r\n                      >\r\n                        ×\r\n                      </button>\r\n\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <div className=\"device-status-row\">\r\n                          <div className={`device-status-indicator ${device.status}`}></div>\r\n                          <span className={`device-status-text ${device.status}`}>\r\n                            {device.status === 'connected' ? 'Connected' : 'Disconnected'} • {device.lastSeen}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Connect/Disconnect Button - Bottom Right Corner */}\r\n                      <button\r\n                        className={`device-action-button-bottom-right ${device.status === 'connected' ? 'disconnect-button' : 'connect-button'}`}\r\n                        onClick={() => device.status === 'connected' ? disconnectDevice(device.id) : connectDevice(device.id)}\r\n                      >\r\n                        {device.status === 'connected' ? 'Disconnect' : 'Connect'}\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n              </div>\r\n\r\n              {/* Device Discovery */}\r\n              <div className=\"settings-section\">\r\n                <div className=\"section-header\">\r\n                  <h3 className=\"section-title\">Device Discovery</h3>\r\n                  <button\r\n                    className={`discover-button ${isDiscovering ? 'discover-button-disabled' : ''}`}\r\n                    onClick={refreshDiscovery}\r\n                    disabled={isDiscovering}\r\n                  >\r\n                    {isDiscovering ? '🔍 Scanning...' : 'Discover'}\r\n                  </button>\r\n                </div>\r\n                {networkDevices.length === 0 ? (\r\n                  <div className=\"empty-device-list\">\r\n                    <p className=\"empty-device-text\">No devices found</p>\r\n                    <p className=\"empty-device-subtext\">\r\n                      {isDiscovering ? 'Scanning for devices...' : 'Click Discover to scan for devices'}\r\n                    </p>\r\n                  </div>\r\n                ) : (\r\n                  networkDevices.map((device) => (\r\n                    <div key={device.id} className=\"discovered-device-card\">\r\n                      <div className=\"device-info\">\r\n                        <p className=\"device-card-name\">{device.name}</p>\r\n                        <p className=\"device-card-type\">{device.type}</p>\r\n                        <p className=\"device-card-last-seen\">{device.lastSeen}</p>\r\n                      </div>\r\n                      <button\r\n                        className=\"pair-button\"\r\n                        onClick={() => pairDevice(device.id)}\r\n                      >\r\n                        Pair\r\n                      </button>\r\n                    </div>\r\n                  ))\r\n                )}\r\n\r\n                <div className=\"qr-buttons-container\">\r\n                  <button className=\"qr-generate-button\" onClick={generateQRCode}>\r\n                    Generate QR\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Additional Settings */}\r\n              <div className=\"settings-section\">\r\n                <h3 className=\"section-title\">Additional Settings</h3>\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => setShowSyncSettings(true)}\r\n                >\r\n                  <span className=\"setting-item-text\">Sync Settings</span>\r\n                  <span className=\"setting-item-arrow\">›</span>\r\n                </button>\r\n\r\n                {/* Background Sync Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    const newValue = !backgroundSyncEnabled;\r\n                    setBackgroundSyncEnabled(newValue);\r\n                    showMessage(newValue ? 'Background sync enabled' : 'Background sync disabled');\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Background Sync</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {backgroundSyncEnabled ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n\r\n                {/* Network Discovery Setting */}\r\n                <button\r\n                  className=\"setting-item\"\r\n                  onClick={() => {\r\n                    if (isDiscovering) {\r\n                      setIsDiscovering(false);\r\n                      showMessage('Network discovery stopped');\r\n                    } else {\r\n                      setIsDiscovering(true);\r\n                      showMessage('Network discovery started');\r\n                    }\r\n                  }}\r\n                >\r\n                  <span className=\"setting-item-text\">Network Discovery</span>\r\n                  <span className=\"setting-item-value\">\r\n                    {isDiscovering ? 'on' : 'off'}\r\n                  </span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Sync Settings Modal */}\r\n      {showSyncSettings && (\r\n        <div className=\"modal-overlay\" onClick={() => setShowSyncSettings(false)}>\r\n          <div className=\"sync-settings-modal\" onClick={(e) => e.stopPropagation()}>\r\n            <div className=\"settings-header\">\r\n              <h2 className=\"settings-title\">Sync Settings</h2>\r\n              <button className=\"close-button\" onClick={() => setShowSyncSettings(false)}>\r\n                ✕\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"settings-content\">\r\n              <div className=\"settings-section\">\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Auto Sync</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.autoSync}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, autoSync: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Sync Delay: {syncSettings.syncDelay} seconds</label>\r\n                  <p className=\"sync-setting-description\">\r\n                    {syncSettings.syncDelay === 0 ? 'Instant sync' : `${syncSettings.syncDelay} second delay`}\r\n                  </p>\r\n                  <div className=\"sync-delay-controls\">\r\n                    <button\r\n                      className=\"sync-delay-button\"\r\n                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.max(0, syncSettings.syncDelay - 1)})}\r\n                    >\r\n                      -\r\n                    </button>\r\n                    <span className=\"sync-delay-value\">{syncSettings.syncDelay}s</span>\r\n                    <button\r\n                      className=\"sync-delay-button\"\r\n                      onClick={() => setSyncSettings({...syncSettings, syncDelay: Math.min(30, syncSettings.syncDelay + 1)})}\r\n                    >\r\n                      +\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Sync on Connect</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.syncOnConnect}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, syncOnConnect: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <label className=\"sync-setting-label\">Bidirectional Sync</label>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    className=\"sync-setting-checkbox\"\r\n                    checked={syncSettings.bidirectional}\r\n                    onChange={(e) => setSyncSettings({...syncSettings, bidirectional: e.target.checked})}\r\n                  />\r\n                </div>\r\n\r\n                {/* Cross-Platform Sync Controls */}\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('📱 Windows ↔ Android sync enabled! Clipboard will sync between Windows and Android devices.')}\r\n                  >\r\n                    📱 Windows ↔ Android Sync\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('🖥️ Windows ↔ Windows sync enabled! Clipboard will sync between Windows PCs.')}\r\n                  >\r\n                    🖥️ Windows ↔ Windows Sync\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"sync-setting-item\">\r\n                  <button\r\n                    className=\"cross-platform-sync-button\"\r\n                    onClick={() => showMessage('🚀 Universal sync enabled! Clipboard will sync across all connected devices.')}\r\n                  >\r\n                    🚀 Sync All Devices\r\n                  </button>\r\n                </div>\r\n\r\n                {/* Floating Overlay Button Settings */}\r\n                <div className=\"settings-section\">\r\n                  <h3 className=\"settings-section-title\">📋 Floating Overlay Button Settings</h3>\r\n                  <p className=\"settings-description\">\r\n                    Configure the floating overlay button for quick access to connected device clipboards\r\n                  </p>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Enable Floating Overlay Button</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('📋 Floating overlay button is always enabled for accessibility')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Show Device Count Badge</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('🔢 Device count badge enabled')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"settings-row\">\r\n                    <span className=\"settings-label\">Auto-hide After Copy</span>\r\n                    <button\r\n                      className=\"settings-toggle active\"\r\n                      onClick={() => showMessage('⏱️ Auto-hide after copy enabled')}\r\n                    >\r\n                      <div className=\"settings-toggle-thumb active\"></div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <p className=\"settings-note\">\r\n                    💡 The floating overlay button (📋) appears in the header and provides instant access to clipboard content from all connected Android devices and Windows PCs. Tap to open, long-press items to quick-copy.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGP,QAAQ,CAAC,sIAAsI,CAAC;EACtM,MAAM,CAACQ,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGT,QAAQ,CAAC,0IAA0I,CAAC;EACpN,MAAM,CAACU,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACY,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACc,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACgB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAEhF,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAC/C;IAAEoB,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,iDAAiD;IAAEC,SAAS,EAAE;EAAgB,CAAC,EACnG;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,gLAAgL;IAAEC,SAAS,EAAE;EAAiB,CAAC,EACnO;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,+BAA+B;IAAEC,SAAS,EAAE;EAAa,CAAC,EAC9E;IAAEF,EAAE,EAAE,GAAG;IAAEC,OAAO,EAAE,sEAAsE;IAAEC,SAAS,EAAE;EAAc,CAAC,CACvH,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,WAAW,CAAC;EACrE,MAAM,CAACiC,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;;EAE/E;EACA,MAAM,CAACmC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC;IAC/C2C,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,CACjD;IACEoB,EAAE,EAAE,WAAW;IACf6B,IAAI,EAAE,0BAA0B;IAChCC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE;EACb,CAAC,EACD;IACEjC,EAAE,EAAE,SAAS;IACb6B,IAAI,EAAE,sBAAsB;IAC5BC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,cAAc;IACtBC,QAAQ,EAAE,YAAY;IACtBC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,CACnC;IACEoB,EAAE,EAAE,cAAc;IAClB6B,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,uBAAuB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEjC,EAAE,EAAE,gBAAgB;IACpB6B,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,cAAc;IACpBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,uBAAuB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC;IAC3CiD,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACAtD,KAAK,CAACE,SAAS,CAAC,MAAM;IACpB,MAAMwD,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF;QACA,IAAIC,UAAU,GAAG,YAAY;QAC7B,IAAIC,SAAS,GAAG,SAAS;;QAEzB;QACA,IAAIC,SAAS,CAACC,aAAa,EAAE;UAC3B,MAAMC,QAAQ,GAAGF,SAAS,CAACC,aAAa,CAACC,QAAQ;UACjDH,SAAS,GAAGG,QAAQ,IAAI,SAAS;QACnC,CAAC,MAAM,IAAIF,SAAS,CAACG,SAAS,EAAE;UAC9B;UACA,MAAMC,YAAY,GAAGJ,SAAS,CAACG,SAAS,CAACE,KAAK,CAAC,uBAAuB,CAAC;UACvE,IAAID,YAAY,EAAE;YAChB,MAAME,OAAO,GAAGF,YAAY,CAAC,CAAC,CAAC;YAC/B,QAAQE,OAAO;cACb,KAAK,MAAM;gBAAEP,SAAS,GAAG,eAAe;gBAAE;cAC1C,KAAK,KAAK;gBAAEA,SAAS,GAAG,aAAa;gBAAE;cACvC,KAAK,KAAK;gBAAEA,SAAS,GAAG,WAAW;gBAAE;cACrC,KAAK,KAAK;gBAAEA,SAAS,GAAG,WAAW;gBAAE;cACrC;gBAASA,SAAS,GAAG,cAAcO,OAAO,EAAE;YAC9C;UACF;QACF;;QAEA;QACA,IAAI;UACF,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,IAAIF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,EAAE;YACxEX,UAAU,GAAG,GAAGS,MAAM,CAACC,QAAQ,CAACC,QAAQ,eAAe;UACzD,CAAC,MAAM;YACL;YACAX,UAAU,GAAG,GAAGC,SAAS,YAAY;UACvC;QACF,CAAC,CAAC,OAAOW,CAAC,EAAE;UACVZ,UAAU,GAAG,GAAGC,SAAS,YAAY;QACvC;QAEAH,aAAa,CAACe,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPtB,IAAI,EAAES,UAAU;UAChBR,IAAI,EAAES,SAAS;UACfR,MAAM,EAAEpB,gBAAgB,KAAK,WAAW,GAAG,QAAQ,GAAG,cAAc;UACpEqB,QAAQ,EAAErB,gBAAgB,KAAK,WAAW,GAAG,KAAK,GAAG;QACvD,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,OAAOyC,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,KAAK,CAAC;QAChDhB,aAAa,CAACe,IAAI,KAAK;UACrB,GAAGA,IAAI;UACPtB,IAAI,EAAE,mBAAmB;UACzBC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAEpB,gBAAgB,KAAK,WAAW,GAAG,QAAQ,GAAG,cAAc;UACpEqB,QAAQ,EAAErB,gBAAgB,KAAK,WAAW,GAAG,KAAK,GAAG;QACvD,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAED0B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC1B,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAM4C,WAAW,GAAIC,IAAI,IAAK;IAC5B9C,iBAAiB,CAAC8C,IAAI,CAAC;IACvBC,UAAU,CAAC,MAAM/C,iBAAiB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EAC/C,CAAC;EAED,MAAMgD,eAAe,GAAG,MAAOzD,OAAO,IAAK;IACzC,IAAI;MACF,MAAMuC,SAAS,CAACmB,SAAS,CAACC,SAAS,CAAC3D,OAAO,CAAC;MAC5Cd,sBAAsB,CAACc,OAAO,CAAC;MAC/BsD,WAAW,CAAC,6BAA6B,CAAC;IAC5C,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDG,WAAW,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMM,UAAU,GAAIC,IAAI,IAAK;IAC3BJ,eAAe,CAACI,IAAI,CAAC7D,OAAO,CAAC;IAC7BZ,2BAA2B,CAACyE,IAAI,CAAC7D,OAAO,CAAC;EAC3C,CAAC;EAED,MAAM8D,iBAAiB,GAAIC,MAAM,IAAK;IACpC,MAAMC,cAAc,GAAGnE,YAAY,CAACoE,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAAC9D,EAAE,KAAKgE,MAAM,CAAC;IACtEjE,eAAe,CAACkE,cAAc,CAAC;IAC/BV,WAAW,CAAC,2BAA2B,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMY,sBAAsB,GAAGA,CAAA,KAAM;IACnCxE,wBAAwB,CAACT,mBAAmB,CAAC;IAC7CK,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM6E,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,MAAMC,UAAU,GAAG3E,qBAAqB,CAAC4E,IAAI,CAAC,CAAC;IAC/C,IAAI,CAACD,UAAU,EAAE;MACfd,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEApE,sBAAsB,CAACkF,UAAU,CAAC;IAClC,IAAI;MACF,MAAM7B,SAAS,CAACmB,SAAS,CAACC,SAAS,CAACS,UAAU,CAAC;IACjD,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACkB,IAAI,CAAC,6BAA6B,EAAEnB,KAAK,CAAC;IACpD;IAEA7D,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;IAC5B4D,WAAW,CAAC,kCAAkC,CAAC;EACjD,CAAC;EAED,MAAMiB,oBAAoB,GAAGA,CAAA,KAAM;IACjCjF,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,wBAAwB,CAAC,EAAE,CAAC;EAC9B,CAAC;EAED,MAAM8E,2BAA2B,GAAGA,CAAA,KAAM;IACxC5E,6BAA6B,CAACT,wBAAwB,CAAC;IACvDK,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMiF,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAML,UAAU,GAAGzE,0BAA0B,CAAC0E,IAAI,CAAC,CAAC;IACpD,IAAI,CAACD,UAAU,EAAE;MACfd,WAAW,CAAC,yBAAyB,CAAC;MACtC;IACF;IAEAlE,2BAA2B,CAACgF,UAAU,CAAC;IACvC5E,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;IACjC0D,WAAW,CAAC,uCAAuC,CAAC;EACtD,CAAC;EAED,MAAMoB,yBAAyB,GAAGA,CAAA,KAAM;IACtClF,2BAA2B,CAAC,KAAK,CAAC;IAClCI,6BAA6B,CAAC,EAAE,CAAC;EACnC,CAAC;EAED,MAAM+E,OAAO,GAAGA,CAAA,KAAM;IACpBrB,WAAW,CAAC,mCAAmC,CAAC;IAChDE,UAAU,CAAC,MAAM;MACfF,WAAW,CAAC,mBAAmB,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrE,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChCgD,WAAW,CAAChD,aAAa,GAAG,0BAA0B,GAAG,sBAAsB,CAAC;EAClF,CAAC;EAED,MAAMuE,cAAc,GAAGA,CAAA,KAAM;IAC3BvB,WAAW,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAMwB,qBAAqB,GAAGA,CAAA,KAAM;IAClCjE,2BAA2B,CAAC,CAACD,wBAAwB,CAAC;IACtD0C,WAAW,CAAC1C,wBAAwB,GAAG,2BAA2B,GAAG,0BAA0B,CAAC;EAClG,CAAC;;EAED;EACA,MAAMmE,YAAY,GAAIC,QAAQ,IAAK;IACjC,MAAMC,cAAc,GAAGvD,aAAa,CAACuC,MAAM,CAACiB,MAAM,IAAIA,MAAM,CAACnF,EAAE,KAAKiF,QAAQ,CAAC;IAC7ErD,gBAAgB,CAACsD,cAAc,CAAC;IAChC3B,WAAW,CAAC,wCAAwC,CAAC;EACvD,CAAC;EAED,MAAM6B,aAAa,GAAIH,QAAQ,IAAK;IAClC,MAAMC,cAAc,GAAGvD,aAAa,CAAC0D,GAAG,CAACF,MAAM,IAC7CA,MAAM,CAACnF,EAAE,KAAKiF,QAAQ,GAAG;MAAE,GAAGE,MAAM;MAAEpD,MAAM,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAW,CAAC,GAAGmD,MACtF,CAAC;IACDvD,gBAAgB,CAACsD,cAAc,CAAC;IAChC3B,WAAW,CAAC,iCAAiC,CAAC;EAChD,CAAC;EAED,MAAM+B,gBAAgB,GAAIL,QAAQ,IAAK;IACrC,MAAMC,cAAc,GAAGvD,aAAa,CAAC0D,GAAG,CAACF,MAAM,IAC7CA,MAAM,CAACnF,EAAE,KAAKiF,QAAQ,GAAG;MAAE,GAAGE,MAAM;MAAEpD,MAAM,EAAE,cAAc;MAAEC,QAAQ,EAAE;IAAW,CAAC,GAAGmD,MACzF,CAAC;IACDvD,gBAAgB,CAACsD,cAAc,CAAC;IAChC3B,WAAW,CAAC,wBAAwB,CAAC;EACvC,CAAC;EAED,MAAMgC,UAAU,GAAIN,QAAQ,IAAK;IAC/B,MAAMO,YAAY,GAAGtD,iBAAiB,CAACuD,IAAI,CAACN,MAAM,IAAIA,MAAM,CAACnF,EAAE,KAAKiF,QAAQ,CAAC;IAC7E,IAAIO,YAAY,EAAE;MAChB,MAAME,eAAe,GAAG;QACtB,GAAGF,YAAY;QACfzD,MAAM,EAAE,WAAW;QACnBC,QAAQ,EAAE;MACZ,CAAC;MACDJ,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE+D,eAAe,CAAC,CAAC;MACrDnC,WAAW,CAAC,8BAA8BiC,YAAY,CAAC3D,IAAI,EAAE,CAAC;IAChE;EACF,CAAC;EAED,MAAM8D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIxE,aAAa,EAAE;IAEnBC,gBAAgB,CAAC,IAAI,CAAC;IACtBmC,WAAW,CAAC,4BAA4B,CAAC;;IAEzC;IACAE,UAAU,CAAC,MAAM;MACfvC,iBAAiB,CAACgB,iBAAiB,CAAC;MACpCd,gBAAgB,CAAC,KAAK,CAAC;MACvBmC,WAAW,CAAC,YAAYrB,iBAAiB,CAAC0D,MAAM,oBAAoB,CAAC;IACvE,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBtC,WAAW,CAAC,2EAA2E,CAAC;EAC1F,CAAC;EAED,MAAMuC,cAAc,GAAGA,CAAA,KAAM;IAC3BvC,WAAW,CAAC,mFAAmF,CAAC;EAClG,CAAC;;EAED;EACA,MAAMwC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMC,SAAS,GAAGrF,gBAAgB,KAAK,WAAW,GAAG,cAAc,GAAG,WAAW;IACjFC,mBAAmB,CAACoF,SAAS,CAAC;IAC9BzC,WAAW,CAAC,oCAAoCyC,SAAS,EAAE,CAAC;EAC9D,CAAC;EAED,oBACEjH,OAAA;IAAKkH,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BnH,OAAA;MAAKkH,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBACrBnH,OAAA;QAAKkH,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BnH,OAAA;UAAKkH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BnH,OAAA;YACEoH,GAAG,EAAC,wBAAwB;YAC5BC,GAAG,EAAC,aAAa;YACjBH,SAAS,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACFzH,OAAA;YAAKkH,SAAS,EAAE,kBAAkBtE,aAAa,CAAC8E,IAAI,CAACtB,MAAM,IAAIA,MAAM,CAACpD,MAAM,KAAK,WAAW,CAAC,GAAG,WAAW,GAAG,cAAc;UAAG;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnI,CAAC,eACNzH,OAAA;UAAIkH,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACNzH,OAAA;QAAKkH,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BnH,OAAA;UACEkH,SAAS,EAAE,eAAepF,wBAAwB,GAAG,QAAQ,GAAG,EAAE,EAAG;UACrE6F,OAAO,EAAE3B,qBAAsB;UAC/B4B,KAAK,EAAC,kCAAkC;UAAAT,QAAA,EACzC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzH,OAAA;UACEkH,SAAS,EAAE,eAAe1F,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC1DmG,OAAO,EAAE7B,iBAAkB;UAC3B8B,KAAK,EAAC,YAAY;UAAAT,QAAA,eAElBnH,OAAA;YAAKkH,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBnH,OAAA;cAAKkH,SAAS,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChCzH,OAAA;cAAKkH,SAAS,EAAC;YAAU;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACTzH,OAAA;UAAQkH,SAAS,EAAC,aAAa;UAACS,OAAO,EAAE5B,cAAe;UAAC6B,KAAK,EAAC,UAAU;UAAAT,QAAA,EAAC;QAE1E;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzH,OAAA;UAAQkH,SAAS,EAAC,aAAa;UAACS,OAAO,EAAEA,CAAA,KAAMtG,eAAe,CAAC,IAAI,CAAE;UAACuG,KAAK,EAAC,UAAU;UAAAT,QAAA,EAAC;QAEvF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/F,cAAc,iBACb1B,OAAA;MAAKkH,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BnH,OAAA;QAAAmH,QAAA,EAAOzF;MAAc;QAAA4F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDzH,OAAA;MAAKkH,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3BnH,OAAA;QAAKkH,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BnH,OAAA;UAAKkH,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpCnH,OAAA;YAAKkH,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CnH,OAAA;cAAIkH,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDzH,OAAA;cAAGkH,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAE/D,UAAU,CAACN;YAAI;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELlH,mBAAmB,gBAClBP,OAAA;UAAKkH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BnH,OAAA;YACEkH,SAAS,EAAC,iBAAiB;YAC3BW,KAAK,EAAElH,qBAAsB;YAC7BmH,QAAQ,EAAG3D,CAAC,IAAKvD,wBAAwB,CAACuD,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAC1DG,WAAW,EAAC,uCAAuC;YACnDC,SAAS;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACFzH,OAAA;YAAKkH,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnH,OAAA;cAAQkH,SAAS,EAAC,yBAAyB;cAACS,OAAO,EAAEtC,kBAAmB;cAAA8B,QAAA,EAAC;YAEzE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzH,OAAA;cAAQkH,SAAS,EAAC,2BAA2B;cAACS,OAAO,EAAElC,oBAAqB;cAAA0B,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENzH,OAAA;UAAKkH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BnH,OAAA;YACEkH,SAAS,EAAC,uCAAuC;YACjDS,OAAO,EAAEA,CAAA,KAAMhD,eAAe,CAACxE,mBAAmB,CAAE;YAAAgH,QAAA,gBAEpDnH,OAAA;cAAGkH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAEhH;YAAmB;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDzH,OAAA;cAAGkH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA8B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChEzH,OAAA;cAAQkH,SAAS,EAAC,oBAAoB;cAACS,OAAO,EAAEvC,sBAAuB;cAAA+B,QAAA,EAAC;YAExE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzH,OAAA;QAAKkH,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BnH,OAAA;UAAKkH,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpCnH,OAAA;YAAIkH,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7DzH,OAAA;YAAGkH,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,EAELhH,wBAAwB,gBACvBT,OAAA;UAAKkH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BnH,OAAA;YACEkH,SAAS,EAAC,iBAAiB;YAC3BW,KAAK,EAAEhH,0BAA2B;YAClCiH,QAAQ,EAAG3D,CAAC,IAAKrD,6BAA6B,CAACqD,CAAC,CAAC4D,MAAM,CAACF,KAAK,CAAE;YAC/DG,WAAW,EAAC,4CAA4C;YACxDC,SAAS;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACFzH,OAAA;YAAKkH,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BnH,OAAA;cAAQkH,SAAS,EAAC,8BAA8B;cAACS,OAAO,EAAEhC,uBAAwB;cAAAwB,QAAA,EAAC;YAEnF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzH,OAAA;cAAQkH,SAAS,EAAC,gCAAgC;cAACS,OAAO,EAAE/B,yBAA0B;cAAAuB,QAAA,EAAC;YAEvF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENzH,OAAA;UAAKkH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BnH,OAAA;YACEkH,SAAS,EAAC,4CAA4C;YACtDS,OAAO,EAAEA,CAAA,KAAMhD,eAAe,CAACtE,wBAAwB,CAAE;YAAA8G,QAAA,gBAEzDnH,OAAA;cAAGkH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAE9G;YAAwB;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DzH,OAAA;cAAGkH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAkC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpEzH,OAAA;cAAQkH,SAAS,EAAC,oBAAoB;cAACS,OAAO,EAAEjC,2BAA4B;cAAAyB,QAAA,EAAC;YAE7E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNzH,OAAA;QAAIkH,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpDzH,OAAA;QAAKkH,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BpG,YAAY,CAACuF,GAAG,CAAEvB,IAAI,iBACrB/E,OAAA;UAEEkH,SAAS,EAAC,cAAc;UACxBS,OAAO,EAAEA,CAAA,KAAM7C,UAAU,CAACC,IAAI,CAAE;UAAAoC,QAAA,gBAEhCnH,OAAA;YAAKkH,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCnH,OAAA;cAAMkH,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEpC,IAAI,CAAC5D;YAAS;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDzH,OAAA;cACEkH,SAAS,EAAC,eAAe;cACzBS,OAAO,EAAGxD,CAAC,IAAK;gBACdA,CAAC,CAAC+D,eAAe,CAAC,CAAC;gBACnBlD,iBAAiB,CAACD,IAAI,CAAC9D,EAAE,CAAC;cAC5B,CAAE;cAAAkG,QAAA,EACH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNzH,OAAA;YAAGkH,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEpC,IAAI,CAAC7D;UAAO;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GAhBzC1C,IAAI,CAAC9D,EAAE;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBT,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzH,OAAA;MAAQkH,SAAS,EAAC,sBAAsB;MAACS,OAAO,EAAE9B,OAAQ;MAAAsB,QAAA,eACxDnH,OAAA;QAAKoH,GAAG,EAAC,WAAW;QAACC,GAAG,EAAC,MAAM;QAACH,SAAS,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,EAGRrG,YAAY,iBACXpB,OAAA;MAAKkH,SAAS,EAAC,eAAe;MAACS,OAAO,EAAEA,CAAA,KAAMtG,eAAe,CAAC,KAAK,CAAE;MAAA8F,QAAA,eACnEnH,OAAA;QAAKkH,SAAS,EAAC,kBAAkB;QAACS,OAAO,EAAGxD,CAAC,IAAKA,CAAC,CAAC+D,eAAe,CAAC,CAAE;QAAAf,QAAA,gBACpEnH,OAAA;UAAKkH,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BnH,OAAA;YAAIkH,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CzH,OAAA;YAAQkH,SAAS,EAAC,cAAc;YAACS,OAAO,EAAEA,CAAA,KAAMtG,eAAe,CAAC,KAAK,CAAE;YAAA8F,QAAA,EAAC;UAExE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENzH,OAAA;UAAKkH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAE/BnH,OAAA;YAAKkH,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BnH,OAAA;cAAIkH,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CzH,OAAA;cAAKkH,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BnH,OAAA;gBAAGkH,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAE/D,UAAU,CAACN;cAAI;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDzH,OAAA;gBAAGkH,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAE/D,UAAU,CAACL;cAAI;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDzH,OAAA;gBAAGkH,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,MAAI,EAAC/D,UAAU,CAACF,SAAS;cAAA;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DzH,OAAA;gBAAKkH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCnH,OAAA;kBAAKkH,SAAS,EAAE,2BAA2B9D,UAAU,CAACJ,MAAM;gBAAG;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtEzH,OAAA;kBAAMkH,SAAS,EAAE,sBAAsB9D,UAAU,CAACJ,MAAM,EAAG;kBAAAmE,QAAA,GAAC,UAClD,EAAC/D,UAAU,CAACJ,MAAM,KAAK,QAAQ,GAAG,WAAW,GAAG,cAAc;gBAAA;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EACLrE,UAAU,CAACJ,MAAM,KAAK,cAAc,iBACnChD,OAAA;gBAAGkH,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzH,OAAA;YAAKkH,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BnH,OAAA;cAAIkH,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChD7E,aAAa,CAACiE,MAAM,KAAK,CAAC,gBACzB7G,OAAA;cAAKkH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnH,OAAA;gBAAGkH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5DzH,OAAA;gBAAGkH,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAA+C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,GAEN7E,aAAa,CAAC0D,GAAG,CAAEF,MAAM,iBACvBpG,OAAA;cAAqBkH,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBAEnDnH,OAAA;gBACEkH,SAAS,EAAC,yBAAyB;gBACnCS,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAACG,MAAM,CAACnF,EAAE,CAAE;gBAAAkG,QAAA,EACxC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAETzH,OAAA;gBAAKkH,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BnH,OAAA;kBAAGkH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEf,MAAM,CAACtD;gBAAI;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDzH,OAAA;kBAAGkH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEf,MAAM,CAACrD;gBAAI;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDzH,OAAA;kBAAKkH,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCnH,OAAA;oBAAKkH,SAAS,EAAE,2BAA2Bd,MAAM,CAACpD,MAAM;kBAAG;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClEzH,OAAA;oBAAMkH,SAAS,EAAE,sBAAsBd,MAAM,CAACpD,MAAM,EAAG;oBAAAmE,QAAA,GACpDf,MAAM,CAACpD,MAAM,KAAK,WAAW,GAAG,WAAW,GAAG,cAAc,EAAC,UAAG,EAACoD,MAAM,CAACnD,QAAQ;kBAAA;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzH,OAAA;gBACEkH,SAAS,EAAE,qCAAqCd,MAAM,CAACpD,MAAM,KAAK,WAAW,GAAG,mBAAmB,GAAG,gBAAgB,EAAG;gBACzH2E,OAAO,EAAEA,CAAA,KAAMvB,MAAM,CAACpD,MAAM,KAAK,WAAW,GAAGuD,gBAAgB,CAACH,MAAM,CAACnF,EAAE,CAAC,GAAGoF,aAAa,CAACD,MAAM,CAACnF,EAAE,CAAE;gBAAAkG,QAAA,EAErGf,MAAM,CAACpD,MAAM,KAAK,WAAW,GAAG,YAAY,GAAG;cAAS;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA,GA1BDrB,MAAM,CAACnF,EAAE;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2Bd,CACN,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzH,OAAA;YAAKkH,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BnH,OAAA;cAAKkH,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BnH,OAAA;gBAAIkH,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDzH,OAAA;gBACEkH,SAAS,EAAE,mBAAmB9E,aAAa,GAAG,0BAA0B,GAAG,EAAE,EAAG;gBAChFuF,OAAO,EAAEf,gBAAiB;gBAC1BuB,QAAQ,EAAE/F,aAAc;gBAAA+E,QAAA,EAEvB/E,aAAa,GAAG,gBAAgB,GAAG;cAAU;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLvF,cAAc,CAAC2E,MAAM,KAAK,CAAC,gBAC1B7G,OAAA;cAAKkH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnH,OAAA;gBAAGkH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrDzH,OAAA;gBAAGkH,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAChC/E,aAAa,GAAG,yBAAyB,GAAG;cAAoC;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,GAENvF,cAAc,CAACoE,GAAG,CAAEF,MAAM,iBACxBpG,OAAA;cAAqBkH,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrDnH,OAAA;gBAAKkH,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BnH,OAAA;kBAAGkH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEf,MAAM,CAACtD;gBAAI;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDzH,OAAA;kBAAGkH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,EAAEf,MAAM,CAACrD;gBAAI;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjDzH,OAAA;kBAAGkH,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEf,MAAM,CAACnD;gBAAQ;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNzH,OAAA;gBACEkH,SAAS,EAAC,aAAa;gBACvBS,OAAO,EAAEA,CAAA,KAAMnB,UAAU,CAACJ,MAAM,CAACnF,EAAE,CAAE;gBAAAkG,QAAA,EACtC;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAXDrB,MAAM,CAACnF,EAAE;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYd,CACN,CACF,eAEDzH,OAAA;cAAKkH,SAAS,EAAC,sBAAsB;cAAAC,QAAA,eACnCnH,OAAA;gBAAQkH,SAAS,EAAC,oBAAoB;gBAACS,OAAO,EAAEZ,cAAe;gBAAAI,QAAA,EAAC;cAEhE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNzH,OAAA;YAAKkH,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BnH,OAAA;cAAIkH,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtDzH,OAAA;cACEkH,SAAS,EAAC,cAAc;cACxBS,OAAO,EAAEA,CAAA,KAAMpG,mBAAmB,CAAC,IAAI,CAAE;cAAA4F,QAAA,gBAEzCnH,OAAA;gBAAMkH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDzH,OAAA;gBAAMkH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eAGTzH,OAAA;cACEkH,SAAS,EAAC,cAAc;cACxBS,OAAO,EAAEA,CAAA,KAAM;gBACb,MAAMS,QAAQ,GAAG,CAACpG,qBAAqB;gBACvCC,wBAAwB,CAACmG,QAAQ,CAAC;gBAClC5D,WAAW,CAAC4D,QAAQ,GAAG,yBAAyB,GAAG,0BAA0B,CAAC;cAChF,CAAE;cAAAjB,QAAA,gBAEFnH,OAAA;gBAAMkH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DzH,OAAA;gBAAMkH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EACjCnF,qBAAqB,GAAG,IAAI,GAAG;cAAK;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGTzH,OAAA;cACEkH,SAAS,EAAC,cAAc;cACxBS,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIvF,aAAa,EAAE;kBACjBC,gBAAgB,CAAC,KAAK,CAAC;kBACvBmC,WAAW,CAAC,2BAA2B,CAAC;gBAC1C,CAAC,MAAM;kBACLnC,gBAAgB,CAAC,IAAI,CAAC;kBACtBmC,WAAW,CAAC,2BAA2B,CAAC;gBAC1C;cACF,CAAE;cAAA2C,QAAA,gBAEFnH,OAAA;gBAAMkH,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5DzH,OAAA;gBAAMkH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EACjC/E,aAAa,GAAG,IAAI,GAAG;cAAK;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAnG,gBAAgB,iBACftB,OAAA;MAAKkH,SAAS,EAAC,eAAe;MAACS,OAAO,EAAEA,CAAA,KAAMpG,mBAAmB,CAAC,KAAK,CAAE;MAAA4F,QAAA,eACvEnH,OAAA;QAAKkH,SAAS,EAAC,qBAAqB;QAACS,OAAO,EAAGxD,CAAC,IAAKA,CAAC,CAAC+D,eAAe,CAAC,CAAE;QAAAf,QAAA,gBACvEnH,OAAA;UAAKkH,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BnH,OAAA;YAAIkH,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDzH,OAAA;YAAQkH,SAAS,EAAC,cAAc;YAACS,OAAO,EAAEA,CAAA,KAAMpG,mBAAmB,CAAC,KAAK,CAAE;YAAA4F,QAAA,EAAC;UAE5E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENzH,OAAA;UAAKkH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BnH,OAAA;YAAKkH,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BnH,OAAA;cAAKkH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnH,OAAA;gBAAOkH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvDzH,OAAA;gBACE+C,IAAI,EAAC,UAAU;gBACfmE,SAAS,EAAC,uBAAuB;gBACjCmB,OAAO,EAAE/F,YAAY,CAACE,QAAS;gBAC/BsF,QAAQ,EAAG3D,CAAC,IAAK5B,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEE,QAAQ,EAAE2B,CAAC,CAAC4D,MAAM,CAACM;gBAAO,CAAC;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzH,OAAA;cAAKkH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnH,OAAA;gBAAOkH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAC,cAAY,EAAC7E,YAAY,CAACG,SAAS,EAAC,UAAQ;cAAA;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1FzH,OAAA;gBAAGkH,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACpC7E,YAAY,CAACG,SAAS,KAAK,CAAC,GAAG,cAAc,GAAG,GAAGH,YAAY,CAACG,SAAS;cAAe;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CAAC,eACJzH,OAAA;gBAAKkH,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClCnH,OAAA;kBACEkH,SAAS,EAAC,mBAAmB;kBAC7BS,OAAO,EAAEA,CAAA,KAAMpF,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEG,SAAS,EAAE6F,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjG,YAAY,CAACG,SAAS,GAAG,CAAC;kBAAC,CAAC,CAAE;kBAAA0E,QAAA,EACvG;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTzH,OAAA;kBAAMkH,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAAE7E,YAAY,CAACG,SAAS,EAAC,GAAC;gBAAA;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnEzH,OAAA;kBACEkH,SAAS,EAAC,mBAAmB;kBAC7BS,OAAO,EAAEA,CAAA,KAAMpF,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEG,SAAS,EAAE6F,IAAI,CAACE,GAAG,CAAC,EAAE,EAAElG,YAAY,CAACG,SAAS,GAAG,CAAC;kBAAC,CAAC,CAAE;kBAAA0E,QAAA,EACxG;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzH,OAAA;cAAKkH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnH,OAAA;gBAAOkH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7DzH,OAAA;gBACE+C,IAAI,EAAC,UAAU;gBACfmE,SAAS,EAAC,uBAAuB;gBACjCmB,OAAO,EAAE/F,YAAY,CAACI,aAAc;gBACpCoF,QAAQ,EAAG3D,CAAC,IAAK5B,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEI,aAAa,EAAEyB,CAAC,CAAC4D,MAAM,CAACM;gBAAO,CAAC;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzH,OAAA;cAAKkH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCnH,OAAA;gBAAOkH,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChEzH,OAAA;gBACE+C,IAAI,EAAC,UAAU;gBACfmE,SAAS,EAAC,uBAAuB;gBACjCmB,OAAO,EAAE/F,YAAY,CAACK,aAAc;gBACpCmF,QAAQ,EAAG3D,CAAC,IAAK5B,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEK,aAAa,EAAEwB,CAAC,CAAC4D,MAAM,CAACM;gBAAO,CAAC;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNzH,OAAA;cAAKkH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCnH,OAAA;gBACEkH,SAAS,EAAC,4BAA4B;gBACtCS,OAAO,EAAEA,CAAA,KAAMnD,WAAW,CAAC,6FAA6F,CAAE;gBAAA2C,QAAA,EAC3H;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENzH,OAAA;cAAKkH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCnH,OAAA;gBACEkH,SAAS,EAAC,4BAA4B;gBACtCS,OAAO,EAAEA,CAAA,KAAMnD,WAAW,CAAC,8EAA8E,CAAE;gBAAA2C,QAAA,EAC5G;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENzH,OAAA;cAAKkH,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCnH,OAAA;gBACEkH,SAAS,EAAC,4BAA4B;gBACtCS,OAAO,EAAEA,CAAA,KAAMnD,WAAW,CAAC,8EAA8E,CAAE;gBAAA2C,QAAA,EAC5G;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNzH,OAAA;cAAKkH,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BnH,OAAA;gBAAIkH,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAmC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/EzH,OAAA;gBAAGkH,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAEpC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAEJzH,OAAA;gBAAKkH,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BnH,OAAA;kBAAMkH,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA8B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtEzH,OAAA;kBACEkH,SAAS,EAAC,wBAAwB;kBAClCS,OAAO,EAAEA,CAAA,KAAMnD,WAAW,CAAC,gEAAgE,CAAE;kBAAA2C,QAAA,eAE7FnH,OAAA;oBAAKkH,SAAS,EAAC;kBAA8B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENzH,OAAA;gBAAKkH,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BnH,OAAA;kBAAMkH,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DzH,OAAA;kBACEkH,SAAS,EAAC,wBAAwB;kBAClCS,OAAO,EAAEA,CAAA,KAAMnD,WAAW,CAAC,+BAA+B,CAAE;kBAAA2C,QAAA,eAE5DnH,OAAA;oBAAKkH,SAAS,EAAC;kBAA8B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENzH,OAAA;gBAAKkH,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BnH,OAAA;kBAAMkH,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5DzH,OAAA;kBACEkH,SAAS,EAAC,wBAAwB;kBAClCS,OAAO,EAAEA,CAAA,KAAMnD,WAAW,CAAC,iCAAiC,CAAE;kBAAA2C,QAAA,eAE9DnH,OAAA;oBAAKkH,SAAS,EAAC;kBAA8B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENzH,OAAA;gBAAGkH,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAE7B;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACvH,EAAA,CAtxBQD,GAAG;AAAAwI,EAAA,GAAHxI,GAAG;AAwxBZ,eAAeA,GAAG;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}