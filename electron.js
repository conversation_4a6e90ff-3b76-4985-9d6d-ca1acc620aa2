const { app, BrowserWindow, ipcMain, shell } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');
const { autoUpdater } = require('electron-updater');
const os = require('os');

// Keep a global reference of the window object
let mainWindow;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'public/clipsy-logo-no-bg.png'),
    titleBarStyle: 'default',
    show: false // Don't show until ready
  });

  // Load the app
  const startUrl = isDev
    ? 'http://localhost:3000'
    : `file://${path.join(__dirname, 'build/index.html')}`;

  mainWindow.loadURL(startUrl);

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Focus on window
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// App event handlers
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC handlers for Windows API access
ipcMain.handle('get-computer-name', async () => {
  try {
    // Get Windows computer name using multiple methods
    const computerName = os.hostname() || process.env.COMPUTERNAME || process.env.HOSTNAME;
    return computerName ? computerName.toUpperCase() : 'WINDOWS-PC';
  } catch (error) {
    console.error('Error getting computer name:', error);
    return 'WINDOWS-PC';
  }
});

ipcMain.handle('get-app-version', async () => {
  return app.getVersion();
});

ipcMain.handle('minimize-window', async () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('maximize-window', async () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('close-window', async () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

ipcMain.handle('get-network-info', async () => {
  try {
    return os.networkInterfaces();
  } catch (error) {
    console.error('Error getting network info:', error);
    return {};
  }
});

ipcMain.handle('write-clipboard', async (event, text) => {
  try {
    const { clipboard } = require('electron');
    clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Error writing to clipboard:', error);
    return false;
  }
});

ipcMain.handle('read-clipboard', async () => {
  try {
    const { clipboard } = require('electron');
    return clipboard.readText();
  } catch (error) {
    console.error('Error reading from clipboard:', error);
    return '';
  }
});

ipcMain.handle('show-notification', async (event, { title, body }) => {
  try {
    const { Notification } = require('electron');
    if (Notification.isSupported()) {
      new Notification({ title, body }).show();
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error showing notification:', error);
    return false;
  }
});

ipcMain.handle('get-system-info', async () => {
  try {
    return {
      platform: os.platform(),
      arch: os.arch(),
      release: os.release(),
      hostname: os.hostname(),
      userInfo: os.userInfo(),
      networkInterfaces: os.networkInterfaces()
    };
  } catch (error) {
    console.error('Error getting system info:', error);
    return null;
  }
});

ipcMain.handle('get-device-specs', async () => {
  try {
    const deviceSpecs = {
      computerName: os.hostname() || process.env.COMPUTERNAME,
      deviceName: `${os.hostname()} - ${os.platform()}`,
      osVersion: os.release(),
      platform: os.platform(),
      architecture: os.arch(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpus: os.cpus(),
      networkInterfaces: os.networkInterfaces()
    };
    return deviceSpecs;
  } catch (error) {
    console.error('Error getting device specs:', error);
    return null;
  }
});

// Auto updater events
if (!isDev) {
  autoUpdater.checkForUpdatesAndNotify();
}

autoUpdater.on('update-available', () => {
  console.log('Update available');
});

autoUpdater.on('update-downloaded', () => {
  console.log('Update downloaded');
  autoUpdater.quitAndInstall();
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// Handle certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (isDev) {
    // In development, ignore certificate errors
    event.preventDefault();
    callback(true);
  } else {
    // In production, use default behavior
    callback(false);
  }
});
