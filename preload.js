const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Computer name and system info
  getComputerName: () => ipcRenderer.invoke('get-computer-name'),
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
  getDeviceSpecs: () => ipcRenderer.invoke('get-device-specs'),
  
  // Platform info
  platform: process.platform,
  
  // App version
  getVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  
  // File system operations (if needed)
  openFile: () => ipc<PERSON>ender<PERSON>.invoke('open-file'),
  saveFile: (data) => ipcRenderer.invoke('save-file', data),
  
  // Network operations
  getNetworkInfo: () => ipcR<PERSON>er.invoke('get-network-info'),
  
  // Clipboard operations
  writeClipboard: (text) => ipcRenderer.invoke('write-clipboard', text),
  readClipboard: () => ipcRenderer.invoke('read-clipboard'),
  
  // Notifications
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', { title, body }),
  
  // Development helpers
  isDev: process.env.NODE_ENV === 'development',
  
  // Event listeners
  onWindowEvent: (callback) => {
    ipcRenderer.on('window-event', callback);
    return () => ipcRenderer.removeListener('window-event', callback);
  },
  
  onAppUpdate: (callback) => {
    ipcRenderer.on('app-update', callback);
    return () => ipcRenderer.removeListener('app-update', callback);
  }
});

// Also expose a simpler API for backward compatibility
contextBridge.exposeInMainWorld('nativeAPI', {
  getComputerName: () => ipcRenderer.invoke('get-computer-name'),
  getDeviceSpecs: () => ipcRenderer.invoke('get-device-specs'),
  platform: process.platform
});

// Log that preload script has loaded
console.log('Preload script loaded successfully');
