/**
 * Network Discovery Service for Clipsy Windows App
 * Handles local network device discovery, IP detection, and device broadcasting
 * Makes Windows devices discoverable by other Clipsy devices on the local network
 */

const { v4: uuidv4 } = require('uuid');
const os = require('os');
const dgram = require('dgram');
const http = require('http');

class NetworkDiscoveryService {
  constructor() {
    this.callbacks = null;
    this.discoveredDevices = new Map();
    this.discoveryInterval = null;
    this.broadcastInterval = null;
    this.udpSocket = null;
    this.httpServer = null;
    this.isDiscovering = false;
    this.isBroadcasting = false;
    this.currentNetworkInfo = null;
    this.currentDeviceInfo = null;

    // Device identification
    this.deviceId = this.generateDeviceId();
    this.deviceName = this.generateDeviceName();

    // Configuration
    this.CLIPSY_PORTS = [8080, 8081, 8082, 8083, 8084, 8085];
    this.DISCOVERY_INTERVAL = 15000; // 15 seconds
    this.BROADCAST_INTERVAL = 10000; // 10 seconds
    this.DEVICE_TIMEOUT = 45000; // 45 seconds
    this.BROADCAST_PORT = 8888; // UDP broadcast port
    this.HTTP_DISCOVERY_PORT = 8889; // HTTP discovery endpoint
    this.SERVICE_NAME = '_clipsy._tcp.local';
  }

  /**
   * Generate unique device ID
   */
  generateDeviceId() {
    return uuidv4();
  }

  /**
   * Generate device name based on platform
   */
  generateDeviceName() {
    const hostname = os.hostname();
    const timestamp = new Date().toISOString().slice(0, 10);
    return `Clipsy-Windows-${hostname}-${timestamp}`;
  }

  /**
   * Get current device capabilities
   */
  getDeviceCapabilities() {
    return {
      supportsClipboard: true,
      supportsFiles: true,
      supportsImages: true,
      supportsRichText: true,
      supportsEncryption: true,
      maxClipboardSize: 10 * 1024 * 1024, // 10MB
      supportedFormats: ['text/plain', 'text/html', 'text/rtf', 'image/png', 'image/jpeg', 'application/octet-stream']
    };
  }

  /**
   * Initialize network discovery service
   */
  async initialize(callbacks) {
    this.callbacks = callbacks;
    await this.detectNetworkInfo();
    await this.initializeCurrentDevice();
    this.setupHttpDiscoveryServer();
  }

  /**
   * Initialize current device information
   */
  async initializeCurrentDevice() {
    const networkInfo = await this.detectNetworkInfo();
    this.currentDeviceInfo = {
      id: this.deviceId,
      name: this.deviceName,
      ip: networkInfo?.ip || '127.0.0.1',
      port: this.CLIPSY_PORTS[0],
      type: 'windows',
      lastSeen: new Date().toISOString(),
      isReachable: true,
      services: ['clipboard', 'sync', 'files'],
      capabilities: this.getDeviceCapabilities(),
      version: '1.0.0',
      platform: 'win32'
    };
  }

  /**
   * Detect network information
   */
  async detectNetworkInfo() {
    try {
      const interfaces = os.networkInterfaces();
      let localIP = null;
      let isConnected = false;

      // Find the first non-internal IPv4 address
      for (const [name, addresses] of Object.entries(interfaces)) {
        if (addresses) {
          for (const addr of addresses) {
            if (addr.family === 'IPv4' && !addr.internal) {
              localIP = addr.address;
              isConnected = true;
              break;
            }
          }
        }
        if (localIP) break;
      }

      this.currentNetworkInfo = {
        localIP,
        isConnected,
        connectionType: 'ethernet' // Assume ethernet for Windows
      };

      if (this.callbacks?.onNetworkChanged) {
        this.callbacks.onNetworkChanged(this.currentNetworkInfo);
      }

      return this.currentNetworkInfo;
    } catch (error) {
      console.error('Error detecting network info:', error);
      return null;
    }
  }

  /**
   * Setup HTTP discovery server to receive broadcasts
   */
  setupHttpDiscoveryServer() {
    try {
      this.httpServer = http.createServer((req, res) => {
        if (req.method === 'POST' && req.url === '/clipsy/discovery') {
          let body = '';
          req.on('data', chunk => {
            body += chunk.toString();
          });
          req.on('end', () => {
            try {
              const message = JSON.parse(body);
              this.handleDiscoveryMessage(message);
              res.writeHead(200, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ status: 'received' }));
            } catch (error) {
              res.writeHead(400, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ error: 'Invalid JSON' }));
            }
          });
        } else {
          res.writeHead(404);
          res.end();
        }
      });

      this.httpServer.listen(this.HTTP_DISCOVERY_PORT, () => {
        console.log(`HTTP discovery server listening on port ${this.HTTP_DISCOVERY_PORT}`);
      });
    } catch (error) {
      console.error('Error setting up HTTP discovery server:', error);
    }
  }

  /**
   * Handle incoming discovery messages
   */
  handleDiscoveryMessage(message) {
    try {
      if (message.type === 'CLIPSY_DISCOVERY' && message.deviceId !== this.deviceId) {
        const device = {
          id: message.deviceId,
          name: message.deviceName,
          ip: message.ip,
          port: message.port,
          type: message.deviceType,
          lastSeen: new Date().toISOString(),
          isReachable: true,
          services: ['clipboard', 'sync'],
          capabilities: message.capabilities,
          version: message.version,
          platform: message.deviceType
        };

        this.discoveredDevices.set(device.id, device);
        
        if (this.callbacks?.onDeviceFound) {
          this.callbacks.onDeviceFound(device);
        }

        console.log('Discovered device via broadcast:', device.name);
      }
    } catch (error) {
      console.error('Error handling discovery message:', error);
    }
  }

  /**
   * Start network discovery and device broadcasting
   */
  async startDiscovery() {
    if (this.isDiscovering) {
      console.log('Discovery already running');
      return;
    }

    this.isDiscovering = true;
    console.log('Starting network discovery and broadcasting...');

    // Initialize current device if not done
    if (!this.currentDeviceInfo) {
      await this.initializeCurrentDevice();
    }

    // Start device broadcasting
    this.startBroadcasting();

    // Initial discovery
    await this.performDiscovery();

    // Set up periodic discovery
    this.discoveryInterval = setInterval(async () => {
      await this.performDiscovery();
    }, this.DISCOVERY_INTERVAL);

    console.log('Network discovery and broadcasting started');
  }

  /**
   * Stop network discovery and broadcasting
   */
  stopDiscovery() {
    if (this.discoveryInterval) {
      clearInterval(this.discoveryInterval);
      this.discoveryInterval = null;
    }

    this.stopBroadcasting();

    if (this.httpServer) {
      this.httpServer.close();
      this.httpServer = null;
    }

    this.isDiscovering = false;
    console.log('Network discovery and broadcasting stopped');
  }

  /**
   * Start device broadcasting to make this device discoverable
   */
  startBroadcasting() {
    if (this.isBroadcasting) {
      console.log('Broadcasting already running');
      return;
    }

    this.isBroadcasting = true;
    console.log('Starting device broadcasting...');

    // Setup UDP socket for broadcasting
    this.setupUdpBroadcast();

    // Initial broadcast
    this.broadcastDevicePresence();

    // Set up periodic broadcasting
    this.broadcastInterval = setInterval(() => {
      this.broadcastDevicePresence();
    }, this.BROADCAST_INTERVAL);

    console.log('Device broadcasting started');
  }

  /**
   * Stop device broadcasting
   */
  stopBroadcasting() {
    if (this.broadcastInterval) {
      clearInterval(this.broadcastInterval);
      this.broadcastInterval = null;
    }

    if (this.udpSocket) {
      this.udpSocket.close();
      this.udpSocket = null;
    }

    this.isBroadcasting = false;
    console.log('Device broadcasting stopped');
  }

  /**
   * Setup UDP socket for broadcasting
   */
  setupUdpBroadcast() {
    try {
      this.udpSocket = dgram.createSocket('udp4');
      this.udpSocket.bind(() => {
        this.udpSocket.setBroadcast(true);
      });
    } catch (error) {
      console.error('Error setting up UDP broadcast:', error);
    }
  }

  /**
   * Broadcast device presence to make this device discoverable
   */
  async broadcastDevicePresence() {
    if (!this.currentDeviceInfo || !this.currentNetworkInfo?.isConnected) {
      console.log('Cannot broadcast: device info or network not available');
      return;
    }

    try {
      const broadcastMessage = {
        type: 'CLIPSY_DISCOVERY',
        deviceId: this.currentDeviceInfo.id,
        deviceName: this.currentDeviceInfo.name,
        deviceType: 'windows',
        ip: this.currentDeviceInfo.ip,
        port: this.currentDeviceInfo.port,
        version: this.currentDeviceInfo.version,
        capabilities: this.currentDeviceInfo.capabilities,
        timestamp: Date.now()
      };

      // UDP broadcast
      await this.udpBroadcast(broadcastMessage);

      // HTTP broadcast as fallback
      await this.httpBroadcast(broadcastMessage);

    } catch (error) {
      console.error('Error broadcasting device presence:', error);
    }
  }

  /**
   * UDP broadcast implementation
   */
  async udpBroadcast(message) {
    if (!this.udpSocket || !this.currentNetworkInfo?.localIP) return;

    try {
      const messageBuffer = Buffer.from(JSON.stringify(message));
      const localIP = this.currentNetworkInfo.localIP;
      const ipParts = localIP.split('.');
      const broadcastIP = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}.255`;

      this.udpSocket.send(messageBuffer, this.BROADCAST_PORT, broadcastIP, (error) => {
        if (error) {
          console.error('UDP broadcast error:', error);
        } else {
          console.log(`UDP broadcast sent to ${broadcastIP}:${this.BROADCAST_PORT}`);
        }
      });
    } catch (error) {
      console.error('Error in UDP broadcast:', error);
    }
  }

  /**
   * HTTP-based broadcasting as fallback
   */
  async httpBroadcast(message) {
    if (!this.currentNetworkInfo?.localIP) return;

    const localIP = this.currentNetworkInfo.localIP;
    const ipParts = localIP.split('.');
    const baseIP = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}`;

    // Broadcast to common discovery endpoints in the network
    const broadcastPromises = [];

    for (let i = 1; i <= 254; i++) {
      const targetIP = `${baseIP}.${i}`;
      if (targetIP === localIP) continue; // Skip self

      broadcastPromises.push(this.sendBroadcastToIP(targetIP, message));
    }

    // Execute broadcasts with limited concurrency
    const chunks = this.chunkArray(broadcastPromises, 20);
    for (const chunk of chunks) {
      await Promise.allSettled(chunk);
    }
  }

  /**
   * Send broadcast message to specific IP
   */
  async sendBroadcastToIP(ip, message) {
    try {
      const postData = JSON.stringify(message);
      const options = {
        hostname: ip,
        port: this.BROADCAST_PORT,
        path: '/clipsy/discovery',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        },
        timeout: 1000
      };

      return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
          resolve();
        });

        req.on('error', () => {
          // Silently ignore errors (expected for non-Clipsy devices)
          resolve();
        });

        req.on('timeout', () => {
          req.destroy();
          resolve();
        });

        req.write(postData);
        req.end();
      });
    } catch (error) {
      // Silently ignore broadcast failures
    }
  }

  /**
   * Perform network discovery scan
   */
  async performDiscovery() {
    try {
      console.log('Performing network discovery scan...');

      // Update network info
      await this.detectNetworkInfo();

      if (!this.currentNetworkInfo?.isConnected) {
        console.log('No network connection, skipping discovery');
        return;
      }

      // Get local IP and network range
      const localIP = this.currentNetworkInfo.localIP;
      if (!localIP) {
        console.log('Could not determine local IP, skipping discovery');
        return;
      }

      // Scan network range for Clipsy services
      await this.scanNetworkRange(localIP);

      // Clean up old devices
      this.cleanupOldDevices();

      // Notify discovery complete
      if (this.callbacks?.onDiscoveryComplete) {
        this.callbacks.onDiscoveryComplete(Array.from(this.discoveredDevices.values()));
      }

    } catch (error) {
      console.error('Error during discovery:', error);
      if (this.callbacks?.onError) {
        this.callbacks.onError(`Discovery error: ${error.message}`);
      }
    }
  }

  /**
   * Scan network range for Clipsy devices
   */
  async scanNetworkRange(localIP) {
    const ipParts = localIP.split('.');
    const baseIP = `${ipParts[0]}.${ipParts[1]}.${ipParts[2]}`;

    console.log(`Scanning network range: ${baseIP}.1-254`);

    const scanPromises = [];

    // Scan all IPs in the range
    for (let i = 1; i <= 254; i++) {
      const targetIP = `${baseIP}.${i}`;
      if (targetIP === localIP) continue; // Skip self

      // Scan all Clipsy ports for each IP
      for (const port of this.CLIPSY_PORTS) {
        scanPromises.push(this.scanDevice(targetIP, port));
      }
    }

    // Execute scans with limited concurrency
    const chunks = this.chunkArray(scanPromises, 50);
    for (const chunk of chunks) {
      await Promise.allSettled(chunk);
    }
  }

  /**
   * Scan individual device
   */
  async scanDevice(ip, port) {
    try {
      const response = await this.httpRequest(`http://${ip}:${port}/clipsy/info`, 2000);

      if (response) {
        const deviceInfo = JSON.parse(response);
        const device = {
          id: deviceInfo.id || `${ip}:${port}`,
          name: deviceInfo.name || `Clipsy Device (${ip})`,
          ip,
          port,
          type: deviceInfo.type || 'unknown',
          lastSeen: new Date().toISOString(),
          isReachable: true,
          services: deviceInfo.services || ['clipboard'],
          capabilities: deviceInfo.capabilities || this.getDefaultCapabilities(),
          version: deviceInfo.version || '1.0.0',
          platform: deviceInfo.platform || 'unknown'
        };

        this.discoveredDevices.set(device.id, device);

        if (this.callbacks?.onDeviceFound) {
          this.callbacks.onDeviceFound(device);
        }

        console.log(`Discovered Clipsy device: ${device.name} at ${ip}:${port}`);
      }
    } catch (error) {
      // Silently ignore scan failures (expected for non-Clipsy devices)
    }
  }

  /**
   * Make HTTP request with timeout
   */
  httpRequest(url, timeout = 3000) {
    return new Promise((resolve, reject) => {
      const req = http.get(url, { timeout }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve(data));
      });

      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  /**
   * Get default device capabilities
   */
  getDefaultCapabilities() {
    return {
      supportsClipboard: true,
      supportsFiles: false,
      supportsImages: true,
      supportsRichText: false,
      supportsEncryption: false,
      maxClipboardSize: 1024 * 1024,
      supportedFormats: ['text/plain']
    };
  }

  /**
   * Clean up old devices that haven't been seen recently
   */
  cleanupOldDevices() {
    const now = Date.now();
    const devicesToRemove = [];

    for (const [deviceId, device] of this.discoveredDevices) {
      const lastSeen = new Date(device.lastSeen).getTime();
      if (now - lastSeen > this.DEVICE_TIMEOUT) {
        devicesToRemove.push(deviceId);
      }
    }

    for (const deviceId of devicesToRemove) {
      this.discoveredDevices.delete(deviceId);
      if (this.callbacks?.onDeviceLost) {
        this.callbacks.onDeviceLost(deviceId);
      }
      console.log(`Removed stale device: ${deviceId}`);
    }
  }

  /**
   * Utility function to chunk array into smaller arrays
   */
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Get all discovered devices
   */
  getDiscoveredDevices() {
    return Array.from(this.discoveredDevices.values());
  }

  /**
   * Get current device info
   */
  getCurrentDevice() {
    return this.currentDeviceInfo;
  }

  /**
   * Get network info
   */
  getNetworkInfo() {
    return this.currentNetworkInfo;
  }
}

module.exports = NetworkDiscoveryService;
