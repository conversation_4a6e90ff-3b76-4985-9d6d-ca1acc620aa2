{"name": "clipsy-windows", "version": "1.0.0", "description": "Clipsy Windows Application", "main": "electron.js", "homepage": "./", "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-qr-code": "^2.0.16", "electron-is-dev": "^3.0.1"}, "devDependencies": {"electron": "^37.1.0", "electron-builder": "^26.0.12", "react-scripts": "^5.0.1", "concurrently": "^9.2.0", "wait-on": "^8.0.3", "@types/plist": "^3.0.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "build-win": "npm run build && electron-builder --win", "build-win-portable": "npm run build && electron-builder --win portable", "pack": "electron-builder --dir"}, "keywords": [], "author": "", "license": "ISC", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.clipsy.windows", "productName": "<PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["build/**/*", "electron.js", "preload.js"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "public/clipsy-logo-no-bg.png", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "<PERSON><PERSON><PERSON>"}, "portable": {"artifactName": "${productName}-${version}-portable-${arch}.${ext}"}}}