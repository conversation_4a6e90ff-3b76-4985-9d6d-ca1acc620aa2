{"name": "clipsy-windows", "version": "1.0.0", "description": "Clipsy Windows Application", "main": "electron.js", "homepage": "./", "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-qr-code": "^2.0.16", "electron-is-dev": "^3.0.1"}, "devDependencies": {"electron": "^37.1.0", "electron-builder": "^26.0.12", "react-scripts": "^5.0.1", "concurrently": "^9.2.0", "wait-on": "^8.0.3", "@types/plist": "^3.0.5"}, "oldDependencies": {"7zip-bin": "^5.2.0", "abab": "^2.0.6", "abbrev": "^1.1.1", "accepts": "^1.3.8", "acorn": "^8.15.0", "acorn-globals": "^6.0.0", "acorn-jsx": "^5.3.2", "acorn-walk": "^7.2.0", "address": "^1.2.2", "adjust-sourcemap-loader": "^4.0.0", "agent-base": "^7.1.3", "agentkeepalive": "^4.6.0", "aggregate-error": "^3.1.0", "ajv": "^6.12.6", "ajv-formats": "^2.1.1", "ajv-keywords": "^3.5.2", "ansi-escapes": "^4.3.2", "ansi-html": "^0.0.9", "ansi-html-community": "^0.0.8", "ansi-regex": "^5.0.1", "ansi-styles": "^4.3.0", "any-promise": "^1.3.0", "anymatch": "^3.1.3", "app-builder-bin": "^5.0.0-alpha.12", "app-builder-lib": "^26.0.12", "arg": "^5.0.2", "argparse": "^2.0.1", "aria-query": "^5.3.2", "array-buffer-byte-length": "^1.0.2", "array-flatten": "^1.1.1", "array-includes": "^3.1.9", "array-union": "^2.1.0", "array.prototype.findlast": "^1.2.5", "array.prototype.findlastindex": "^1.2.6", "array.prototype.flat": "^1.3.3", "array.prototype.flatmap": "^1.3.3", "array.prototype.reduce": "^1.0.8", "array.prototype.tosorted": "^1.1.4", "arraybuffer.prototype.slice": "^1.0.4", "asap": "^2.0.6", "ast-types-flow": "^0.0.8", "async": "^3.2.6", "async-exit-hook": "^2.0.1", "async-function": "^1.0.0", "asynckit": "^0.4.0", "at-least-node": "^1.0.0", "autoprefixer": "^10.4.21", "available-typed-arrays": "^1.0.7", "axe-core": "^4.10.3", "axios": "^1.10.0", "axobject-query": "^4.1.0", "babel-jest": "^27.5.1", "babel-loader": "^8.4.1", "babel-plugin-istanbul": "^6.1.1", "babel-plugin-jest-hoist": "^27.5.1", "babel-plugin-macros": "^3.1.0", "babel-plugin-named-asset-import": "^0.3.8", "babel-plugin-polyfill-corejs2": "^0.4.13", "babel-plugin-polyfill-corejs3": "^0.11.1", "babel-plugin-polyfill-regenerator": "^0.6.4", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "babel-preset-current-node-syntax": "^1.1.0", "babel-preset-jest": "^27.5.1", "babel-preset-react-app": "^10.1.0", "balanced-match": "^1.0.2", "base64-js": "^1.5.1", "batch": "^0.6.1", "bfj": "^7.1.0", "big.js": "^5.2.2", "binary-extensions": "^2.3.0", "bl": "^4.1.0", "bluebird": "^3.7.2", "body-parser": "^1.20.3", "bonjour-service": "^1.3.0", "boolbase": "^1.0.0", "boolean": "^3.2.0", "brace-expansion": "^1.1.12", "braces": "^3.0.3", "browser-process-hrtime": "^1.0.0", "browserslist": "^4.25.1", "bser": "^2.1.1", "buffer": "^5.7.1", "buffer-crc32": "^0.2.13", "buffer-from": "^1.1.2", "builder-util": "^26.0.11", "builder-util-runtime": "^9.3.1", "builtin-modules": "^3.3.0", "bundle-name": "^4.1.0", "bytes": "^3.1.2", "cacache": "^16.1.3", "cacheable-lookup": "^5.0.4", "cacheable-request": "^7.0.4", "call-bind": "^1.0.8", "call-bind-apply-helpers": "^1.0.2", "call-bound": "^1.0.4", "callsites": "^3.1.0", "camel-case": "^4.1.2", "camelcase": "^6.3.0", "camelcase-css": "^2.0.1", "caniuse-api": "^3.0.0", "caniuse-lite": "^1.0.30001726", "case-sensitive-paths-webpack-plugin": "^2.4.0", "chalk": "^4.1.2", "char-regex": "^1.0.2", "check-types": "^11.2.3", "chokidar": "^3.6.0", "chownr": "^2.0.0", "chrome-trace-event": "^1.0.4", "chromium-pickle-js": "^0.2.0", "ci-info": "^3.9.0", "cjs-module-lexer": "^1.4.3", "clean-css": "^5.3.3", "clean-stack": "^2.2.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.9.2", "cliui": "^8.0.1", "clone": "^1.0.4", "clone-deep": "^4.0.1", "clone-response": "^1.0.3", "co": "^4.6.0", "coa": "^2.0.2", "collect-v8-coverage": "^1.0.2", "color-convert": "^2.0.1", "color-name": "^1.1.4", "colord": "^2.9.3", "colorette": "^2.0.20", "combined-stream": "^1.0.8", "commander": "^5.1.0", "common-tags": "^1.8.2", "commondir": "^1.0.1", "compare-version": "^0.1.2", "compressible": "^2.0.18", "compression": "^1.8.0", "concat-map": "^0.0.1", "concurrently": "^9.2.0", "config-file-ts": "^0.2.8-rc1", "confusing-browser-globals": "^1.0.11", "connect-history-api-fallback": "^2.0.0", "content-disposition": "^0.5.4", "content-type": "^1.0.5", "convert-source-map": "^2.0.0", "cookie": "^0.7.1", "cookie-signature": "^1.0.6", "core-js": "^3.43.0", "core-js-compat": "^3.43.0", "core-js-pure": "^3.43.0", "cosmiconfig": "^7.1.0", "cross-spawn": "^7.0.6", "crypto-random-string": "^2.0.0", "css-blank-pseudo": "^3.0.3", "css-declaration-sorter": "^6.4.1", "css-has-pseudo": "^3.0.4", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^3.4.1", "css-prefers-color-scheme": "^6.0.3", "css-select": "^4.3.0", "css-select-base-adapter": "^0.1.1", "css-tree": "^1.0.0-alpha.37", "css-what": "^6.1.0", "cssdb": "^7.11.2", "cssesc": "^3.0.0", "cssnano": "^5.1.15", "cssnano-preset-default": "^5.2.14", "cssnano-utils": "^3.1.0", "csso": "^4.2.0", "cssom": "^0.4.4", "cssstyle": "^2.3.0", "csstype": "^3.1.3", "damerau-levenshtein": "^1.0.8", "data-urls": "^2.0.0", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "debug": "^4.4.1", "decamelize": "^1.2.0", "decimal.js": "^10.5.0", "decompress-response": "^6.0.0", "dedent": "^0.7.0", "deep-is": "^0.1.4", "deepmerge": "^4.3.1", "default-browser": "^5.2.1", "default-browser-id": "^5.0.0", "default-gateway": "^6.0.3", "defaults": "^1.0.4", "defer-to-connect": "^2.0.1", "define-data-property": "^1.1.4", "define-lazy-prop": "^2.0.0", "define-properties": "^1.2.1", "delayed-stream": "^1.0.0", "depd": "^2.0.0", "destroy": "^1.2.0", "detect-libc": "^2.0.4", "detect-newline": "^3.1.0", "detect-node": "^2.1.0", "detect-port-alt": "^1.1.6", "didyoumean": "^1.2.2", "diff-sequences": "^27.5.1", "dijkstrajs": "^1.0.3", "dir-compare": "^4.2.0", "dir-glob": "^3.0.1", "dlv": "^1.1.3", "dmg-builder": "^26.0.12", "dns-packet": "^5.6.1", "doctrine": "^3.0.0", "dom-converter": "^0.2.0", "dom-serializer": "^1.4.1", "domelementtype": "^2.3.0", "domexception": "^2.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "dot-case": "^3.0.4", "dotenv": "^16.5.0", "dotenv-expand": "^11.0.7", "dunder-proto": "^1.0.1", "duplexer": "^0.1.2", "eastasianwidth": "^0.2.0", "ee-first": "^1.1.1", "ejs": "^3.1.10", "electron-publish": "^26.0.11", "electron-to-chromium": "^1.5.176", "emittery": "^0.8.1", "emoji-regex": "^8.0.0", "emojis-list": "^3.0.0", "encodeurl": "^2.0.0", "encoding": "^0.1.13", "end-of-stream": "^1.4.5", "enhanced-resolve": "^5.18.2", "entities": "^2.2.0", "env-paths": "^2.2.1", "envinfo": "^7.14.0", "err-code": "^2.0.3", "error-ex": "^1.3.2", "error-stack-parser": "^2.1.4", "es-abstract": "^1.24.0", "es-array-method-boxes-properly": "^1.0.0", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-iterator-helpers": "^1.2.1", "es-module-lexer": "^1.7.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "es-shim-unscopables": "^1.1.0", "es-to-primitive": "^1.3.0", "es6-error": "^4.1.1", "escalade": "^3.2.0", "escape-html": "^1.0.3", "escape-string-regexp": "^4.0.0", "escodegen": "^2.1.0", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.12.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^25.7.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-testing-library": "^5.11.1", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "eslint-webpack-plugin": "^3.2.0", "espree": "^9.6.1", "esprima": "^4.0.1", "esquery": "^1.6.0", "esrecurse": "^4.3.0", "estraverse": "^5.3.0", "estree-walker": "^1.0.1", "esutils": "^2.0.3", "etag": "^1.8.1", "eventemitter3": "^4.0.7", "events": "^3.3.0", "execa": "^5.1.1", "exit": "^0.1.2", "expect": "^27.5.1", "exponential-backoff": "^3.1.2", "express": "^4.21.2", "extract-zip": "^2.0.1", "fast-deep-equal": "^3.1.3", "fast-glob": "^3.3.3", "fast-json-stable-stringify": "^2.1.0", "fast-levenshtein": "^2.0.6", "fast-uri": "^3.0.6", "fastest-levenshtein": "^1.0.16", "fastq": "^1.19.1", "faye-websocket": "^0.11.4", "fb-watchman": "^2.0.2", "fd-slicer": "^1.1.0", "file-entry-cache": "^6.0.1", "file-loader": "^6.2.0", "filelist": "^1.0.4", "filesize": "^8.0.7", "fill-range": "^7.1.1", "finalhandler": "^1.3.1", "find-cache-dir": "^3.3.2", "find-up": "^4.1.0", "flat": "^5.0.2", "flat-cache": "^3.2.0", "flatted": "^3.3.3", "follow-redirects": "^1.15.9", "for-each": "^0.3.5", "foreground-child": "^3.3.1", "fork-ts-checker-webpack-plugin": "^6.5.3", "form-data": "^4.0.3", "forwarded": "^0.2.0", "fraction.js": "^4.3.7", "fresh": "^0.5.2", "fs-extra": "^8.1.0", "fs-minipass": "^2.1.0", "fs-monkey": "^1.0.6", "fs.realpath": "^1.0.0", "function-bind": "^1.1.2", "function.prototype.name": "^1.1.8", "functions-have-names": "^1.2.3", "gensync": "^1.0.0-beta.2", "get-caller-file": "^2.0.5", "get-intrinsic": "^1.3.0", "get-own-enumerable-property-symbols": "^3.0.2", "get-package-type": "^0.1.0", "get-proto": "^1.0.1", "get-stream": "^5.2.0", "get-symbol-description": "^1.1.0", "glob": "^7.2.3", "glob-parent": "^6.0.2", "glob-to-regexp": "^0.4.1", "global-agent": "^3.0.0", "global-modules": "^2.0.0", "global-prefix": "^3.0.0", "globals": "^11.12.0", "globalthis": "^1.0.4", "globby": "^11.1.0", "gopd": "^1.2.0", "got": "^11.8.6", "graceful-fs": "^4.2.11", "graphemer": "^1.4.0", "gzip-size": "^6.0.0", "handle-thing": "^2.0.1", "harmony-reflect": "^1.6.2", "has-bigints": "^1.1.0", "has-flag": "^4.0.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2", "he": "^1.2.0", "hoopy": "^0.1.4", "hosted-git-info": "^4.1.0", "hpack.js": "^2.1.6", "html-encoding-sniffer": "^2.0.1", "html-entities": "^2.6.0", "html-escaper": "^2.0.2", "html-minifier-terser": "^6.1.0", "html-webpack-plugin": "^5.6.3", "htmlparser2": "^6.1.0", "http-cache-semantics": "^4.2.0", "http-deceiver": "^1.2.7", "http-errors": "^2.0.0", "http-parser-js": "^0.5.10", "http-proxy": "^1.18.1", "http-proxy-agent": "^7.0.2", "http-proxy-middleware": "^2.0.9", "http2-wrapper": "^1.0.3", "https-proxy-agent": "^7.0.6", "human-signals": "^2.1.0", "humanize-ms": "^1.2.1", "hyperdyperid": "^1.2.0", "iconv-lite": "^0.6.3", "icss-utils": "^5.1.0", "idb": "^7.1.1", "identity-obj-proxy": "^3.0.0", "ieee754": "^1.2.1", "ignore": "^5.3.2", "immer": "^9.0.21", "import-fresh": "^3.3.1", "import-local": "^3.2.0", "imurmurhash": "^0.1.4", "indent-string": "^4.0.0", "infer-owner": "^1.0.4", "inflight": "^1.0.6", "inherits": "^2.0.4", "ini": "^1.3.8", "internal-slot": "^1.1.0", "interpret": "^3.1.1", "ip-address": "^9.0.5", "ipaddr.js": "^2.2.0", "is-array-buffer": "^3.0.5", "is-arrayish": "^0.2.1", "is-async-function": "^2.1.1", "is-bigint": "^1.1.0", "is-binary-path": "^2.1.0", "is-boolean-object": "^1.2.2", "is-callable": "^1.2.7", "is-ci": "^3.0.1", "is-core-module": "^2.16.1", "is-data-view": "^1.0.2", "is-date-object": "^1.1.0", "is-docker": "^2.2.1", "is-extglob": "^2.1.1", "is-finalizationregistry": "^1.1.1", "is-fullwidth-code-point": "^3.0.0", "is-generator-fn": "^2.1.0", "is-generator-function": "^1.1.0", "is-glob": "^4.0.3", "is-inside-container": "^1.0.0", "is-interactive": "^1.0.0", "is-lambda": "^1.0.1", "is-map": "^2.0.3", "is-module": "^1.0.0", "is-negative-zero": "^2.0.3", "is-network-error": "^1.1.0", "is-number": "^7.0.0", "is-number-object": "^1.1.1", "is-obj": "^1.0.1", "is-path-inside": "^3.0.3", "is-plain-obj": "^3.0.0", "is-plain-object": "^2.0.4", "is-potential-custom-element-name": "^1.0.1", "is-regex": "^1.2.1", "is-regexp": "^1.0.0", "is-root": "^2.1.0", "is-set": "^2.0.3", "is-shared-array-buffer": "^1.0.4", "is-stream": "^2.0.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1", "is-typed-array": "^1.1.15", "is-typedarray": "^1.0.0", "is-unicode-supported": "^0.1.0", "is-weakmap": "^2.0.2", "is-weakref": "^1.1.1", "is-weakset": "^2.0.4", "is-wsl": "^2.2.0", "isarray": "^2.0.5", "isbinaryfile": "^5.0.4", "isexe": "^2.0.0", "isobject": "^3.0.1", "istanbul-lib-coverage": "^3.2.2", "istanbul-lib-instrument": "^5.2.1", "istanbul-lib-report": "^3.0.1", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.1.7", "iterator.prototype": "^1.1.5", "jackspeak": "^3.4.3", "jake": "^10.9.2", "jest-changed-files": "^27.5.1", "jest-circus": "^27.5.1", "jest-cli": "^27.5.1", "jest-config": "^27.5.1", "jest-diff": "^27.5.1", "jest-docblock": "^27.5.1", "jest-each": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "jest-environment-node": "^27.5.1", "jest-get-type": "^27.5.1", "jest-haste-map": "^27.5.1", "jest-jasmine2": "^27.5.1", "jest-leak-detector": "^27.5.1", "jest-matcher-utils": "^27.5.1", "jest-message-util": "^27.5.1", "jest-mock": "^27.5.1", "jest-pnp-resolver": "^1.2.3", "jest-regex-util": "^27.5.1", "jest-resolve": "^27.5.1", "jest-resolve-dependencies": "^27.5.1", "jest-runner": "^27.5.1", "jest-runtime": "^27.5.1", "jest-serializer": "^27.5.1", "jest-snapshot": "^27.5.1", "jest-util": "^27.5.1", "jest-validate": "^27.5.1", "jest-watch-typeahead": "^1.1.0", "jest-watcher": "^27.5.1", "jest-worker": "^27.5.1", "jiti": "^1.21.7", "joi": "^17.13.3", "js-tokens": "^4.0.0", "js-yaml": "^4.1.0", "jsbn": "^1.1.0", "jsdom": "^16.7.0", "jsesc": "^3.1.0", "json-buffer": "^3.0.1", "json-parse-even-better-errors": "^2.3.1", "json-schema": "^0.4.0", "json-schema-traverse": "^0.4.1", "json-stable-stringify-without-jsonify": "^1.0.1", "json-stringify-safe": "^5.0.1", "json5": "^2.2.3", "jsonfile": "^4.0.0", "jsonpath": "^1.1.1", "jsonpointer": "^5.0.1", "jsx-ast-utils": "^3.3.5", "keyv": "^4.5.4", "kind-of": "^6.0.3", "kleur": "^3.0.3", "klona": "^2.0.6", "language-subtag-registry": "^0.3.23", "language-tags": "^1.0.9", "launch-editor": "^2.10.0", "lazy-val": "^1.0.5", "leven": "^3.1.0", "levn": "^0.4.1", "lilconfig": "^2.1.0", "lines-and-columns": "^1.2.4", "loader-runner": "^4.3.0", "loader-utils": "^2.0.4", "locate-path": "^5.0.0", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lodash.memoize": "^4.1.2", "lodash.merge": "^4.6.2", "lodash.sortby": "^4.7.0", "lodash.uniq": "^4.5.0", "log-symbols": "^4.1.0", "loose-envify": "^1.4.0", "lower-case": "^2.0.2", "lowercase-keys": "^2.0.0", "lru-cache": "^6.0.0", "magic-string": "^0.25.9", "make-dir": "^3.1.0", "make-fetch-happen": "^10.2.1", "makeerror": "^1.0.12", "matcher": "^3.0.0", "math-intrinsics": "^1.1.0", "mdn-data": "^2.0.4", "media-typer": "^0.3.0", "memfs": "^3.5.3", "merge-descriptors": "^1.0.3", "merge-stream": "^2.0.0", "merge2": "^1.4.1", "methods": "^1.1.2", "micromatch": "^4.0.8", "mime": "^2.6.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "mimic-fn": "^2.1.0", "mimic-response": "^1.0.1", "mini-css-extract-plugin": "^2.9.2", "minimalistic-assert": "^1.0.1", "minimatch": "^10.0.3", "minimist": "^1.2.8", "minipass": "^3.3.6", "minipass-collect": "^1.0.2", "minipass-fetch": "^2.1.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "minipass-sized": "^1.0.3", "minizlib": "^2.1.2", "mkdirp": "^1.0.4", "ms": "^2.1.3", "multicast-dns": "^7.2.5", "mz": "^2.7.0", "nanoid": "^3.3.11", "natural-compare": "^1.4.0", "natural-compare-lite": "^1.4.0", "negotiator": "^0.6.4", "neo-async": "^2.6.2", "no-case": "^3.0.4", "node-abi": "^3.75.0", "node-api-version": "^0.2.1", "node-forge": "^1.3.1", "node-int64": "^0.4.0", "node-releases": "^2.0.19", "nopt": "^6.0.0", "normalize-path": "^3.0.0", "normalize-range": "^0.1.2", "normalize-url": "^6.1.0", "npm-run-path": "^4.0.1", "nth-check": "^2.1.1", "nwsapi": "^2.2.20", "object-assign": "^4.1.1", "object-hash": "^3.0.0", "object-inspect": "^1.13.4", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "object.entries": "^1.1.9", "object.fromentries": "^2.0.8", "object.getownpropertydescriptors": "^2.1.8", "object.groupby": "^1.0.3", "object.values": "^1.2.1", "obuf": "^1.1.2", "on-finished": "^2.4.1", "on-headers": "^1.0.2", "once": "^1.4.0", "onetime": "^5.1.2", "open": "^8.4.2", "optionator": "^0.9.4", "ora": "^5.4.1", "own-keys": "^1.0.1", "p-cancelable": "^2.1.1", "p-limit": "^3.1.0", "p-locate": "^4.1.0", "p-map": "^4.0.0", "p-retry": "^6.2.1", "p-try": "^2.2.0", "package-json-from-dist": "^1.0.1", "param-case": "^3.0.4", "parent-module": "^1.0.1", "parse-json": "^5.2.0", "parse5": "^6.0.1", "parseurl": "^1.3.3", "pascal-case": "^3.1.2", "path-exists": "^4.0.0", "path-is-absolute": "^1.0.1", "path-key": "^3.1.1", "path-parse": "^1.0.7", "path-scurry": "^1.11.1", "path-to-regexp": "^0.1.12", "path-type": "^4.0.0", "pe-library": "^0.4.1", "pend": "^1.2.0", "performance-now": "^2.1.0", "picocolors": "^1.1.1", "picomatch": "^2.3.1", "pify": "^2.3.0", "pirates": "^4.0.7", "pkg-dir": "^4.2.0", "pkg-up": "^3.1.0", "plist": "^3.1.0", "pngjs": "^5.0.0", "possible-typed-array-names": "^1.1.0", "postcss": "^8.5.6", "postcss-attribute-case-insensitive": "^5.0.2", "postcss-browser-comments": "^4.0.0", "postcss-calc": "^8.2.4", "postcss-clamp": "^4.1.0", "postcss-color-functional-notation": "^4.2.4", "postcss-color-hex-alpha": "^8.0.4", "postcss-color-rebeccapurple": "^7.1.1", "postcss-colormin": "^5.3.1", "postcss-convert-values": "^5.1.3", "postcss-custom-media": "^8.0.2", "postcss-custom-properties": "^12.1.11", "postcss-custom-selectors": "^6.0.3", "postcss-dir-pseudo-class": "^6.0.5", "postcss-discard-comments": "^5.1.2", "postcss-discard-duplicates": "^5.1.0", "postcss-discard-empty": "^5.1.1", "postcss-discard-overridden": "^5.1.0", "postcss-double-position-gradients": "^3.1.2", "postcss-env-function": "^4.0.6", "postcss-flexbugs-fixes": "^5.0.2", "postcss-focus-visible": "^6.0.4", "postcss-focus-within": "^5.0.4", "postcss-font-variant": "^5.0.0", "postcss-gap-properties": "^3.0.5", "postcss-image-set-function": "^4.0.7", "postcss-import": "^15.1.0", "postcss-initial": "^4.0.1", "postcss-js": "^4.0.1", "postcss-lab-function": "^4.2.1", "postcss-load-config": "^4.0.2", "postcss-loader": "^6.2.1", "postcss-logical": "^5.0.4", "postcss-media-minmax": "^5.0.0", "postcss-merge-longhand": "^5.1.7", "postcss-merge-rules": "^5.1.4", "postcss-minify-font-values": "^5.1.0", "postcss-minify-gradients": "^5.1.1", "postcss-minify-params": "^5.1.4", "postcss-minify-selectors": "^5.2.1", "postcss-modules-extract-imports": "^3.1.0", "postcss-modules-local-by-default": "^4.2.0", "postcss-modules-scope": "^3.2.1", "postcss-modules-values": "^4.0.0", "postcss-nested": "^6.2.0", "postcss-nesting": "^10.2.0", "postcss-normalize": "^10.0.1", "postcss-normalize-charset": "^5.1.0", "postcss-normalize-display-values": "^5.1.0", "postcss-normalize-positions": "^5.1.1", "postcss-normalize-repeat-style": "^5.1.1", "postcss-normalize-string": "^5.1.0", "postcss-normalize-timing-functions": "^5.1.0", "postcss-normalize-unicode": "^5.1.1", "postcss-normalize-url": "^5.1.0", "postcss-normalize-whitespace": "^5.1.1", "postcss-opacity-percentage": "^1.1.3", "postcss-ordered-values": "^5.1.3", "postcss-overflow-shorthand": "^3.0.4", "postcss-page-break": "^3.0.4", "postcss-place": "^7.0.5", "postcss-preset-env": "^7.8.3", "postcss-pseudo-class-any-link": "^7.1.6", "postcss-reduce-initial": "^5.1.2", "postcss-reduce-transforms": "^5.1.0", "postcss-replace-overflow-wrap": "^4.0.0", "postcss-selector-not": "^6.0.1", "postcss-selector-parser": "^7.1.0", "postcss-svgo": "^5.1.0", "postcss-unique-selectors": "^5.1.1", "postcss-value-parser": "^4.2.0", "prelude-ls": "^1.2.1", "pretty-bytes": "^5.6.0", "pretty-error": "^4.0.0", "pretty-format": "^27.5.1", "proc-log": "^2.0.1", "process-nextick-args": "^2.0.1", "progress": "^2.0.3", "promise": "^8.3.0", "promise-inflight": "^1.0.1", "promise-retry": "^2.0.1", "prompts": "^2.4.2", "prop-types": "^15.8.1", "proxy-addr": "^2.0.7", "proxy-from-env": "^1.1.0", "psl": "^1.15.0", "pump": "^3.0.3", "punycode": "^2.3.1", "q": "^1.5.1", "qr.js": "^0.0.0", "qrcode": "^1.5.4", "qs": "^6.13.0", "querystringify": "^2.2.0", "queue-microtask": "^1.2.3", "quick-lru": "^5.1.1", "raf": "^3.4.1", "randombytes": "^2.1.0", "range-parser": "^1.2.1", "raw-body": "^2.5.2", "react": "^19.1.0", "react-app-polyfill": "^3.0.0", "react-dev-utils": "^12.0.1", "react-dom": "^19.1.0", "react-error-overlay": "^6.1.0", "react-is": "^16.13.1", "react-qr-code": "^2.0.16", "react-refresh": "^0.11.0", "react-scripts": "^5.0.1", "read-binary-file-arch": "^1.0.6", "read-cache": "^1.0.0", "readable-stream": "^3.6.2", "readdirp": "^3.6.0", "rechoir": "^0.8.0", "recursive-readdir": "^2.2.3", "reflect.getprototypeof": "^1.0.10", "regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regenerator-runtime": "^0.13.11", "regex-parser": "^2.3.1", "regexp.prototype.flags": "^1.5.4", "regexpu-core": "^6.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "relateurl": "^0.2.7", "renderkid": "^3.0.0", "require-directory": "^2.1.1", "require-from-string": "^2.0.2", "require-main-filename": "^2.0.0", "requires-port": "^1.0.0", "resedit": "^1.7.2", "resolve": "^1.22.10", "resolve-alpn": "^1.2.1", "resolve-cwd": "^3.0.0", "resolve-from": "^5.0.0", "resolve-url-loader": "^4.0.0", "resolve.exports": "^1.1.1", "responselike": "^2.0.1", "restore-cursor": "^3.1.0", "retry": "^0.12.0", "reusify": "^1.1.0", "rimraf": "^3.0.2", "roarr": "^2.15.4", "rollup": "^2.79.2", "rollup-plugin-terser": "^7.0.2", "run-applescript": "^7.0.0", "run-parallel": "^1.2.0", "rxjs": "^7.8.2", "safe-array-concat": "^1.1.3", "safe-buffer": "^5.2.1", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "safer-buffer": "^2.1.2", "sanitize-filename": "^1.6.3", "sanitize.css": "^13.0.0", "sass-loader": "^12.6.0", "sax": "^1.4.1", "saxes": "^5.0.1", "scheduler": "^0.26.0", "schema-utils": "^3.3.0", "select-hose": "^2.0.0", "selfsigned": "^2.4.1", "semver": "^6.3.1", "semver-compare": "^1.0.0", "send": "^0.19.0", "serialize-error": "^7.0.1", "serialize-javascript": "^6.0.2", "serve-index": "^1.9.1", "serve-static": "^1.16.2", "set-blocking": "^2.0.0", "set-function-length": "^1.2.2", "set-function-name": "^2.0.2", "set-proto": "^1.0.0", "setprototypeof": "^1.2.0", "shallow-clone": "^3.0.1", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "shell-quote": "^1.8.3", "side-channel": "^1.1.0", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2", "signal-exit": "^3.0.7", "simple-update-notifier": "^2.0.0", "sisteransi": "^1.0.5", "slash": "^3.0.0", "smart-buffer": "^4.2.0", "sockjs": "^0.3.24", "socks": "^2.8.5", "socks-proxy-agent": "^7.0.0", "source-list-map": "^2.0.1", "source-map": "^0.6.1", "source-map-js": "^1.2.1", "source-map-loader": "^3.0.2", "source-map-support": "^0.5.21", "sourcemap-codec": "^1.4.8", "spdy": "^4.0.2", "spdy-transport": "^3.0.0", "sprintf-js": "^1.1.3", "ssri": "^9.0.1", "stable": "^0.1.8", "stack-utils": "^2.0.6", "stackframe": "^1.3.4", "stat-mode": "^1.0.0", "static-eval": "^2.0.2", "statuses": "^2.0.1", "stop-iteration-iterator": "^1.1.0", "string_decoder": "^1.3.0", "string-length": "^4.0.2", "string-natural-compare": "^3.0.1", "string-width": "^4.2.3", "string-width-cjs": "^4.2.3", "string.prototype.includes": "^2.0.1", "string.prototype.matchall": "^4.0.12", "string.prototype.repeat": "^1.0.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "stringify-object": "^3.3.0", "strip-ansi": "^6.0.1", "strip-ansi-cjs": "^6.0.1", "strip-bom": "^4.0.0", "strip-comments": "^2.0.1", "strip-final-newline": "^2.0.0", "strip-json-comments": "^3.1.1", "style-loader": "^4.0.0", "stylehacks": "^5.1.1", "sucrase": "^3.35.0", "sumchecker": "^3.0.1", "supports-color": "^7.2.0", "supports-hyperlinks": "^2.3.0", "supports-preserve-symlinks-flag": "^1.0.0", "svg-parser": "^2.0.4", "svgo": "^1.3.2", "symbol-tree": "^3.2.4", "tailwindcss": "^3.4.17", "tapable": "^2.2.2", "tar": "^6.2.1", "temp-dir": "^2.0.0", "temp-file": "^3.4.0", "tempy": "^0.6.0", "terminal-link": "^2.1.1", "terser": "^5.43.1", "terser-webpack-plugin": "^5.3.14", "test-exclude": "^6.0.0", "text-table": "^0.2.0", "thenify": "^3.3.1", "thenify-all": "^1.6.0", "thingies": "^1.21.0", "throat": "^6.0.2", "thunky": "^1.1.0", "tiny-async-pool": "^1.3.0", "tmp": "^0.2.3", "tmp-promise": "^3.0.3", "tmpl": "^1.0.5", "to-regex-range": "^5.0.1", "toidentifier": "^1.0.1", "tough-cookie": "^4.1.4", "tr46": "^2.1.0", "tree-dump": "^1.0.3", "tree-kill": "^1.2.2", "truncate-utf8-bytes": "^1.0.2", "tryer": "^1.0.1", "ts-interface-checker": "^0.1.13", "tsconfig-paths": "^3.15.0", "tslib": "^2.8.1", "tsutils": "^3.21.0", "type-check": "^0.4.0", "type-detect": "^4.0.8", "type-fest": "^0.13.1", "type-is": "^1.6.18", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "typedarray-to-buffer": "^3.1.5", "typescript": "^5.8.3", "unbox-primitive": "^1.1.0", "underscore": "^1.12.1", "undici-types": "^7.8.0", "unicode-canonical-property-names-ecmascript": "^2.0.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.2.0", "unicode-property-aliases-ecmascript": "^2.1.0", "unique-filename": "^2.0.1", "unique-slug": "^3.0.0", "unique-string": "^2.0.0", "universalify": "^0.1.2", "unpipe": "^1.0.0", "unquote": "^1.1.1", "upath": "^1.2.0", "update-browserslist-db": "^1.1.3", "uri-js": "^4.4.1", "url-parse": "^1.5.10", "utf8-byte-length": "^1.0.5", "util-deprecate": "^1.0.2", "util.promisify": "^1.0.1", "utila": "^0.4.0", "utils-merge": "^1.0.1", "uuid": "^11.1.0", "v8-to-istanbul": "^8.1.1", "vary": "^1.1.2", "w3c-hr-time": "^1.0.2", "w3c-xmlserializer": "^2.0.0", "walker": "^1.0.8", "watchpack": "^2.4.4", "wbuf": "^1.7.3", "wcwidth": "^1.0.1", "webidl-conversions": "^6.1.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-middleware": "^7.4.2", "webpack-dev-server": "^5.2.2", "webpack-manifest-plugin": "^4.1.1", "webpack-merge": "^6.0.1", "webpack-sources": "^3.3.3", "websocket-driver": "^0.7.4", "websocket-extensions": "^0.1.4", "whatwg-encoding": "^1.0.5", "whatwg-fetch": "^3.6.20", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.7.0", "which": "^2.0.2", "which-boxed-primitive": "^1.1.1", "which-builtin-type": "^1.2.1", "which-collection": "^1.0.2", "which-module": "^2.0.1", "which-typed-array": "^1.1.19", "wildcard": "^2.0.1", "word-wrap": "^1.2.5", "workbox-background-sync": "^6.6.0", "workbox-broadcast-update": "^6.6.0", "workbox-build": "^6.6.0", "workbox-cacheable-response": "^6.6.0", "workbox-core": "^6.6.0", "workbox-expiration": "^6.6.0", "workbox-google-analytics": "^6.6.0", "workbox-navigation-preload": "^6.6.0", "workbox-precaching": "^6.6.0", "workbox-range-requests": "^6.6.0", "workbox-recipes": "^6.6.0", "workbox-routing": "^6.6.0", "workbox-strategies": "^6.6.0", "workbox-streams": "^6.6.0", "workbox-sw": "^6.6.0", "workbox-webpack-plugin": "^6.6.0", "workbox-window": "^6.6.0", "wrap-ansi": "^7.0.0", "wrap-ansi-cjs": "^7.0.0", "wrappy": "^1.0.2", "write-file-atomic": "^3.0.3", "ws": "^8.18.2", "xml-name-validator": "^3.0.0", "xmlbuilder": "^15.1.1", "xmlchars": "^2.2.0", "y18n": "^5.0.8", "yallist": "^4.0.0", "yaml": "^1.10.2", "yargs": "^17.7.2", "yargs-parser": "^21.1.1", "yauzl": "^2.10.0", "yocto-queue": "^0.1.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "react-scripts start", "build": "react-scripts build", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "build-electron": "npm run build && electron-builder", "build-win": "npm run build && electron-builder --win", "build-win-portable": "npm run build && electron-builder --win portable", "dist": "npm run build && electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": [], "author": "", "license": "ISC", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.clipsy.windows", "productName": "<PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["build/**/*", "electron.js", "preload.js", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "public/clipsy-logo-no-bg.png", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "<PERSON><PERSON><PERSON>"}, "portable": {"artifactName": "${productName}-${version}-portable-${arch}.${ext}"}, "extraResources": [{"from": "public/", "to": "public/", "filter": ["**/*"]}]}}